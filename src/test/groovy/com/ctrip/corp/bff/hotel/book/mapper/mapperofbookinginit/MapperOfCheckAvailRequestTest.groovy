package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.basebiz.iplocation.proto.GetIpInfoResponseTypeV2
import com.ctrip.basebiz.iplocation.proto.IpInfoEntityV2
import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckBaseEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.FeeTypes
import com.ctrip.corp.agg.hotel.roomavailable.entity.GuestInfoType
import com.ctrip.corp.agg.hotel.roomavailable.entity.ModifyReasonEnum
import com.ctrip.corp.agg.hotel.roomavailable.entity.OperationType
import com.ctrip.corp.agg.hotel.roomavailable.entity.UseCouponInfoType
import com.ctrip.corp.agg.hotel.roomavailable.entity.UserChangeCouponInfoType
import com.ctrip.corp.agg.hotel.tmc.entity.GetHotelTravelPolicyResponseType
import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig
import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfHotelTravelPolicy
import com.ctrip.corp.bff.framework.hotel.entity.contract.ArriveTimeInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.CouponDetailInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.CouponInfoInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ArriveTimeToken
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple7;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelGeoInfoResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.OrderResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.common.RcToken
import com.ctrip.corp.bff.framework.template.common.utils.CalendarUtil
import com.ctrip.corp.bff.framework.template.common.utils.date.DateUtil
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.PosEnum
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CertificateInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.NationalityInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.RCInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple6
import com.ctrip.corp.bff.hotel.book.common.enums.RcTypeEnum
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import com.ctrip.corp.bff.framework.template.common.utils.HostUtil
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import org.apache.commons.lang3.StringUtils
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/29
 */
class MapperOfCheckAvailRequestTest extends Specification {
    def mapper = new MapperOfCheckAvailRequest()

    def savePoint = new SavePoint()

    void setup() {
        MockitoAnnotations.openMocks(this)
    }

    void cleanup() {
        savePoint.rollback()
    }


    def "test getGuestInfoList"() {
        given:
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo)
        HotelBookPassengerInput passengerInput1 = new HotelBookPassengerInput(
                hotelPassengerInput: new HotelPassengerInput(employee: "T", uid: "uid1")
        )
        HotelBookPassengerInput passengerInput2 = new HotelBookPassengerInput(
                hotelPassengerInput: new HotelPassengerInput(employee: "F")
        )
        List<HotelBookPassengerInput> hotelPassengerInputs = [passengerInput1, passengerInput2]
        accountInfo.isTravelStandPolicy() >> true

        when:
        List<GuestInfoType> result = mapper.getGuestInfoList(accountInfo, hotelPassengerInputs)

        then:
        result != null
        result.size() == 2
        result[0].getUid() == "uid1"
        result[0].isEmployee() == true
        result[1].getUid() == null
        result[1].isEmployee() == false
    }

    def "getSelectedNationalityCode_1" () {
        expect:
        mapper.getSelectedNationalityCode(null, [new StrategyInfo(strategyKey: "CHECKED_MAS", strategyValue: "T")]) == "MY"
        mapper.getSelectedNationalityCode(null, [new StrategyInfo(strategyKey: "FIRST_NATION", strategyValue: "T")]) == "T"

    }

    def "test getSelectedNationalityCode"() {
        given:

        when: "hotelBookPassengerInputs is empty"
        def hotelBookPassengerInputs = []
        def result = mapper.getSelectedNationalityCode(hotelBookPassengerInputs, null)
        then:
        result == null

        when: "hotelBookPassengerInputs is null"
        hotelBookPassengerInputs = null
        result = mapper.getSelectedNationalityCode(hotelBookPassengerInputs, null)
        then:
        result == null

        when: "hotelBookPassengerInputs contains valid nationality codes"
        hotelBookPassengerInputs = [
                new HotelBookPassengerInput(nationalityInfo: new NationalityInfo(nationalityCode: "US")),
                new HotelBookPassengerInput(nationalityInfo: new NationalityInfo(nationalityCode: "CN")),
                new HotelBookPassengerInput(nationalityInfo: new NationalityInfo(nationalityCode: "JP"))
        ]
        result = mapper.getSelectedNationalityCode(hotelBookPassengerInputs, null)
        then:
        result == "US,CN,JP"

        when: "hotelBookPassengerInputs contains blank nationality codes"
        hotelBookPassengerInputs = [
                new HotelBookPassengerInput(nationalityInfo: new NationalityInfo(nationalityCode: "US")),
                new HotelBookPassengerInput(nationalityInfo: new NationalityInfo(nationalityCode: "")),
                new HotelBookPassengerInput(nationalityInfo: new NationalityInfo(nationalityCode: "JP"))
        ]
        result = mapper.getSelectedNationalityCode(hotelBookPassengerInputs, null)
        then:
        result == "US,JP"

        when: "all nationality codes are blank"
        hotelBookPassengerInputs = [
                new HotelBookPassengerInput(nationalityInfo: new NationalityInfo(nationalityCode: "")),
                new HotelBookPassengerInput(nationalityInfo: new NationalityInfo(nationalityCode: ""))
        ]
        result = mapper.getSelectedNationalityCode(hotelBookPassengerInputs, null)
        then:
        result == ""
    }

    def "test getModifyReasonEnumByRcCode"() {
        expect:
        mapper.getModifyReasonEnumByRcCode(code) == expected

        where:
        code           || expected
        null           || ModifyReasonEnum.CCANCEL
        "NOROOM"       || ModifyReasonEnum.NOROOM
        "HOUZHUIDB"    || ModifyReasonEnum.HOUZHUIDB
        "ALTERPRICE"   || ModifyReasonEnum.ALTERPRICE
        "NOTFULFIL"    || ModifyReasonEnum.NOTFULFIL
        "ONLINEMAN"    || ModifyReasonEnum.ONLINEMAN
        "XIANGAIYU"    || ModifyReasonEnum.XIANGAIYU
        "ONLINECHANGE" || ModifyReasonEnum.ONLINECHANGE
        "UNKNOWN"      || ModifyReasonEnum.CCANCEL
    }

    def "test getModifyReasonEnumOffline"() {
        given:
        def mapper = new MapperOfCheckAvailRequest()

        when: "rcInfos is null"
        def result = mapper.getModifyReasonEnumOffline(null)
        then:
        result == ModifyReasonEnum.CCANCEL

        when: "rcInfos is empty"
        result = mapper.getModifyReasonEnumOffline([])
        then:
        result == ModifyReasonEnum.CCANCEL

        when: "rcTokenStr is empty"
        RCInput rcInput = new RCInput()
        rcInput.setRcToken("")
        List<RCInput> rcInfos = [rcInput]
        result = mapper.getModifyReasonEnumOffline(rcInfos)
        then:
        result == ModifyReasonEnum.CCANCEL

        when: "rcTokenStr is valid and returns a known ModifyReasonEnum"
        rcInput.setRcToken("validToken")
        rcInfos = [rcInput]

        result = mapper.getModifyReasonEnumOffline(rcInfos)
        /*Mockito.mockStatic(TokenParseUtil.class)
        Mockito.when(TokenParseUtil.parseToken(any(String.class), RcToken.class)).thenReturn(null)*/
        then:
        result == ModifyReasonEnum.CCANCEL

        when: "rcTokenStr is valid but returns an unknown ModifyReasonEnum"
        /*Mockito.mockStatic(TokenParseUtil.class)
        Mockito.when(TokenParseUtil.parseToken("UNKNOWN", RcToken.class)).thenReturn(new RcToken(type: RcTypeEnum.OFFLINE_MODIFY.getCode(), code: "UNKNOWN"))*/
        result = mapper.getModifyReasonEnumOffline(rcInfos)
        then:
        result == ModifyReasonEnum.CCANCEL
    }

    @Unroll
    def "test getCheckOriOrderEntity"() {
        given:
        def mapper = new MapperOfCheckAvailRequest()
        def bookingInitRequestType = new BookingInitRequestType()
        def integrationSoaRequestType = new IntegrationSoaRequestType()
        integrationSoaRequestType.setSourceFrom(sourceFrom)
        bookingInitRequestType.setIntegrationSoaRequestType(integrationSoaRequestType)
        bookingInitRequestType.setStrategyInfos(strategyInfos)
        bookingInitRequestType.setRcInfos([])
        def resourceToken = new ResourceToken()
        def orderResourceToken = new OrderResourceToken()
        orderResourceToken.setOrderId(orderId)
        resourceToken.setOrderResourceToken(orderResourceToken)

        when:
        def result = mapper.setCheckOriOrderInfo(bookingInitRequestType, resourceToken)

        then:
        result.getModifyReason() == expectedModifyReason
        result.getOperationType() == expectedOperationType
        result.getOriOrderId() == orderId

        where:
        sourceFrom         | strategyInfos                                       | orderId | expectedModifyReason          | expectedOperationType
        SourceFrom.Offline | [new StrategyInfo("BOOKING_SCENARIO", "MODIFY")]    | 456L    | ModifyReasonEnum.CCANCEL      | OperationType.CHANGEORDER
        SourceFrom.Offline | [new StrategyInfo("BOOKING_SCENARIO", "CopyOrder")] | 789L    | ModifyReasonEnum.ONLINECHANGE | OperationType.COPYORDER
    }

    def "test setBookScenarioInfo"() {
        given:
        def mapper = new MapperOfCheckAvailRequest()
        def bookingInitRequestType = new BookingInitRequestType()
        def policyInput = new PolicyInput()
        bookingInitRequestType.setPolicyInput(policyInput)
        bookingInitRequestType.strategyInfos = new ArrayList<>()
        bookingInitRequestType.strategyInfos.add(strategyInfo)

        when:
        def result = mapper.setBookScenarioInfo(bookingInitRequestType)

        then:
        result.isModifyOrCorpOrder() == expectedModifyOrCorpOrder
        result.isBookingWithPersonalAccount() == expectedBookingWithPersonalAccount

        where:
        strategyInfo                                                                       | expectedModifyOrCorpOrder | expectedBookingWithPersonalAccount
        new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "Modify")         | true                      | false
        new StrategyInfo(strategyKey: "BOOKING_WITH_PERSONAL_ACCOUNT", strategyValue: "T") | false                     | true
    }

    def "test setCheckAvailExtInfo"() {
        given:
        def resourceToken = new ResourceToken()
        def hotelResourceToken = new HotelResourceToken()
        hotelResourceToken.setSearchHotelTraceId(searchHotelTraceId)
        def roomResourceToken = new RoomResourceToken()
        roomResourceToken.setRatePlanTraceLogId(ratePlanTraceLogId)
        roomResourceToken.setpId(pid)
        roomResourceToken.setProductToken(productToken)
        resourceToken.setHotelResourceToken(hotelResourceToken)
        resourceToken.setRoomResourceToken(roomResourceToken)

        when:
        def result = mapper.setCheckAvailExtInfo(resourceToken)

        then:
        result.getRatePlanTraceLogId() == ratePlanTraceLogId
        result.getSearchHotelTraceId() == searchHotelTraceId
        result.getPid() == pid
        result.getProductTokenList().size() == expectedProductTokenListSize
        result.getProductTokenList().get(0).getXProductType() == expectedXProductType
        result.getProductTokenList().get(0).getXProductToken() == productToken

        where:
        ratePlanTraceLogId | searchHotelTraceId | pid  | productToken | expectedProductTokenListSize | expectedXProductType
        "traceLogId1"      | "hotelTraceId1"    | "p1" | "token1"     | 1                            | 1
        "traceLogId2"      | "hotelTraceId2"    | "p2" | "null"       | 1                            | 1
    }


    def "test getMultiCoupon with #description"() {
        given:
        MapperOfCheckAvailRequest mapper = new MapperOfCheckAvailRequest()
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType()
        bookingInitRequestType.setStrategyInfos(Arrays.asList(new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "T")))

        when:
        UseCouponInfoType result = mapper.getMultiCoupon(bookingInitRequestType)

        then:
        !result.allCouponCanceled



        when:
        bookingInitRequestType.setStrategyInfos(Arrays.asList(new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "F")))
        result = mapper.getMultiCoupon(bookingInitRequestType)

        then:
        result == null
    }


    def "check returns valid ParamCheckResult for valid input"() {
        given:
        def bookingInitRequestType = Mock(BookingInitRequestType)
        def accountInfo = Mock(WrapperOfAccount.AccountInfo)
        def tuple = Tuple7.of(bookingInitRequestType, null, accountInfo, null, null, null, null)

        when:
        def result = mapper.check(tuple)

        then:
        result == null
    }

    def "setCheckAvailBaseInfo returns null when integrationSoaRequestType is null"() {
        given:
        def bookingInitRequestType = Mock(BookingInitRequestType) {
            getIntegrationSoaRequestType() >> null
        }

        when:
        def result = mapper.setCheckAvailBaseInfo(bookingInitRequestType, null, null, null, null, null)

        then:
        result == null
    }

    def "getGuestInfoList returns null when accountInfo is not travel stand policy"() {
        given:
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isTravelStandPolicy() >> false
        }

        when:
        def result = mapper.getGuestInfoList(accountInfo, [])

        then:
        result == null
    }

    def "getGuestInfoList returns valid list for valid input"() {
        given:
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isTravelStandPolicy() >> true
        }
        def passengerInput = Mock(HotelBookPassengerInput)

        when:
        def result = mapper.getGuestInfoList(accountInfo, [passengerInput])

        then:
        result != null
    }

    def "getSelectedNationalityCode returns null for empty input"() {
        when:
        def result = mapper.getSelectedNationalityCode([], null)

        then:
        result == null
    }

    def "getSelectedNationalityCode returns concatenated codes for valid input"() {
        given:
        def passengerInput1 = Mock(HotelBookPassengerInput) {
            getNationalityInfo() >> Mock(NationalityInfo) {
                getNationalityCode() >> "US"
            }
        }
        def passengerInput2 = Mock(HotelBookPassengerInput) {
            getNationalityInfo() >> Mock(NationalityInfo) {
                getNationalityCode() >> "CN"
            }
        }

        when:
        def result = mapper.getSelectedNationalityCode([passengerInput1, passengerInput2], null)

        then:
        result == "US,CN"
    }

    def "setCheckAvailRoomInfo returns null when resourceToken is null"() {
        when:
        def result = mapper.setCheckAvailRoomInfo(null, null, null, null, null)

        then:
        result == null
    }


    def "setCheckAvailExtInfo returns null when roomResourceToken is null"() {
        given:
        def resourceToken = Mock(ResourceToken) {
            getRoomResourceToken() >> null
        }

        when:
        def result = mapper.setCheckAvailExtInfo(resourceToken)

        then:
        result == null
    }

    def "setCheckAvailExtInfo returns valid CheckExtEntity for valid input"() {
        given:
        def resourceToken = Mock(ResourceToken) {
            getRoomResourceToken() >> Mock(RoomResourceToken)
            getHotelResourceToken() >> Mock(HotelResourceToken)
        }

        when:
        def result = mapper.setCheckAvailExtInfo(resourceToken)

        then:
        result != null
    }

    def "setBookScenarioInfo returns valid CheckBookingScenarioEntity for valid input"() {
        given:
        def bookingInitRequestType = Mock(BookingInitRequestType)

        when:
        def result = mapper.setBookScenarioInfo(bookingInitRequestType)

        then:
        result != null
    }

    def "setCheckOriOrderInfo returns null when orderId is null"() {
        given:
        def resourceToken = Mock(ResourceToken) {
            getOrderResourceToken() >> Mock(OrderResourceToken) {
                getOrderId() >> null
            }
        }

        when:
        def result = mapper.setCheckOriOrderInfo(Mock(BookingInitRequestType), resourceToken)

        then:
        result == null
    }

    @Unroll
    def "getCertificateNationality"() {
        given:
        def hotelBookPassengerInput = new HotelBookPassengerInput(
                certificateInfos: [
                        new CertificateInfo(
                                certificateType: "OTHERDOCUMENT",
                                nationalityInfo: new NationalityInfo(
                                        nationalityCode: "MY"
                                )
                        )
                ]
        )
        when:
        def result = mapper.getCertificateNationality(hotelBookPassengerInput)
        then:
        result == "MY"
    }

    def "getMultiCoupon" () {
        expect:
        mapper.getMultiCoupon(new BookingInitRequestType(strategyInfos: [new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "T")])).allCouponCanceled == false
        mapper.getMultiCoupon(new BookingInitRequestType(strategyInfos: [new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "F")])) == null
        mapper.getMultiCoupon(new BookingInitRequestType(couponInfoInput: new CouponInfoInput(useCoupon: "T"),strategyInfos: [new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "F")])) .allCouponCanceled == false
    }


    @Unroll
    def "testBuildCheckAvailCompare with #description"() {
        given: "Mocked dependencies"
        def mapper = new MapperOfCheckAvailRequest()
        def bookingInitRequestType = new BookingInitRequestType(hotelBookPassengerInputs: [new HotelBookPassengerInput(), new HotelBookPassengerInput()])
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isTravelStandPolicy() >> true
            isPolicyModel() >> true
            isPackageEnabled() >> true
            isOaApprovalHead() >> true
        }

        when: "Calling buildCheckAvailCompare"
        def result = mapper.buildCheckAvailCompare(7, 3, bookingInitRequestType, accountInfo, [new HotelBookPassengerInput(), new HotelBookPassengerInput()])

        then: "The result should match the expected value"
        result.policyModel
        result.getAdultQuantity() == 7
        result.travelStandPolicy
        result.roomQuantity == 3
        result.packageEnabled
        result.hotelBookPassengerInputsSize == 2
    }

    @Unroll
    def "buildGuestPerson"() {
        given: "Mocked dependencies"
        def mapper = Spy(MapperOfCheckAvailRequest)
        mapper.buildCheckAvailCompare(_, _, _, _) >> new MapperOfCheckAvailRequest.CheckAvailCompare()
        def bookingInitRequestType = new BookingInitRequestType(hotelBookInputOfQuantity: null, hotelBookInput: new HotelBookInput(adultQuantity: 3))
        def accountInfo = Mock(WrapperOfAccount.AccountInfo)

        when: "Calling buildGuestPerson"
        def result = mapper.buildGuestPerson(bookingInitRequestType, accountInfo, null, null)

        then: "The result should match the expected value"
        result == 3
    }

    @Unroll
    def "buildGuestPerson-new"() {
        given: "Mocked dependencies"
        def mapper = Spy(MapperOfCheckAvailRequest)
        mapper.buildCheckAvailCompare(_, _, _, _) >> new MapperOfCheckAvailRequest.CheckAvailCompare()
        def bookingInitRequestType = new BookingInitRequestType(hotelBookInputOfQuantity: new HotelBookInput(), hotelBookInput: new HotelBookInput(adultQuantity: 3))
        def accountInfo = Mock(WrapperOfAccount.AccountInfo)

        when: "Calling buildGuestPerson"
        def result = mapper.buildGuestPerson(bookingInitRequestType, accountInfo, null, null)

        then: "The result should match the expected value"
        result == 3
    }


    @Unroll
    def "buildAdultQuantityNew-isTravelStandPolicy"() {
        given: "Mocked dependencies"
        def mapper = Spy(MapperOfCheckAvailRequest)
        mapper.buildCheckAvailCompare(_, _, _, _) >> new MapperOfCheckAvailRequest.CheckAvailCompare()
        def bookingInitRequestType = new BookingInitRequestType(
                hotelBookPassengerInputs: [new HotelBookPassengerInput(), new HotelBookPassengerInput(), new HotelBookPassengerInput(), new HotelBookPassengerInput(), new HotelBookPassengerInput()],
                hotelBookInputOfQuantity: new HotelBookInput(adultQuantity: 7, roomQuantity: roomQuantity),
                hotelBookInput: new HotelBookInput(adultQuantity: 3, roomQuantity: 2),
                corpPayInfo: new CorpPayInfo(corpPayType: "public")
        )
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isTravelStandPolicy() >> true
        }

        expect: "Calling buildGuestPerson"
        result == mapper.buildAdultQuantityNew(bookingInitRequestType, accountInfo, [new HotelBookPassengerInput(), new HotelBookPassengerInput(), new HotelBookPassengerInput(), new HotelBookPassengerInput(), new HotelBookPassengerInput()])

        where: "The result should match the expected value"
        hotelBookPassengerInputs                                                                                                                                    | roomQuantity || result
        [new HotelBookPassengerInput(), new HotelBookPassengerInput(), new HotelBookPassengerInput(), new HotelBookPassengerInput(), new HotelBookPassengerInput()] | 8            || 8
        [new HotelBookPassengerInput(), new HotelBookPassengerInput(), new HotelBookPassengerInput(), new HotelBookPassengerInput(), new HotelBookPassengerInput()] | 4            || 5
    }

    @Unroll
    def "buildAdultQuantityNew"() {
        given: "Mocked dependencies"
        def mapper = Spy(MapperOfCheckAvailRequest)
        mapper.buildCheckAvailCompare(_, _, _, _) >> new MapperOfCheckAvailRequest.CheckAvailCompare()
        def bookingInitRequestType = new BookingInitRequestType(
                hotelBookPassengerInputs: [new HotelBookPassengerInput(), new HotelBookPassengerInput(), new HotelBookPassengerInput(), new HotelBookPassengerInput(), new HotelBookPassengerInput()],
                hotelBookInputOfQuantity: new HotelBookInput(adultQuantity: adultQuantity, roomQuantity: roomQuantity),
                hotelBookInput: new HotelBookInput(adultQuantity: 3, roomQuantity: 2),
                corpPayInfo: new CorpPayInfo(corpPayType: "public")
        )
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isTravelStandPolicy() >> false
        }

        expect: "Calling buildGuestPerson"
        result == mapper.buildAdultQuantityNew(bookingInitRequestType, accountInfo, [new HotelBookPassengerInput(), new HotelBookPassengerInput(), new HotelBookPassengerInput(), new HotelBookPassengerInput(), new HotelBookPassengerInput()])

        where: "The result should match the expected value"
        adultQuantity | roomQuantity || result
        3             | 8            || 8
        4             | 3            || 4
    }

    @Unroll
    def "getSelectedNationalityCode"() {
        given:
        def result
        when:
        def strategyInfos = [new StrategyInfo("USE_CERTIFICATE_NATIONALITY", "T")]
        def hotelBookPassengerInputs = [
                new HotelBookPassengerInput(
                certificateInfos: [
                        new CertificateInfo(
                                certificateType: "OTHERDOCUMENT",
                                nationalityInfo: new NationalityInfo(
                                        nationalityCode: "MY"
                                )
                        )
                ]),
                new HotelBookPassengerInput(
                        nationalityInfo: new NationalityInfo(nationalityCode: "CN")
                )
        ]
        result = mapper.getSelectedNationalityCode(hotelBookPassengerInputs, strategyInfos)
        then:
        result == "MY,CN"
    }
}
