package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.agg.hotel.salestrategy.entity.CorpXGuestInfoType
import com.ctrip.corp.agg.hotel.salestrategy.entity.CorpXProductInfoType
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceDetailInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.CorpXProductInfoToken
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import org.mockito.MockitoAnnotations
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2025/4/24 15:22
 *
 */
class MapperOfCalculateTravelRewardsRequestTest extends Specification {
    def mapper = new MapperOfCalculateTravelRewardsRequest()

    def savePoint = new SavePoint()

    void setup() {
        MockitoAnnotations.openMocks(this)
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "testBuildCorpXProductInfoType with non-empty insuranceHotelBookPassengerInputs"() {
        given: "A HotelInsuranceDetailInput with non-empty insuranceHotelBookPassengerInputs"
        new MockUp<LogUtil>() {
            @Mock
            public static void loggingClogOnly(LogLevelEnum level,
                                               Class<?> type,
                                               String title,
                                               String message,
                                               Map<String, String> indexTags) {
                return
            }
        }
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
                hotelInsuranceInput: new HotelInsuranceInput(
                        hotelInsuranceDetailInputs: [new HotelInsuranceDetailInput(
                                insuranceToken: "H4sIAAAAAAAAAOMSMrS0NDEwsrQwNzW1NLYwN7Q0E2CUYFLi8/APcfXx9AsODXL0c3bVEuZoWPKCWYBBgkeJw0LPAAyMxJ4uan7a1vpseuuLhT0vZ64syCktft7ZYcXs7BfpwO4lbGgEVmZgaBJvamhqYmlkYBSEQ08Um4mphbmFWQJTBmMBMwAAReeQlwAAAA==",
                                insuranceHotelBookPassengerInputs: [new HotelBookPassengerInput(
                                        hotelPassengerInput: new HotelPassengerInput(
                                                uid: "123",
                                                employee: "T",
                                                roomIndex: 1
                                        )
                                )]
                        )])
        )
        when: "Calling buildCorpXProductInfoType"
        def result = mapper.buildCorpXProductInfoType(bookingInitRequestType)

        then: "The result should be a valid CorpXProductInfoType"
        result.get(0).priceMark == "199402987559387196"
        result.get(0).ownerType == "PERSON"
        result.get(0).guestList.size() == 1
        result.get(0).guestList[0].uid == "123"
        result.get(0).guestList[0].employee
        result.get(0).guestList[0].roomIndex == 1
    }

    def "getGuestIngetfoList" () {
        expect:
        new MapperOfCalculateTravelRewardsRequest().getGuestIngetfoList(new BookingInitRequestType(hotelBookPassengerInputs: [new HotelBookPassengerInput(
                hotelPassengerInput: new HotelPassengerInput(
                        uid: "",
                        infoId: "a",
                        employee: "T",
                        roomIndex: 1
                )
        )]),  [new HotelBookPassengerInput(
                hotelPassengerInput: new HotelPassengerInput(
                        uid: "",
                        infoId: "a",
                        employee: "T",
                        roomIndex: 1
                )
        )])*.uid == ["a"]
        new MapperOfCalculateTravelRewardsRequest().getGuestIngetfoList(new BookingInitRequestType(hotelBookPassengerInputs: [new HotelBookPassengerInput(
                hotelPassengerInput: new HotelPassengerInput(
                        uid: "b",
                        infoId: "a",
                        employee: "T",
                        roomIndex: 1
                )
        )]),  [new HotelBookPassengerInput(
                hotelPassengerInput: new HotelPassengerInput(
                        uid: "b",
                        infoId: "a",
                        employee: "T",
                        roomIndex: 1
                )
        )])*.uid == ["b"]
    }

}
