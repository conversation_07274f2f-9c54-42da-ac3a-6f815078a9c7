package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelItem
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelRatePlan
import com.ctrip.corp.agg.hotel.roomavailable.entity.SalePromotionEntity
import com.ctrip.corp.hotel.book.query.entity.GetHotelDetailInfoResponseType
import com.ctrip.corp.hotel.book.query.entity.HotelBrandEntity;

import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomItem;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple8;

import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo

import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodResponseType
import com.ctrip.corp.order.data.aggregation.query.contract.HotelInfoType
import com.ctrip.corp.order.data.aggregation.query.contract.HotelOrderType
import com.ctrip.corp.order.data.aggregation.query.contract.OrderBasicInfoType
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType
import com.ctrip.corp.order.data.aggregation.query.contract.ServiceFeeConfigType
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.QueryOrderSettingsResponseType
import mockit.internal.state.SavePoint
import spock.lang.Specification


/**
 * <AUTHOR>
 * @Date 2024/12/10 16:00
 */
class MapperOfCalculateServiceChargeV2RequestTypeTest extends Specification {

    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }
    def "testBuildOrderAmsFeeConfigVersion"() {
        given:
        QueryHotelOrderDataResponseType queryHotelOrderDataResponseType = new QueryHotelOrderDataResponseType()
        queryHotelOrderDataResponseType.setHotelInfo(new HotelInfoType(hotelOrder: new HotelOrderType(serviceFeeConfig: new ServiceFeeConfigType(amsFeeConfigVersion: "12"))))
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(strategyInfos: strategyInfos)
        MapperOfCalculateServiceChargeV2RequestType mapperOfCalculateServiceChargeV2RequestType = new MapperOfCalculateServiceChargeV2RequestType()
        expect:
        mapperOfCalculateServiceChargeV2RequestType.buildOrderAmsFeeConfigVersion(queryHotelOrderDataResponseType, bookingInitRequestType) == result
        where:
        strategyInfos                                                                      || result
        [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "")]             || null
        [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "MODIFY")]       || "12"
        [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "CopyOrder")]    || null
        [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "EXTEND")]       || null
        [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "APPLY_MODIFY")] || "12"
    }
    def "testBuildOrderAmsFeeConfigVersion1"() {
        given:
        QueryHotelOrderDataResponseType queryHotelOrderDataResponseType = new QueryHotelOrderDataResponseType()
        queryHotelOrderDataResponseType.setHotelInfo(new HotelInfoType(hotelOrder: new HotelOrderType(serviceFeeConfig: new ServiceFeeConfigType(amsFeeConfigVersion: "12"))))
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(strategyInfos: strategyInfos)
        MapperOfGetRoomChargePolicyListRequestType mapperOfGetRoomChargePolicyListRequestType = new MapperOfGetRoomChargePolicyListRequestType()
        expect:
        mapperOfGetRoomChargePolicyListRequestType.buildOrderAmsFeeConfigVersion(queryHotelOrderDataResponseType, bookingInitRequestType) == result
        where:
        strategyInfos                                                                      || result
        [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "")]             || null
        [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "MODIFY")]       || "12"
        [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "CopyOrder")]    || null
        [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "EXTEND")]       || null
        [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "APPLY_MODIFY")] || "12"
    }


    def "getOrderAmountInfoType" () {
        expect:
        new MapperOfCalculateServiceChargeV2RequestType().getOrderAmountInfoType(null, null) == null
        new MapperOfCalculateServiceChargeV2RequestType().getOrderAmountInfoType(new QueryHotelOrderDataResponseType(), new BookingInitRequestType()) == null
        new MapperOfCalculateServiceChargeV2RequestType().getOrderAmountInfoType(new QueryHotelOrderDataResponseType(orderBasicInfo: new OrderBasicInfoType(orderId: 2)), new BookingInitRequestType(strategyInfos: [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "MODIFY")])) .orderId == 2
    }

    def "getBaseInfoType" () {
        expect:
        new MapperOfCalculateServiceChargeV2RequestType().getBaseInfoType(Mock(WrapperOfAccount.AccountInfo), new BookingInitRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(),requestId: "a"))).traceId == "a"
    }

    def "convert" () {
        given:
        def tester = Spy(MapperOfCalculateServiceChargeV2RequestType)
        def input = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        input.getHotelItem() >> new HotelItem()
        def hotelBrand = new GetHotelDetailInfoResponseType();
        expect:
        tester.convert(Tuple8.of(Mock(WrapperOfAccount.AccountInfo), new QueryHotelOrderDataResponseType(), new QueryOrderSettingsResponseType(), input, new BookingInitRequestType(hotelBookInput: new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo(checkIn: "2025-01-01", checkOut: "2025-01-02")),corpPayInfo: new CorpPayInfo("private"),integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(), sourceFrom: SourceFrom.Offline),strategyInfos: [new StrategyInfo(strategyKey: "BLUE_SPACE_PRE_CHARGE_SERVICE", strategyValue: "T")]), new ResourceToken(), new GetSupportedPaymentMethodResponseType(),hotelBrand)).chargeMomentFilter == "PreCharge"

    }

    def "testBuildHotelInfoTypeWithHotelBrandInfo"() {
        given:
        def hotelItem = new HotelItem()
        def checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        checkAvailInfo.getHotelItem() >> hotelItem

        hotelItem.setCountry(1)
        hotelItem.setProvince(2)
        hotelItem.setCity(3)

        def getHotelDetailInfoResponseType = new com.ctrip.corp.hotel.book.query.entity.GetHotelDetailInfoResponseType()
        def hotelDetailInfoEntity = new com.ctrip.corp.hotel.book.query.entity.HotelDetailInfoEntity()
        def detailBaseInfoEntity = new com.ctrip.corp.hotel.book.query.entity.DetailBaseInfoEntity()
        def hotelBrandEntity = new HotelBrandEntity()
        hotelBrandEntity.setGroupId(100)
        hotelBrandEntity.setBrandId(200)
        detailBaseInfoEntity.setHotelBrandInfo(hotelBrandEntity)
        hotelDetailInfoEntity.setHotelBaseInfo(detailBaseInfoEntity)
        getHotelDetailInfoResponseType.setHotelDetailInfo(hotelDetailInfoEntity)

        when:
        def result = MapperOfCalculateServiceChargeV2RequestType.buildHotelInfoType(checkAvailInfo, getHotelDetailInfoResponseType)

        then:
        result != null
        result.countryId == 1
        result.provinceId == 2
        result.cityId == 3
        result.hotelGroupId == 100
        result.hotelBrandId == 200
    }


}

