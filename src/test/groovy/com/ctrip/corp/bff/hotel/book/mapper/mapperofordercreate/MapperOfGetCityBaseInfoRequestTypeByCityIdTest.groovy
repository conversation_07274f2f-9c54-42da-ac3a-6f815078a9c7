package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoRequestType
import com.ctrip.corp.hotel.book.query.entity.QueryBaseEntity
import com.ctrip.corp.hotel.book.query.entity.UserInfoEntity
import mockit.internal.state.SavePoint
import spock.lang.Specification

class MapperOfGetCityBaseInfoRequestTypeByCityIdTest extends Specification {
    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "test convert with typical input"() {
        given: "准备输入参数"
        def integrationSoaRequestType = Mock(IntegrationSoaRequestType)
        def userInfo = Mock(UserInfo)
        integrationSoaRequestType.getLanguage() >> "zh_CN"
        integrationSoaRequestType.getRequestId() >> "trace123"
        integrationSoaRequestType.getSourceFrom() >> SourceFrom.H5
        integrationSoaRequestType.getUserInfo() >> userInfo
        userInfo.getCorpId() >> "12345"
        userInfo.getUserId() >> "67890"

        def cityId = 100
        def tuple = Tuple2.of(integrationSoaRequestType, cityId)
        def mapper = new MapperOfGetCityBaseInfoRequestTypeByCityId()

        when: "调用convert"
        GetCityBaseInfoRequestType result = mapper.convert(tuple)

        then: "断言结果"
        result != null
        result.cityIdList == [cityId]
        result.baseInfo != null
        result.baseInfo instanceof QueryBaseEntity
        result.baseInfo.locale == "zh_CN"
        result.baseInfo.traceId == "trace123"
        result.baseInfo.requestFrom == "H5"
        result.baseInfo.userInfo != null
        result.baseInfo.userInfo instanceof UserInfoEntity
        result.baseInfo.userInfo.corpId == "12345"
        result.baseInfo.userInfo.uid == "67890"
    }

    def "test convert with another SourceFrom and different locale"() {
        given:
        def integrationSoaRequestType = Mock(IntegrationSoaRequestType)
        def userInfo = Mock(UserInfo)
        integrationSoaRequestType.getLanguage() >> "en_US"
        integrationSoaRequestType.getRequestId() >> "trace456"
        integrationSoaRequestType.getSourceFrom() >> SourceFrom.Online
        integrationSoaRequestType.getUserInfo() >> userInfo
        userInfo.getCorpId() >> "54321"
        userInfo.getUserId() >> "09876"

        def cityId = 200
        def tuple = Tuple2.of(integrationSoaRequestType, cityId)
        def mapper = new MapperOfGetCityBaseInfoRequestTypeByCityId()

        when:
        GetCityBaseInfoRequestType result = mapper.convert(tuple)

        then:
        result != null
        result.cityIdList == [cityId]
        result.baseInfo != null
        result.baseInfo.locale == "en_US"
        result.baseInfo.traceId == "trace456"
        result.baseInfo.requestFrom == "Online"
        result.baseInfo.userInfo.corpId == "54321"
        result.baseInfo.userInfo.uid == "09876"
    }

    def "test check method returns null for valid input"() {
        given: "准备有效的输入参数"
        def integrationSoaRequestType = Mock(IntegrationSoaRequestType)
        def userInfo = Mock(UserInfo)
        integrationSoaRequestType.getLanguage() >> "zh_CN"
        integrationSoaRequestType.getRequestId() >> "trace123"
        integrationSoaRequestType.getSourceFrom() >> SourceFrom.H5
        integrationSoaRequestType.getUserInfo() >> userInfo
        userInfo.getCorpId() >> "12345"
        userInfo.getUserId() >> "67890"

        def cityId = 100
        def tuple = Tuple2.of(integrationSoaRequestType, cityId)
        def mapper = new MapperOfGetCityBaseInfoRequestTypeByCityId()

        when: "调用check方法"
        def result = mapper.check(tuple)

        then: "应该返回null，表示不进行参数校验"
        result == null
    }

    def "test check method returns null for null IntegrationSoaRequestType"() {
        given: "IntegrationSoaRequestType为null"
        def tuple = Tuple2.of(null, 100)
        def mapper = new MapperOfGetCityBaseInfoRequestTypeByCityId()

        when: "调用check方法"
        def result = mapper.check(tuple)

        then: "应该返回null"
        result == null
    }

    def "test check method returns null for null cityId"() {
        given: "cityId为null"
        def integrationSoaRequestType = Mock(IntegrationSoaRequestType)
        def tuple = Tuple2.of(integrationSoaRequestType, null)
        def mapper = new MapperOfGetCityBaseInfoRequestTypeByCityId()

        when: "调用check方法"
        def result = mapper.check(tuple)

        then: "应该返回null"
        result == null
    }

    def "test check method returns null for both null parameters"() {
        given: "两个参数都为null"
        def tuple = Tuple2.of(null, null)
        def mapper = new MapperOfGetCityBaseInfoRequestTypeByCityId()

        when: "调用check方法"
        def result = mapper.check(tuple)

        then: "应该返回null"
        result == null
    }
}