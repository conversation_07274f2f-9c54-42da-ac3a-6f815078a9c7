package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import corp.user.service.corpUserInfoService.GetCorpUserInfoRequestType;

class MapperOfGetCorpUserInfoRequestTypeByPolicyIdTest extends Specification {
    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }
    def mapper = new MapperOfGetCorpUserInfoRequestTypeByPolicyId()

    def "convert方法应正确设置uid"() {
        given:
        def policyId = "test-policy-id"
        def tuple = Tuple1.of(policyId)

        when:
        GetCorpUserInfoRequestType result = mapper.convert(tuple)

        then:
        result != null
        result.getUid() == policyId
    }

    def "check方法应返回null"() {
        given:
        def tuple = Tuple1.of("any-policy-id")

        when:
        def result = mapper.check(tuple)

        then:
        result == null
    }
}
