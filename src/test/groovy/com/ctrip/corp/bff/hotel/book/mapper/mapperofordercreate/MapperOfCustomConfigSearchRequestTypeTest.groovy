package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2025/6/16 15:50
 *
 */
class MapperOfCustomConfigSearchRequestTypeTest extends Specification {
    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "testConvert with valid IntegrationSoaRequestType"() {
        given: "A valid IntegrationSoaRequestType with UserInfo"
        def integrationSoaRequestType = Mock(IntegrationSoaRequestType) {
            getUserInfo() >> Mock(UserInfo) {
                getCorpId() >> "corp123"
                getUserId() >> "user456"
            }
        }
        def mapper = new MapperOfCustomConfigSearchRequestType()

        when: "Calling convert with valid IntegrationSoaRequestType"
        def result = mapper.convert(Tuple1.of(integrationSoaRequestType) as Tuple1<IntegrationSoaRequestType>)

        then: "The result should match the expected CustomConfigSearchRequestType"
        result != null
        result.getCorpId() == "corp123"
        result.getUId() == "user456"
    }
}
