package com.ctrip.corp.bff.hotel.book.common.wrapper

import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckAvailResponseType
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelRatePlan
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomItem
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelItem
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig
import com.ctrip.corp.bff.framework.hotel.common.util.WaitFutureUtil
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.entity.ActionInfo
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.MapString
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil
import com.ctrip.corp.bff.hotel.book.qconfig.QConfigOfCodeMappingConfig
import com.ctrip.corp.bff.hotel.book.qconfig.entity.CodeMappingConfig
import com.ctrip.corp.bff.hotel.book.sharkmock.SharkMockUtil
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2024/11/5 16:13
 *
 */
class WrapperOfCheckAvailTest extends Specification {

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }
    @Unroll
    def "buildActionInfo"() {
        given:
        def qConfigOfCodeMappingConfig = new QConfigOfCodeMappingConfig()
        qConfigOfCodeMappingConfig.setCodeMappingEntities([new CodeMappingConfig(
                actionName: "corphotelroomavailableserviceclient.checkavail",
                mappingActionRelations: ["50119": "recommendRoom_reloadRoomList"],
        )])
        expect:
        acitonInfo == WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(new CheckAvailResponseType(responseCode: responseCode, hotelRatePlan: new HotelRatePlan(roomInfo: new RoomItem())))
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken(balanceType: "PP")))
                .buildActionInfo(responseCode, qConfigOfCodeMappingConfig, false, new HashMap<>());
        where:
        responseCode || acitonInfo
        20000        || null
        50119        || new ActionInfo(actionType: "recommendRoom_reloadRoomList", dataExtInfo: [new MapString(key: "checkAvailResponseCode", value: "50119"),
                                                                                                 new MapString(key: "supportActionTypeNew", value: "F")])
        99999        || null
    }

    @Unroll
    def "getStar should return correct star value"() {
        given:
        def hotelItem = new HotelItem(star: expectedStar)
        def roomItem = new RoomItem(balanceType: "PP")
        def hotelRatePlan = new HotelRatePlan(hotelInfo: hotelItem, roomInfo: roomItem)
        def checkAvailResponseType = new CheckAvailResponseType(hotelRatePlan: hotelRatePlan)
        def resourceToken = new ResourceToken(roomResourceToken: new RoomResourceToken(balanceType: "PP"))
        
        when:
        def wrapperOfCheckAvail = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(resourceToken)
                .build()
        def checkAvailInfo = wrapperOfCheckAvail.getCheckAvailInfo()
        def actualStar = checkAvailInfo.getStar()
        
        then:
        actualStar == expectedStar
        
        where:
        expectedStar << [0, 1, 2, 3, 4, 5, 10, 100]
    }

    def "getStar should return 0 when hotelInfo is null"() {
        given:
        def roomItem = new RoomItem(balanceType: "PP")
        def hotelRatePlan = new HotelRatePlan(hotelInfo: null, roomInfo: roomItem)
        def checkAvailResponseType = new CheckAvailResponseType(hotelRatePlan: hotelRatePlan)
        def resourceToken = new ResourceToken(roomResourceToken: new RoomResourceToken(balanceType: "PP"))
        
        when:
        def wrapperOfCheckAvail = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(resourceToken)
                .build()
        def checkAvailInfo = wrapperOfCheckAvail.getCheckAvailInfo()
        def actualStar = checkAvailInfo.getStar()
        
        then:
        actualStar == 0
    }

    def "getStar should return 0 when hotelInfo.star is null"() {
        given:
        def hotelItem = new HotelItem(star: null)
        def roomItem = new RoomItem(balanceType: "PP")
        def hotelRatePlan = new HotelRatePlan(hotelInfo: hotelItem, roomInfo: roomItem)
        def checkAvailResponseType = new CheckAvailResponseType(hotelRatePlan: hotelRatePlan)
        def resourceToken = new ResourceToken(roomResourceToken: new RoomResourceToken(balanceType: "PP"))
        
        when:
        def wrapperOfCheckAvail = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(resourceToken)
                .build()
        def checkAvailInfo = wrapperOfCheckAvail.getCheckAvailInfo()
        def actualStar = checkAvailInfo.getStar()
        
        then:
        actualStar == 0
    }


    @Unroll
    def "testBuildCodeMappingConfig with #description"() {
        given: "Mocked dependencies"
        def qConfigOfCodeMappingConfig = Mock(QConfigOfCodeMappingConfig)
        def strategyInfoMapInput = Mock(Map)

        and: "Stubbed behavior for QConfigOfCodeMappingConfig"
        qConfigOfCodeMappingConfig.getCodeMappingConfig(_) >> { String key ->
            switch (key) {
                case "corphotelroomavailableserviceclient.checkavail":
                    return new CodeMappingConfig(mappingActionRelations: ["key1": "value1"])
                case "corphotelroomavailableserviceclient.checkavail.hotelcheckavail":
                    return new CodeMappingConfig(mappingActionRelations: ["key2": "value2"])
                case "corphotelroomavailableserviceclient.checkavail.bookingbaseinfo":
                    return new CodeMappingConfig(mappingActionRelations: ["key3": "value3"])
                default:
                    return null
            }
        }
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            public static boolean hotelCheckAvail(Map<String, StrategyInfo> strategyInfoMap) {
                return true
            }
        }

        when: "Calling buildCodeMappingConfig"
        def result = new WrapperOfCheckAvail.CheckAvailBuilder()
                .buildCodeMappingConfig(qConfigOfCodeMappingConfig, supportActionTypeNew, strategyInfoMapInput)

        then: "The result should match the expected CodeMappingConfig"
        result.mappingActionRelations == expectedMappingActionRelations

        where:
        description                                              | supportActionTypeNew | expectedMappingActionRelations
        "supportActionTypeNew is false"                          | false                | ["key1": "value1"]
        "supportActionTypeNew is true, hotelCheckAvail is true"  | true                 | ["key2": "value2"]
    }

    @Unroll
    def "testBuildCodeMappingConfig-base with #description"() {
        given: "Mocked dependencies"
        def qConfigOfCodeMappingConfig = Mock(QConfigOfCodeMappingConfig)
        def strategyInfoMapInput = Mock(Map)

        and: "Stubbed behavior for QConfigOfCodeMappingConfig"
        qConfigOfCodeMappingConfig.getCodeMappingConfig(_) >> { String key ->
            switch (key) {
                case "corphotelroomavailableserviceclient.checkavail":
                    return new CodeMappingConfig(mappingActionRelations: ["key1": "value1"])
                case "corphotelroomavailableserviceclient.checkavail.hotelcheckavail":
                    return new CodeMappingConfig(mappingActionRelations: ["key2": "value2"])
                case "corphotelroomavailableserviceclient.checkavail.bookingbaseinfo":
                    return new CodeMappingConfig(mappingActionRelations: ["key3": "value3"])
                default:
                    return null
            }
        }
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            public static boolean hotelCheckAvail(Map<String, StrategyInfo> strategyInfoMap) {
                return false
            }
        }

        when: "Calling buildCodeMappingConfig"
        def result = new WrapperOfCheckAvail.CheckAvailBuilder()
                .buildCodeMappingConfig(qConfigOfCodeMappingConfig, supportActionTypeNew, strategyInfoMapInput)

        then: "The result should match the expected CodeMappingConfig"
        result.mappingActionRelations == expectedMappingActionRelations

        where:
        description                                              | supportActionTypeNew | expectedMappingActionRelations
        "supportActionTypeNew is true, hotelCheckAvail is false" | true                 | ["key3": "value3"]
    }

    def "check should throw exception when supportActionTypeNew is true"() {
        given: "Mocked dependencies"
        def qConfigOfCodeMappingConfig = Mock(QConfigOfCodeMappingConfig)
        def integrationSoaRequestType = Mock(IntegrationSoaRequestType) {
            getSourceFrom() >> SourceFrom.Online
            getUserInfo() >> Mock(com.ctrip.corp.bff.framework.template.entity.UserInfo) {
                getCorpId() >> "testCorpId"
            }
        }
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                return true
            }
        }
        def checkAvailBuilder = new WrapperOfCheckAvail.CheckAvailBuilder().setCheckAvailResponseType(new CheckAvailResponseType(responseCode: 50119))
        when: "Calling check with supportActionTypeNew=true"
        checkAvailBuilder.check(qConfigOfCodeMappingConfig, new ArrayList<StrategyInfo>(), integrationSoaRequestType, new HashMap<String, StrategyInfo>())

        then: "A BusinessException is thrown"
        def exception = thrown(BusinessException)
        exception.errorCode == com.ctrip.corp.bff.hotel.book.common.enums.BookingInitErrorEnum.CHECK_AVAIL_ERROR.errorCode
    }
}
