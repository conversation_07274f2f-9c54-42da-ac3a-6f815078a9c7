package com.ctrip.corp.bff.hotel.book.mapper.mapperofhotelpassengercheck

import com.ctrip.corp.agg.hotel.roomavailable.entity.BookBaseInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookHotelInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookRoomInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.GeographicalSituationInfo
import com.ctrip.corp.agg.hotel.roomavailable.entity.NationalityRestrictionType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryBillingGuestInfoType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryBookingRulesType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCertificateType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType
import com.ctrip.corp.agg.hotel.roomavailable.entity.RegionInfo
import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityInfoUtil;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CertificateInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.EmailInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ErrorInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.IssuingCountryInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.NationalityInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerBasicInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerCheckInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerName
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PhoneInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import com.ctrip.corp.bff.hotel.book.contract.HotelPassengerCheckRequestType
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfPassengerFormConfig
import com.ctrip.corp.bff.hotel.book.qconfig.entity.entityofpassengerform.CertificateRuleListEntity
import com.ctrip.corp.bff.hotel.book.qconfig.entity.entityofpassengerform.CertificatesBasedOnCountryEntity
import com.ctrip.corp.bff.hotel.book.qconfig.entity.entityofpassengerform.CertificatesEntity
import com.ctrip.corp.bff.hotel.book.qconfig.entity.entityofpassengerform.PassengerFormConfigEntity
import com.ctrip.corp.bff.hotel.book.qconfig.entity.entityofpassengerform.RuleInfoEntity
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * @Author: z.c. wang
 * @Date: 2025/4/18 16:54
 * @Version 1.0
 */
class MapperOfHotelPassengerCheckResponseTest extends Specification {

    def savePoint = new SavePoint()

    void setup() {
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return key
            }

            @Mock
            public static String getSharkValueFormat(String key, Object... arguments) {
                return key
            }

            @Mock
            public static String getTemplate(String key, BigDecimal num) {
                return key
            }
        }
    }

    void cleanup() {
        savePoint.rollback()
    }

    def mapper = new MapperOfHotelPassengerCheckResponse(
            qconfigOfPassengerFormConfig: new QconfigOfPassengerFormConfig(
                    passengerFormConfigEntity: new PassengerFormConfigEntity(
                            certificateListBasedOnCountry: [new CertificatesBasedOnCountryEntity(
                                    countryCode: "CN",
                                    certificateList: [
                                            new CertificatesEntity(certificateType: "IDENTITY_CARD")
                                    ]
                            )],
                            certificateFormRuleInfos: [
                                    new CertificateRuleListEntity(
                                            certificateType: "IDENTITY_CARD",
                                            ruleInfoList: [
                                                    new RuleInfoEntity(
                                                            formType: "FIRST_NAME_AND_MIDDLE_NAME",
                                                            regex: "^(?=.{1,100}\$)[a-zA-Z]{1,100}(?: [a-zA-Z]{1,100})*\$"
                                                    ),
                                                    new RuleInfoEntity(
                                                            formType: "LAST_NAME",
                                                            regex: "^(?=.{1,100}\$)[a-zA-Z]{1,100}(?: [a-zA-Z]{1,100})*\$"
                                                    )
                                            ]
                                    )
                            ]
                    )
            )
    )

    @Unroll
    def "convert"() {
        given:
        def result
        when:
        def passengerRequest = new HotelPassengerCheckRequestType(
                passengerCheckInput: new PassengerCheckInput(
                        passengerInfos: [
                                new PassengerInfo()
                        ]
                )
        )
        result = mapper.map(Tuple2.of(passengerRequest, null))
        then:
        result != null
    }

    @Unroll
    def "checkPassengerCertificate"() {
        given:
        def result
        def queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(
                bookingRules: new QueryBookingRulesType(
                        certificateInfo: new QueryCertificateType(
                                needCertificate: true,
                                supportCertificateType: ["IdentityCard"]
                        )
                ),
                baseInfo: new BookBaseInfoEntity(startTime: "2025-06-01 14:00:00")
        )

        when: "资源需要证件但证件为空"
        result = mapper.checkPassengerCertificate(new PassengerInfo(), queryCheckAvailContextResponseType)
        then:
        result.getErrorCode() == "CERTIFICATE_NOT_CONTAIN"

        when: "人下不存在资源所需证件"
        def passenger1 = new PassengerInfo(certificateInfos:
                [new CertificateInfo(defaultCertificateFlag: "T", certificateType: "PASSPORT")])
        result = mapper.checkPassengerCertificate(passenger1, queryCheckAvailContextResponseType)
        then:
        result.getErrorCode() == "CERTIFICATE_NOT_CONTAIN"

        when: "人下存在资源所需证件但未选中"
        def passenger2 = new PassengerInfo(certificateInfos:
                [new CertificateInfo(defaultCertificateFlag: "F", certificateType: "IDENTITY_CARD"),
                 new CertificateInfo(defaultCertificateFlag: "T", certificateType: "PASSPORT")])
        result = mapper.checkPassengerCertificate(passenger2, queryCheckAvailContextResponseType)
        then:
        result.getErrorCode() == "CERTIFICATE_CONTAIN_CHOOSE_WRONG"

        when: "证件过期"
        def passenger3 = new PassengerInfo(certificateInfos:
                [new CertificateInfo(defaultCertificateFlag: "T", transferCertificateNo: "123", certificateType: "IDENTITY_CARD", cardTimeLimit: "2025-05-30")])
        result = mapper.checkPassengerCertificate(passenger3, queryCheckAvailContextResponseType)
        then:
        result.getErrorCode() == "CERTIFICATE_EXPIRATION_EXPIRED"

        when: "证件距离过期6个月以内"
        def passenger4 = new PassengerInfo(certificateInfos:
                [new CertificateInfo(defaultCertificateFlag: "T", transferCertificateNo: "123", certificateType: "IDENTITY_CARD", cardTimeLimit: "2025-06-30")])
        result = mapper.checkPassengerCertificate(passenger4, queryCheckAvailContextResponseType)
        then:
        result.getErrorCode() == "CERTIFICATE_EXPIRATION_EXPIRED_WARNING"

    }

    @Unroll
    def "checkPassengerEmployee"() {
        given:
        def result
        def queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(
                bookingRules: new QueryBookingRulesType(
                        restrictRuleList: ["EMPLOYEE_BOOKING"]
                )
        )
        when:
        result = mapper.checkPassengerEmployee(new PassengerInfo(employee: "F"), queryCheckAvailContextResponseType)
        then:
        result.getErrorCode() == "PASSENGER_NOT_EMPLOYEE"
    }

    @Unroll
    def "checkPassengerNationality"() {
        given:
        def result
        def queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(
                bookingRules: new QueryBookingRulesType(
                        certificateInfo: new QueryCertificateType(
                                needCertificate: true
                        )
                ),
                hotelInfo: new BookHotelInfoEntity(
                        geographicalInfo: new GeographicalSituationInfo(
                                countryInfo: new RegionInfo(
                                        id: 2
                                )
                        )
                )
        )
        when:
        def passenger1 = new PassengerInfo(certificateInfos:
                [new CertificateInfo(defaultCertificateFlag: "T", certificateType: "PASSPORT",
                        issuingCountryInfo: new IssuingCountryInfo(countryCode: ""))])
        result = mapper.checkPassengerNationality(passenger1, queryCheckAvailContextResponseType)
        then:
        result.getErrorCode() == "NATIONALITY_NULL"

        when:
        def passenger2 = new PassengerInfo(certificateInfos:
                [new CertificateInfo(defaultCertificateFlag: "T", certificateType: "PASSPORT",
                        nationalityInfo: new NationalityInfo(nationalityCode: "CN"))])
        queryCheckAvailContextResponseType.getBookingRules().setNationalityRestrictionInfo(new NationalityRestrictionType(
                allowCountryCodeList: ["JP"]
        ))
        result = mapper.checkPassengerNationality(passenger2, queryCheckAvailContextResponseType)
        then:
        result.getErrorCode() == "NATIONALITY_NOT_ALLOW"

        when:
        queryCheckAvailContextResponseType.getBookingRules().setNationalityRestrictionInfo(new NationalityRestrictionType(
                blockCountryCodeList: ["CN"]
        ))
        result = mapper.checkPassengerNationality(passenger2, queryCheckAvailContextResponseType)
        then:
        result.getErrorCode() == "NATIONALITY_NOT_ALLOW"
    }

    def "passengerHasName"() {
        given:

        when:
        boolean result = mapper.passengerHasName(passengerInfo, queryCheckAvailContextResponseType)

        then:
        result == expectedResult

        where:
        passengerInfo                                                                                                                      | queryCheckAvailContextResponseType       | expectedResult
        null                                                                                                                               | new QueryCheckAvailContextResponseType() | false
        new PassengerInfo(certificateInfos: null)                                                                                          | new QueryCheckAvailContextResponseType() | false
        new PassengerInfo(passengerBasicInfo: new PassengerBasicInfo(defaultNameType: "EN", enName: new PassengerName(firstName: "John"))) | new QueryCheckAvailContextResponseType() | true
        // Add more test cases as needed
    }

    def "passengerHasEnName"() {
        given:

        when:
        boolean result = mapper.passengerHasEnName(passengerInfo, queryCheckAvailContextResponseType)

        then:
        result == expectedResult

        where:
        passengerInfo                                                                                                                      | queryCheckAvailContextResponseType       | expectedResult
        null                                                                                                                               | new QueryCheckAvailContextResponseType() | false
        new PassengerInfo(certificateInfos: null)                                                                                          | new QueryCheckAvailContextResponseType() | false
        new PassengerInfo(passengerBasicInfo: new PassengerBasicInfo(defaultNameType: "EN", enName: new PassengerName(firstName: "John"))) | new QueryCheckAvailContextResponseType() | true
        // Add more test cases as needed
    }

    @Unroll
    def "checkPassengerNull"() {
        given:
        def result
        when:
        result = mapper.checkPassengerNull(null)
        then:
        result.getErrorCode() == "PASSENGER_EMPTY_INVALID"

        when:
        result = mapper.checkPassengerNull([new PassengerInfo()])
        then:
        result == null
    }

    @Unroll
    def "checkPassengerNumber"() {
        given:
        def result
        def queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(
                roomInfo: new BookRoomInfoEntity(
                        maxGuestCountPerRoom: 2
                ),
                baseInfo: new BookBaseInfoEntity(
                        quantity: 2
                )
        )

        when:
        result = mapper.checkPassengerNumber(null, null)
        then:
        result == null

        when:
        def passenger1 = [new PassengerInfo(), new PassengerInfo()]
        result = mapper.checkPassengerNumber(passenger1, null)
        then:
        result == null

        when:
        result = mapper.checkPassengerNumber(passenger1, queryCheckAvailContextResponseType)
        then:
        result == null

        when:
        def passenger2 = [new PassengerInfo(), new PassengerInfo(), new PassengerInfo(), new PassengerInfo(), new PassengerInfo(), new PassengerInfo()]
        result = mapper.checkPassengerNumber(passenger2, queryCheckAvailContextResponseType)
        then:
        result.getErrorCode() == "PASSENGER_OVER_MAX"

        when:
        def passenger3 = [new PassengerInfo()]
        result = mapper.checkPassengerNumber(passenger3, queryCheckAvailContextResponseType)
        then:
        result.getErrorCode() == "PASSENGER_OVER_MIN"

    }

    @Unroll
    def "checkPassengerGender"() {
        given:
        def result
        def queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(
                hotelInfo: new BookHotelInfoEntity(
                        vendorId: 2417
                )
        )
        when:
        def passenger1 = new PassengerInfo(
                passengerBasicInfo: new PassengerBasicInfo(
                        gender: ""
                )
        )
        result = mapper.checkPassengerGender(passenger1, queryCheckAvailContextResponseType)
        then:
        result != null

        when:
        def passenger2 = new PassengerInfo(
                passengerBasicInfo: new PassengerBasicInfo(
                        gender: "F"
                )
        )
        result = mapper.checkPassengerGender(passenger2, queryCheckAvailContextResponseType)
        then:
        result == null

        when:
        def passenger3 = new PassengerInfo(
                passengerBasicInfo: new PassengerBasicInfo(
                        gender: "M"
                )
        )
        result = mapper.checkPassengerGender(passenger3, queryCheckAvailContextResponseType)
        then:
        result == null
    }

    @Unroll
    def "checkPassengerName"() {
        given:
        new MockUp<CityInfoUtil>() {
            @Mock
            public static boolean oversea(Integer cityId) {
                return false
            }

            @Mock
            public static boolean hmt(Integer cityId) {
                return false
            }
        }
        def queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(
                bookingRules: new QueryBookingRulesType(
                        certificateInfo: new QueryCertificateType(
                                needCertificate: true
                        )
                ),
                baseInfo: new BookBaseInfoEntity(startTime: "2025-06-01 14:00:00")
        )
        def result
        when:
        def passenger = new PassengerInfo(employee: "F",
                passengerBasicInfo: new PassengerBasicInfo(
                        defaultNameType: "EN"
                ),
                certificateInfos: [new CertificateInfo(legalLastName: "name", defaultCertificateFlag: "T", certificateType: "IDENTITY_CARD", cardTimeLimit: "2025-05-30")])
        result = mapper.checkPassengerName(passenger, queryCheckAvailContextResponseType)
        then:
        result.getErrorCode() == "PASSENGER_NEED_LOCAL_NAME"

        when:
        def passenger1 = new PassengerInfo(employee: "F",
                passengerBasicInfo: new PassengerBasicInfo(
                        defaultNameType: "LOCAL"
                ),
                certificateInfos: [new CertificateInfo(legalLastName: "name", defaultCertificateFlag: "T", certificateType: "PASSPORT", cardTimeLimit: "2025-05-30")])
        result = mapper.checkPassengerName(passenger1, queryCheckAvailContextResponseType)
        then:
        result.getErrorCode() == "PASSENGER_NEED_EN_NAME"

        when:
        def queryCheckAvailContextResponseTypeAmadeus = new QueryCheckAvailContextResponseType(
                bookingRules: new QueryBookingRulesType(
                        certificateInfo: new QueryCertificateType(
                                needCertificate: true
                        )
                ),
                baseInfo: new BookBaseInfoEntity(startTime: "2025-06-01 14:00:00"),
                roomInfo: new BookRoomInfoEntity(gdsType: "amadeus")
        )
        def passenger2 = new PassengerInfo(employee: "F",
                passengerBasicInfo: new PassengerBasicInfo(
                        defaultNameType: "LOCAL"
                ),
                certificateInfos: [new CertificateInfo(legalLastName: "哈哈", defaultCertificateFlag: "T", certificateType: "PASSPORT", cardTimeLimit: "2025-05-30")])
        result = mapper.checkPassengerName(passenger2, queryCheckAvailContextResponseTypeAmadeus)
        then:
        result.getErrorCode() == "CERTIFICATE_LACK_EN_NAME"

        when:
        queryCheckAvailContextResponseTypeAmadeus = new QueryCheckAvailContextResponseType(
                bookingRules: new QueryBookingRulesType(
                        certificateInfo: new QueryCertificateType(
                                needCertificate: false
                        )
                ),
                baseInfo: new BookBaseInfoEntity(startTime: "2025-06-01 14:00:00"),
                roomInfo: new BookRoomInfoEntity(gdsType: "amadeus")
        )
        passenger2 = new PassengerInfo(employee: "F",
                passengerBasicInfo: new PassengerBasicInfo(
                        defaultNameType: "LOCAL"
                ),
                certificateInfos: [new CertificateInfo(legalLastName: "哈哈", defaultCertificateFlag: "T", certificateType: "PASSPORT", cardTimeLimit: "2025-05-30")])
        result = mapper.checkPassengerName(passenger2, queryCheckAvailContextResponseTypeAmadeus)
        then:
        result.getErrorCode() == "PASSENGER_EN_NAME_NULL"

        when:
        def queryCheckAvailContextResponseTypeContainEn = new QueryCheckAvailContextResponseType(
                bookingRules: new QueryBookingRulesType(
                        certificateInfo: new QueryCertificateType(
                                needCertificate: true
                        ),
                        billingGuestInfo: new QueryBillingGuestInfoType(
                                guestsNameLanguages: ["en"]
                        )
                ),
                baseInfo: new BookBaseInfoEntity(startTime: "2025-06-01 14:00:00")
        )
        result = mapper.checkPassengerName(passenger2, queryCheckAvailContextResponseTypeContainEn)
        then:
        result.getErrorCode() == "CERTIFICATE_LACK_EN_NAME"

        when:
        queryCheckAvailContextResponseTypeContainEn = new QueryCheckAvailContextResponseType(
                bookingRules: new QueryBookingRulesType(
                        certificateInfo: new QueryCertificateType(
                                needCertificate: false
                        ),
                        billingGuestInfo: new QueryBillingGuestInfoType(
                                guestsNameLanguages: ["en"]
                        )
                ),
                baseInfo: new BookBaseInfoEntity(startTime: "2025-06-01 14:00:00")
        )
        result = mapper.checkPassengerName(passenger2, queryCheckAvailContextResponseTypeContainEn)
        then:
        result.getErrorCode() == "PASSENGER_EN_NAME_NULL"
    }


    def "checkPassengerMustInfo should return correct ErrorInfo for missing fields"() {

        expect:
        ErrorInfo errorInfo = mapper.checkPassengerMustInfo(passengerInfo)
        errorInfo.getErrorCode() == expectedErrorEnum

        where:
        passengerInfo                                                                                                    || expectedErrorEnum
        new PassengerInfo(phoneInfo: new PhoneInfo(transferPhoneNo: "123"), emailInfo: null)                             || "EMAIL_NULL"
        new PassengerInfo(phoneInfo: new PhoneInfo(transferPhoneNo: "123"), emailInfo: new EmailInfo(transferEmail: "")) || "EMAIL_NULL"
    }

    def "checkPassengerMustInfo should return null for complete PassengerInfo"() {
        given:
        PassengerInfo passengerInfo = new PassengerInfo(
                phoneInfo: new PhoneInfo(transferPhoneNo: "123456789"),
                emailInfo: new EmailInfo(transferEmail: "<EMAIL>"),
                passengerBasicInfo: new PassengerBasicInfo(gender: "M", birth: "2000-01-01")
        )

        when:
        ErrorInfo errorInfo = mapper.checkPassengerMustInfo(passengerInfo)

        then:
        errorInfo == null
    }

    def "should return null when certificateInfo is null"() {

        when:
        ErrorInfo errorInfo = mapper.checkCardName(null)

        then:
        errorInfo == null
    }

    def "checkCardName"() {
        given:
        def result
        when:
        result = mapper.checkCardName(new CertificateInfo(certificateType: "IDENTITY_CARD", legalFirstName: "@@@", legalLastName: "@@@"))
        then:
        result.getErrorCode() == "LEGAL_FIRST_NAME_INVALID"
        when:
        result = mapper.checkCardName(new CertificateInfo(certificateType: "IDENTITY_CARD", legalFirstName: "asd", legalLastName: "@@@"))
        then:
        result.getErrorCode() == "LEGAL_LAST_NAME_INVALID"
    }

    @Unroll
    def "checkOnlyForCNHotelPassengerNationality"() {
        given:
        def queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(
                bookingRules: new QueryBookingRulesType(
                        nationalityRestrictionInfo: new NationalityRestrictionType(
                                allowCountryCodeList: []
                        )
                )
        )
        def result
        when:
        result = mapper.checkOnlyForCNHotelPassengerNationality(queryCheckAvailContextResponseType, "US")
        then:
        result == null

        when:
        queryCheckAvailContextResponseType.bookingRules.nationalityRestrictionInfo.allowCountryCodeList = ["CN"]
        result = mapper.checkOnlyForCNHotelPassengerNationality(queryCheckAvailContextResponseType, "US")
        then:
        result.getErrorCode() == "ONLY_FOR_CN_GUEST"

        when:
        queryCheckAvailContextResponseType.bookingRules.nationalityRestrictionInfo.allowCountryCodeList = ["CN", "HK", "TW", "MO"]
        result = mapper.checkOnlyForCNHotelPassengerNationality(queryCheckAvailContextResponseType, "US")
        then:
        result.getErrorCode() == "ONLY_FOR_CN_GAT_GUEST"

        when:
        queryCheckAvailContextResponseType.bookingRules.nationalityRestrictionInfo.allowCountryCodeList = ["CN", "HK", "TW", "MO", "MY"]
        result = mapper.checkOnlyForCNHotelPassengerNationality(queryCheckAvailContextResponseType, "US")
        then:
        result == null
    }

    def "buildCertificateCheckOutputs"() {
        given:
        def result
        when:
        new MockUp<MapperOfHotelPassengerCheckResponse>() {
            @Mock
            private ErrorInfo checkCardName(CertificateInfo certificateInfo) {
                return new ErrorInfo()
            }
        }
        result = mapper.buildCertificateCheckOutputs([new CertificateInfo()],
                new QueryCheckAvailContextResponseType(bookingRules: new QueryBookingRulesType(
                        certificateInfo: new QueryCertificateType(
                                needCertificate: true
                        )
                )))
        then:
        result != null
    }

    def "buildCertificateCheckOutputs_name_valid"() {
        given:
        def result
        when:
        new MockUp<MapperOfHotelPassengerCheckResponse>() {
            @Mock
            private ErrorInfo checkCardName(CertificateInfo certificateInfo) {
                return null
            }
        }
        result = mapper.buildCertificateCheckOutputs([new CertificateInfo()],
                new QueryCheckAvailContextResponseType(bookingRules: new QueryBookingRulesType(
                        certificateInfo: new QueryCertificateType(
                                needCertificate: true
                        )
                )))
        then:
        result != null
    }

    @Unroll
    def "checkCardNationality"() {
        given:
        def result
        when:
        result = mapper.checkCardNationality(null)
        then:
        result == null
        when:
        result = mapper.checkCardNationality(new CertificateInfo())
        then:
        result == null
        when:
        result = mapper.checkCardNationality(
                new CertificateInfo(certificateType: "IDENTITY_CARD",
                        nationalityInfo: new NationalityInfo(nationalityCode: "CN")))
        then:
        result == null
        when:
        result = mapper.checkCardNationality(
                new CertificateInfo(certificateType: "HKMACPASS",
                        nationalityInfo: new NationalityInfo(nationalityCode: "CN")))
        then:
        result.getErrorCode() == "CERTIFICATE_TYPE_NOT_SUPPORT"
        when:
        result = mapper.checkCardNationality(
                new CertificateInfo(certificateType: "HKMACPASS",
                        nationalityInfo: new NationalityInfo(nationalityCode: "HK")))
        then:
        result == null
    }

}
