package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import com.ctrip.soa._21234.SearchTripBasicInfoRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

class MapperOfSearchTripBasicInfoRequestTypeTest extends Specification {
    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }
    def mapper = new MapperOfSearchTripBasicInfoRequestType()

    @Unroll
    def "convert方法-不同sourceFrom分支-#desc"() {
        given:
        def integrationSoaRequestType = Mock(IntegrationSoaRequestType) {
            getLanguage() >> language
            getSourceFrom() >> sourceFrom
        }
        def tuple = Tuple2.of(tripId, integrationSoaRequestType)

        when:
        SearchTripBasicInfoRequestType result = mapper.convert(tuple)

        then:
        result.getTripId() == tripId
        result.getLanguage() == language
        result.getChannelType() == expectedChannelType

        where:
        desc           | tripId | language | sourceFrom         | expectedChannelType
        'source为null' | 123L   | 'zh-CN'  | null               | 3
        'Offline'      | 456L   | 'en-US'  | SourceFrom.Offline | 2
        'Online'       | 789L   | 'ja-JP'  | SourceFrom.Online  | 1
        '其他'         | 101L   | 'fr-FR'  | SourceFrom.H5      | 3
    }

    def "check方法应该返回null-正常参数"() {
        given: "准备正常的输入参数"
        def integrationSoaRequestType = Mock(IntegrationSoaRequestType) {
            getLanguage() >> "zh-CN"
            getSourceFrom() >> SourceFrom.Online
        }
        def tuple = Tuple2.of(123L, integrationSoaRequestType)

        when: "调用check方法"
        def result = mapper.check(tuple)

        then: "应该返回null，表示不进行参数校验"
        result == null
    }

    def "check方法应该返回null-tripId为null"() {
        given: "tripId为null"
        def integrationSoaRequestType = Mock(IntegrationSoaRequestType) {
            getLanguage() >> "zh-CN"
            getSourceFrom() >> SourceFrom.Online
        }
        def tuple = Tuple2.of(null, integrationSoaRequestType)

        when: "调用check方法"
        def result = mapper.check(tuple)

        then: "应该返回null"
        result == null
    }

    def "check方法应该返回null-IntegrationSoaRequestType为null"() {
        given: "IntegrationSoaRequestType为null"
        def tuple = Tuple2.of(123L, null)

        when: "调用check方法"
        def result = mapper.check(tuple)

        then: "应该返回null"
        result == null
    }

    def "check方法应该返回null-两个参数都为null"() {
        given: "两个参数都为null"
        def tuple = Tuple2.of(null, null)

        when: "调用check方法"
        def result = mapper.check(tuple)

        then: "应该返回null"
        result == null
    }

    @Unroll
    def "check方法应该返回null-不同语言代码-#language"() {
        given: "不同的语言代码"
        def integrationSoaRequestType = Mock(IntegrationSoaRequestType) {
            getLanguage() >> language
            getSourceFrom() >> SourceFrom.Online
        }
        def tuple = Tuple2.of(123L, integrationSoaRequestType)

        when: "调用check方法"
        def result = mapper.check(tuple)

        then: "应该返回null"
        result == null

        where: "测试各种语言代码"
        language << ["zh-CN", "en-US", "ja-JP", "fr-FR", "de-DE", "ko-KR", "", null]
    }
}
