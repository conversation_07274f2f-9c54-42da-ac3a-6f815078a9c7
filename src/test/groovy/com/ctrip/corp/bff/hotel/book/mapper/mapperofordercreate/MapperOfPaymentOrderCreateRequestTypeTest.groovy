package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.agg.hotel.roomavailable.entity.BookRoomInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.MultipleLanguageText
import com.ctrip.corp.agg.hotel.roomavailable.entity.PaymentRules
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryBookingRulesType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCancelPolicyType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryGuaranteeDetailType
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.BookInitResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ReservationResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.CreateOrderResult
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.FollowApprovalResult
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.PosEnum
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.TripInput
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant
import com.ctrip.corp.bff.hotel.book.common.enums.HotelBalanceTypeEnum
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum
import com.ctrip.corp.bff.hotel.book.common.enums.PaymentGuaranteePolyEnum
import com.ctrip.corp.bff.hotel.book.common.enums.RoomTypeEnum
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.BookUrlInfo
import com.ctrip.corp.bff.hotel.book.contract.FollowApprovalInfoInput
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.contract.PayMentInfoInput
import com.ctrip.corp.bff.hotel.book.sharkmock.SharkMockUtil
import com.ctrip.corp.bff.mice.basic.auth.contract.TmsCreateOrderVerifyResponseType
import com.ctrip.corp.bff.payment.contract.AcquirerDetail
import com.ctrip.corp.bff.payment.contract.InsuranceInfo
import com.ctrip.corp.bff.payment.contract.MixPaymentOrderInfo
import com.ctrip.corp.bff.payment.contract.PaymentInfo
import com.ctrip.corp.bff.payment.contract.PaymentOrderCreateRequestType
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType
import com.ctrip.corp.hotelbooking.hotelws.entity.AmountInfoType
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderRequestType
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType
import com.ctrip.soa._21234.CreateTripResponseType
import com.ctrip.corp.hotelbooking.hotelws.entity.OrderAmountType
import com.ctrip.corp.hotelbooking.hotelws.entity.OrderPaymentType
import com.ctrip.corp.hotelbooking.hotelws.entity.PaymentItemType
import com.ctrip.corp.order.paymentcenter.bill.contract.QueryPaymentBillConfigRequestType
import com.ctrip.corp.order.paymentcenter.bill.contract.QueryPaymentBillConfigResponseType
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import org.apache.commons.lang3.StringUtils
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2024/12/20 15:36
 *
 */
class MapperOfPaymentOrderCreateRequestTypeTest extends Specification {
    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }
    @Unroll
    def "testBuildNeedCardPayFee with different scenarios"() {
        given:
        def queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(
                roomInfo: new BookRoomInfoEntity(gdsType: "", balanceType: balanceType, paymentRulesInfo: new PaymentRules(forceVccPay: forceVccPay)))
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo()


        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        orderCreateRequestType.setStrategyInfos(strategyInfos)

        when:
        String result = new MapperOfPaymentOrderCreateRequestType().buildNeedCardPayFee(checkAvailInfo, orderCreateRequestType)

        then:
        result == expectedNeedCardPayFee

        where:
        forceVccPay | balanceType | strategyInfos                                                            || expectedNeedCardPayFee
        true        | "PP"        | [new StrategyInfo(strategyKey: "NEED_CARD_PAY_FEE", strategyValue: "T")] || "F"
        false       | "FG"        | [new StrategyInfo(strategyKey: "NEED_CARD_PAY_FEE", strategyValue: "T")] || "F"
        true        | "FG"        | [new StrategyInfo(strategyKey: "NEED_CARD_PAY_FEE", strategyValue: "F")] || "F"
        true        | "FG"        | []                                                                       || "F"
        false       | "PP"        | [new StrategyInfo(strategyKey: "NEED_CARD_PAY_FEE", strategyValue: "T")] || "T"
        false       | "PP"        | []                                                                       || "F"
    }

    def "testSupportWechatPayByOthers"() {
        given: "Mock dependencies and inputs"
        def mapper = new MapperOfPaymentOrderCreateRequestType()
        def orderCreateRequestType = new OrderCreateRequestType()
        def accountInfo = Mock(WrapperOfAccount.AccountInfo)
        def createOrderResponseType = new CreateOrderResponseType()
        def orderCreateToken = new OrderCreateToken()

        // Set necessary fields for orderCreateRequestType and accountInfo
        orderCreateRequestType.paymentInfoInput = new PayMentInfoInput()
        orderCreateRequestType.paymentInfoInput.canWechatPayByOthers = "T"
        accountInfo.supportWechat() >> true
        orderCreateRequestType.integrationSoaRequestType = new IntegrationSoaRequestType()
        orderCreateRequestType.integrationSoaRequestType.language = "zh-CN"

        when: "Calling supportWechatPayByOthers method"
        def result = mapper.supportWechatPayByOthers(orderCreateRequestType, accountInfo, createOrderResponseType, orderCreateToken, false)

        then: "Assert the expected outcomes"
        !result // Replace with the actual expected result
        // Add more assertions as needed
    }

    @Unroll
    def "test getClientType with #description"() {
        given:
        MapperOfPaymentOrderCreateRequestType mapper = new MapperOfPaymentOrderCreateRequestType()

        when:
        String result = mapper.getClientType(sourceFrom, clientTypeRequest, false)

        then:
        result == expectedResult

        where:
        description           | sourceFrom         | clientTypeRequest | expectedResult
        "online source"       | SourceFrom.Online  | null              | "Online"
        "offline source"      | SourceFrom.Offline | null              | "Online"
        "H5 client type"      | SourceFrom.Native  | "H5"              | "H5"
        "MiniApp client type" | SourceFrom.Native  | "MiniApp"         | "MINI_APP"
        "default client type" | SourceFrom.Native  | "Other"           | "APP"
    }

    @Unroll
    def "test buildMixPaymentOrderInfoRoomFee with #description"() {
        given: "Mock dependencies and inputs"
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
        def mapper = new MapperOfPaymentOrderCreateRequestType()
        PaymentInfo paymentInfo = new PaymentInfo(paymentTo: "CtripCorp", payTypes: ["Pay"])
        def queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(
                roomInfo: new BookRoomInfoEntity(gdsType: "", balanceType: "FG"),
                bookingRules: new QueryBookingRulesType(cancelPolicyInfo: new QueryCancelPolicyType(guaranteePolicyInfo: new QueryGuaranteeDetailType(paymentGuaranteePoly: paymentGuaranteePoly)))
        )
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo()
        ResourceToken resourceToken = new ResourceToken()
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(hotelPayTypeInput: Arrays.asList(new HotelPayTypeInput(payType: "SELF_PAY", payCode: "SERVICE"), new HotelPayTypeInput(payType: "GUARANTEE_SELF_PAY", payCode: "ROOM")))

        when: "Calling buildMixPaymentOrderInfoRoomFee method"
        MixPaymentOrderInfo result = mapper.buildMixPaymentOrderInfoRoomFee(paymentInfo, checkAvailInfo, resourceToken, orderCreateRequestType, new BigDecimal(200), "USD")

        then: "Assert the expected outcomes"

        then: "The result should match the expected outcome"
        result == expectedResult

        where: "Different scenarios for testing"
        description                        | paymentGuaranteePoly || expectedResult
        "scenario 1"                       | "CTRIP"              || new MixPaymentOrderInfo(title: "房费", amount: new BigDecimal("200"), currency: "USD", payTypes: ["Pay"], paymentTo: "CtripCorp")
        "scenario 2 with different values" | "HOTEL"              || new MixPaymentOrderInfo(title: "房费", amount: new BigDecimal("200"), currency: "USD", payTypes: ["Guarantee"], paymentTo: "SupplierCorp")
    }

    @Unroll
    def "test getServiceFeeSelf with #description"() {
        given:
        MapperOfPaymentOrderCreateRequestType mapper = new MapperOfPaymentOrderCreateRequestType()
        CreateOrderResponseType createOrderResponseType = new CreateOrderResponseType(
                orderPaymentInfo: new OrderPaymentType(
                        orderAmountInfo: new OrderAmountType(
                                paymentItemList: [
                                        new PaymentItemType(feeType: "BookServiceFee", amountInfo: new AmountInfoType(amount: new BigDecimal("100.00")))
                                ]
                        )
                )
        )
        OrderCreateToken orderCreateToken = new OrderCreateToken()
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                hotelPayTypeInput: Arrays.asList(new HotelPayTypeInput(payType: payType, payCode: "SERVICE"))
        )
        ResourceToken resourceToken = new ResourceToken()

        when:
        BigDecimal result = mapper.getServiceFeeSelf(createOrderResponseType, orderCreateToken, orderCreateRequestType, resourceToken)

        then:
        result == expectedResult

        where:
        description           | payType    || expectedResult
        "no service fee"      | "CORP_PAY" || BigDecimal.ZERO
        "service fee present" | "SELF_PAY" || new BigDecimal("100.00")
    }

    @Unroll
    def "test getRoomFee with #description"() {
        given:
        MapperOfPaymentOrderCreateRequestType mapper = new MapperOfPaymentOrderCreateRequestType()
        CreateOrderResponseType createOrderResponseType = new CreateOrderResponseType(
                orderPaymentInfo: new OrderPaymentType(
                        orderAmountInfo: new OrderAmountType(
                                paymentItemList: [
                                        new PaymentItemType(feeType: "BookServiceFee", amountInfo: new AmountInfoType(amount: new BigDecimal("150.00")))
                                ],
                                personalPayAmountInfo: new AmountInfoType(amount: new BigDecimal("200.00"))
                        )
                )
        )
        OrderCreateToken orderCreateToken = new OrderCreateToken()
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                hotelPayTypeInput: Arrays.asList(new HotelPayTypeInput(payType: serivePayType, payCode: "SERVICE"))
        )
        ResourceToken resourceToken = new ResourceToken()

        when:
        BigDecimal result = mapper.getRoomFee(createOrderResponseType, orderCreateToken, orderCreateRequestType, resourceToken)

        then:
        result == expectedResult

        where:
        description           | serivePayType || expectedResult
        "no service fee"      | "CORP_PAY"    || new BigDecimal("200.00")
        "service fee present" | "SELF_PAY"    || new BigDecimal("50.00")
    }
    @Unroll
    def "testTravelIDParam with #description"() {
        given:
        MapperOfPaymentOrderCreateRequestType mapper = new MapperOfPaymentOrderCreateRequestType()
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                followApprovalInfoInput: new FollowApprovalInfoInput(followSelected: followSelected))
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo)
        accountInfo.isPackageEnabled() >> true
        CreateTripResponseType createTripResponseType = Mock(CreateTripResponseType)
        createTripResponseType.getTripId() >> tripId
        OrderCreateToken orderCreateToken = new OrderCreateToken(
                followApprovalResult: new FollowApprovalResult(tripId: followFripId))

        when:
        String result = mapper.travelIDParam(orderCreateRequestType, accountInfo, createTripResponseType, orderCreateToken)

        then:
        result == expectedResult

        where:
        description           | tripId | followSelected | followFripId || expectedResult
        "createTrip is valid" | 12345L | "F"            | null         || "&Travel=12345"
        "createTrip is 0"     | 0      | "F"            | null         || ""
        "createTrip is null"  | null   | "F"            | null         || ""
        "followTrip is valid" | 12345L | "T"            | "12345"      || ""
    }

    @Unroll
    def "testBuildSuccessBackUrlForApp with #description"() {
        given:
        MapperOfPaymentOrderCreateRequestType mapper = new MapperOfPaymentOrderCreateRequestType()
        List<BookUrlInfo> bookUrlInfos = [
                new BookUrlInfo(urlType: "FROM_URL_ORDER_DETAIL", urlValue: "http://example.com/orderDetail"),
                new BookUrlInfo(urlType: "FROM_URL", urlValue: "http://example.com/fromUrl"),
                new BookUrlInfo(urlType: "E_BACK", urlValue: "http://example.com/errorBack"),
                new BookUrlInfo(urlType: "S_BACK", urlValue: "https://ct.ctrip.com/m/dy_3_PayMent/PayState/PayState?site=2&LimitStandard=false&needFinishResult=true&")
        ]
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: SourceFrom.Native, userInfo: new UserInfo(userId: "123", corpId: "456")),
                hotelPayTypeInput: Arrays.asList(new HotelPayTypeInput(payType: "SELF_PAY", payCode: "ROOM")),
                cityInput: new CityInput(cityId: 1),
                hotelBookInput: new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo(checkIn: "2024-12-20", checkOut: "2024-12-21")),
                bookUrlInfos: bookUrlInfos,
        )
        CreateOrderResponseType createOrderResponseType = new CreateOrderResponseType()
        OrderCreateToken orderCreateToken = new OrderCreateToken()
        QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType = new QueryPaymentBillConfigResponseType()
        ResourceToken resourceToken = new ResourceToken(
                reservationResourceToken: new ReservationResourceToken(wsId: "wsid"),
                hotelResourceToken: new HotelResourceToken(hotelId: 1))
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo)
        checkAvailInfo.getRoomType() >> "M"
        TmsCreateOrderVerifyResponseType tmsCreateOrderVerifyResponseType = new TmsCreateOrderVerifyResponseType()
        CreateTripResponseType createTripResponseType = Mock(CreateTripResponseType)
        createTripResponseType.getTripId() >> 123L
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo)
        accountInfo.isPackageEnabled() >> true

        new MockUp<com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                return false
            }
        }

        when:
        String result = mapper.buildSuccessBackUrl(bookUrlInfos, orderCreateRequestType, createOrderResponseType, orderCreateToken, queryPaymentBillConfigResponseType,
                resourceToken, checkAvailInfo, tmsCreateOrderVerifyResponseType, createTripResponseType, accountInfo, [:])

        then:
        result == expectedResult

        where:
        description           || expectedResult
        "valid app source"    || "https://ct.ctrip.com/m/dy_3_PayMent/PayState/PayState?site=2&LimitStandard=false&needFinishResult=true&OrderNumber=0&Uid=123&CorpId=456&OldOrderId=&WSTransactionID=wsid&IsOversea=true&Travel=123&HtlType=false&AuthPass=false&cityId=1&hotelId=1&checkIn=2024-12-20&checkOut=2024-12-21&mssokey=null"
    }

    @Unroll
    def "testCheckMiniApp with no exception for #description"() {
        given: "A MapperOfPaymentOrderCreateRequestType instance"
        def mapper = new MapperOfPaymentOrderCreateRequestType()
        def orderCreateRequestType = new OrderCreateRequestType(
                paymentInfoInput: new PayMentInfoInput(clientType: clientType)
        )
        def queryPaymentBillConfigResponseType = new QueryPaymentBillConfigResponseType(
                disabledPayWayList: disabledPayWayList
        )

        when: "Calling checkMiniApp method"
        mapper.checkMiniApp(orderCreateRequestType, queryPaymentBillConfigResponseType)

        then: "No exception is thrown"
        noExceptionThrown()

        where:
        description                                     | clientType                        | disabledPayWayList
        "Non-MiniApp client type"                       | "OtherClientType"                 | ["OGP_WechatScanCode"]
        "MiniApp client type with no disabled pay ways" | CommonConstant.CLIENTTYPE_MINIAPP | []
    }

    @Unroll
    def "testCheckMiniApp with exception for #description"() {
        given: "A MapperOfPaymentOrderCreateRequestType instance"
        def mapper = new MapperOfPaymentOrderCreateRequestType()
        def orderCreateRequestType = new OrderCreateRequestType(
                paymentInfoInput: new PayMentInfoInput(clientType: CommonConstant.CLIENTTYPE_MINIAPP)
        )
        def queryPaymentBillConfigResponseType = new QueryPaymentBillConfigResponseType(
                disabledPayWayList: ["OGP_WechatScanCode", "WechatScanCode"]
        )

        when: "Calling checkMiniApp method"
        mapper.checkMiniApp(orderCreateRequestType, queryPaymentBillConfigResponseType)

        then: "No exception is thrown"
        def exception = thrown(BusinessException)
        exception.errorCode == 686
    }

    @Unroll
    def "testBuildDisabledPayWayList for #description"() {
        given: "A MapperOfPaymentOrderCreateRequestType instance and mocked inputs"
        def mapper = Spy(MapperOfPaymentOrderCreateRequestType) {
            supportWechatPayByOthers(_, _, _, _, _) >> supportWechatPayOthers
        }
        def queryPaymentBillConfigResponseType = new QueryPaymentBillConfigResponseType(disabledPayWayList: disabledPayWayList)
        def orderCreateRequestType = new OrderCreateRequestType(paymentInfoInput: new PayMentInfoInput())
        def createOrderResponseType = Mock(CreateOrderResponseType)
        def orderCreateToken = Mock(OrderCreateToken)
        def accountInfo = Mock(WrapperOfAccount.AccountInfo)

        when: "Calling buildDisabledPayWayList"
        def result = mapper.buildDisabledPayWayList(queryPaymentBillConfigResponseType, orderCreateRequestType, createOrderResponseType, orderCreateToken, accountInfo, offlineNewPay)

        then: "The result should match the expected output"
        result == expectedResult

        where:
        description                                | disabledPayWayList                          | offlineNewPay | supportWechatPayOthers || expectedResult
        "Empty disabledPayWayList, no support"     | []                                          | false         | false                  || ["WechatPaybyothers"]
        "Non-empty disabledPayWayList, no support" | ["OGP_WechatScanCode"]                      | false         | false                  || ["OGP_WechatScanCode", "WechatPaybyothers"]
        "Non-empty disabledPayWayList, no support" | ["OGP_WechatScanCode", "WechatPaybyothers"] | false         | false                  || ["OGP_WechatScanCode", "WechatPaybyothers"]
        "Empty disabledPayWayList, no support"     | []                                          | false         | true                   || []
        "Non-empty disabledPayWayList, no support" | ["OGP_WechatScanCode"]                      | false         | true                   || ["OGP_WechatScanCode"]
        "Non-empty disabledPayWayList, no support" | ["OGP_WechatScanCode", "WechatPaybyothers"] | false         | true                   || ["OGP_WechatScanCode", "WechatPaybyothers"]
    }

    def "buildPaymentExtendInfo"() {
        given:
        def mapper = Spy(MapperOfPaymentOrderCreateRequestType) {
            buildAcquirerDetails(_, _) >> [new AcquirerDetail()]
            buildInsuranceInfos(_, _, _) >> [new InsuranceInfo()]
        }
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(pos: PosEnum.AUSTRIA)))
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo) {
        }
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContext = Mock(WrapperOfCheckAvail.CheckAvailContextInfo) {
            getRoomTypeEnum() >> RoomTypeEnum.M
        }
        CreateOrderResponseType createOrderResponseType = new CreateOrderResponseType()
        OrderCreateToken orderCreateToken = new OrderCreateToken()
        when:
        def result = mapper.buildPaymentExtendInfo(orderCreateRequestType, accountInfo, checkAvailContext, createOrderResponseType, orderCreateToken)
        then:
        result.hotelExtendInfo.roomType == "M"


        when:
        orderCreateRequestType = new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(pos: PosEnum.CHINA)))
        result = mapper.buildPaymentExtendInfo(orderCreateRequestType, accountInfo, checkAvailContext, createOrderResponseType, orderCreateToken)
        then:
        result.hotelExtendInfo == null
    }

    def "buildCancelBackUrl"() {
        given:
        def mapper = Spy(MapperOfPaymentOrderCreateRequestType) {
            buildBookUrl(_, _) >> "https://www.fat4.trip.com/m/hotel/order?backUrl=%2Fm%2Ftrips&"
            needJumpOrderHomePage(_, _, _) >> false
        }
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            public static CreateOrderResult buildCreateOrderResult(CreateOrderResponseType createOrderResponseType,
                                                                   OrderCreateRequestType orderCreateRequestType, OrderCreateToken orderCreateToken) {
                return new CreateOrderResult(orderID: 123456L)
            }
        }
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(
                        userInfo: new UserInfo(pos: PosEnum.AUSTRIA),
                        language: "zh-CN",
                        sourceFrom: SourceFrom.H5
                )
        )
        CreateOrderResponseType createOrderResponseType = new CreateOrderResponseType()
        OrderCreateToken orderCreateToken = new OrderCreateToken()
        when:
        def result = mapper.buildCancelBackUrl(orderCreateRequestType, createOrderResponseType, orderCreateToken, null, null)
        then:
        result == "https://www.fat4.trip.com/m/hotel/order?backUrl=%2Fm%2Ftrips&orderId=123456&locale=zh-CN"


        when:
        orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(
                        userInfo: new UserInfo(pos: PosEnum.AUSTRIA),
                        language: "zh-CN",
                        sourceFrom: SourceFrom.Online
                )
        )
        result = mapper.buildCancelBackUrl(orderCreateRequestType, createOrderResponseType, orderCreateToken, null, null)
        then:
        result == null


        when:
        orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(
                        userInfo: new UserInfo(pos: PosEnum.CHINA),
                        language: "zh-CN",
                        sourceFrom: SourceFrom.H5
                )
        )
        result = mapper.buildCancelBackUrl(orderCreateRequestType, createOrderResponseType, orderCreateToken, null, null)
        then:
        result == null
    }

    def "buildCancelBackUrl with needJumpOrderHomePage true"() {
        given:
        def mapper = Spy(MapperOfPaymentOrderCreateRequestType) {
            buildFromOrderListUrl(_) >> "https://www.fat4.trip.com/m/hotel/orders?&backUrl=%2Fm%2Ftrips&"
            needJumpOrderHomePage(_, _, _) >> true
        }
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(
                        userInfo: new UserInfo(pos: PosEnum.AUSTRIA),
                        language: "zh-CN",
                        sourceFrom: SourceFrom.H5
                )
        )
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailContextInfo)
        CreateOrderResponseType createOrderResponseType = new CreateOrderResponseType()
        OrderCreateToken orderCreateToken = new OrderCreateToken()
        ResourceToken resourceToken = new ResourceToken()
        when: "blueSpace fromUrl"
        def result = mapper.buildCancelBackUrl(orderCreateRequestType, createOrderResponseType, orderCreateToken, checkAvailInfo, resourceToken)
        then:
        result == "https://www.fat4.trip.com/m/hotel/orders?&backUrl=%2Fm%2Ftrips&"
    }


    def "buildFromUrl"() {
        given:
        def mapper = Spy(MapperOfPaymentOrderCreateRequestType) {
            buildBookUrl(_, _) >> "https://www.fat4.trip.com/m/hotel/order?&backUrl=%2Fm%2Ftrips&"
            buildFromUrl(_, _, _, _) >> "https://www.fat4.ctrip.com/m/hotel/order?backUrl=xxx"
            buildFromOrderListUrl(_) >> "https://www.fat4.trip.com/m/hotel/orders?&backUrl=%2Fm%2Ftrips&"
            needJumpOrderHomePage(_, _, _) >> false
        }
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            public static CreateOrderResult buildCreateOrderResult(CreateOrderResponseType createOrderResponseType,
                                                                   OrderCreateRequestType orderCreateRequestType, OrderCreateToken orderCreateToken) {
                return new CreateOrderResult(orderID: 123456L)
            }
        }
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(
                        userInfo: new UserInfo(pos: PosEnum.AUSTRIA),
                        language: "zh-CN",
                        sourceFrom: SourceFrom.H5
                )
        )
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailContextInfo)
        CreateOrderResponseType createOrderResponseType = new CreateOrderResponseType()
        OrderCreateToken orderCreateToken = new OrderCreateToken()
        ResourceToken resourceToken = new ResourceToken()
        when: "blueSpace fromUrl"
        def result = mapper.buildFromUrl(orderCreateRequestType, createOrderResponseType, orderCreateToken, checkAvailInfo, resourceToken)
        then:
        result == "https://www.fat4.trip.com/m/hotel/order?&backUrl=%2Fm%2Ftrips&orderId=123456&locale=zh-CN"

        when: "china fromUrl"
        orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(
                        userInfo: new UserInfo(pos: PosEnum.CHINA),
                        language: "zh-CN",
                        sourceFrom: SourceFrom.H5
                )
        )
        result = mapper.buildFromUrl(orderCreateRequestType, createOrderResponseType, orderCreateToken, checkAvailInfo, resourceToken)
        then:
        result == "https://www.fat4.ctrip.com/m/hotel/order?backUrl=xxx"
    }

    def "buildFromUrl with needJumpOrderHomePage true"() {
        given:
        def mapper = Spy(MapperOfPaymentOrderCreateRequestType) {
            buildFromOrderListUrl(_) >> "https://www.fat4.trip.com/m/hotel/orders?&backUrl=%2Fm%2Ftrips&"
            needJumpOrderHomePage(_, _, _) >> true
        }
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(
                        userInfo: new UserInfo(pos: PosEnum.AUSTRIA),
                        language: "zh-CN",
                        sourceFrom: SourceFrom.H5
                )
        )
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailContextInfo)
        CreateOrderResponseType createOrderResponseType = new CreateOrderResponseType()
        OrderCreateToken orderCreateToken = new OrderCreateToken()
        ResourceToken resourceToken = new ResourceToken()
        when: "blueSpace fromUrl"
        def result = mapper.buildFromUrl(orderCreateRequestType, createOrderResponseType, orderCreateToken, checkAvailInfo, resourceToken)
        then:
        result == "https://www.fat4.trip.com/m/hotel/orders?&backUrl=%2Fm%2Ftrips&"
    }

    def "needJumpOrderHomePage"() {
        given:
        new MockUp<HotelPayTypeUtil>() {
            @Mock
            public static HotelPayTypeEnum getRoomPayType(List<HotelPayTypeInput> hotelPayTypeInputs) {
                return HotelPayTypeEnum.GUARANTEE_SELF_PAY;
            }
        }
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailContextInfo) {
            getRoomTypeEnum() >> roomType
            getPaymentGuaranteePolyEnum() >> paymentGuaranteePoly
        }
        ResourceToken resourceToken = new ResourceToken()
        def mapper = Spy(MapperOfPaymentOrderCreateRequestType) {
        }
        expect:
        result == mapper.needJumpOrderHomePage(orderCreateRequestType, checkAvailInfo, resourceToken)
        where:
        roomType       | paymentGuaranteePoly                  || result
        RoomTypeEnum.M | PaymentGuaranteePolyEnum.PAY_TO_HOTEL || true
        RoomTypeEnum.M | PaymentGuaranteePolyEnum.PAY_TO_CTRIP || false
        RoomTypeEnum.C | PaymentGuaranteePolyEnum.PAY_TO_HOTEL || true
        RoomTypeEnum.C | PaymentGuaranteePolyEnum.PAY_TO_CTRIP || false
    }

    def "needJumpOrderHomePage-CASH"() {
        given:
        new MockUp<HotelPayTypeUtil>() {
            @Mock
            public static HotelPayTypeEnum getRoomPayType(List<HotelPayTypeInput> hotelPayTypeInputs) {
                return HotelPayTypeEnum.CASH;
            }
            @Mock
            public static HotelPayTypeEnum getServicePayType(List<HotelPayTypeInput> hotelPayTypeInputs,
                                                             HotelPayTypeEnum selectedRoomPayType,
                                                             ResourceToken resourceToken) {
                return HotelPayTypeEnum.SELF_PAY;
            }
        }
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailContextInfo) {
            getRoomTypeEnum() >> roomType
            getPaymentGuaranteePolyEnum() >> paymentGuaranteePoly
        }
        ResourceToken resourceToken = new ResourceToken()
        def mapper = Spy(MapperOfPaymentOrderCreateRequestType) {
        }
        expect:
        result == mapper.needJumpOrderHomePage(orderCreateRequestType, checkAvailInfo, resourceToken)
        where:
        roomType       | paymentGuaranteePoly                  || result
        RoomTypeEnum.M | PaymentGuaranteePolyEnum.PAY_TO_HOTEL || false
        RoomTypeEnum.M | PaymentGuaranteePolyEnum.PAY_TO_CTRIP || false
        RoomTypeEnum.C | PaymentGuaranteePolyEnum.PAY_TO_HOTEL || true
        RoomTypeEnum.C | PaymentGuaranteePolyEnum.PAY_TO_CTRIP || true
    }

    def "buildFromOrderListUrl"() {
        given:
        def mapper = Spy(MapperOfPaymentOrderCreateRequestType) {
            buildBookUrl(_, _) >> "https://www.fat4.trip.com/m/hotel/orders?&backUrl=%2Fm%2Ftrips&" >> null
        }
        when:
        def resutl = mapper.buildFromOrderListUrl(new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(language: "zh-CN", sourceFrom: SourceFrom.H5)))
        then:
        resutl == "https://www.fat4.trip.com/m/hotel/orders?&backUrl=%2Fm%2Ftrips&locale=zh-CN"


        when:
        resutl = mapper.buildFromOrderListUrl(new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(language: "zh-CN", sourceFrom: SourceFrom.H5)))
        then:
        resutl == null
    }

    def "buildOrderPromptInfo" () {
        given:
        new MockUp<JsonUtil>() {
            @Mock
            public static String toJson(Object obj) {
                return ((MapperOfPaymentOrderCreateRequestType.OrderPromptInfo) obj).getVersion()
            }
            }
        expect:
        new MapperOfPaymentOrderCreateRequestType().buildOrderPromptInfo(new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: SourceFrom.H5)), null, null, null) == null
        new MapperOfPaymentOrderCreateRequestType().buildOrderPromptInfo(new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: SourceFrom.Online, language: "zh-CN")), null, null, null) == null
        new MapperOfPaymentOrderCreateRequestType().buildOrderPromptInfo(new OrderCreateRequestType(hotelBookInput: new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo(checkIn: "2025-01-01", checkOut: "2025-01-02")),integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: SourceFrom.Online, language: "en-US")), Mock(WrapperOfCheckAvail.CheckAvailContextInfo), null, null) == "1.0.0"
    }

    def "buildPaymentOrderInfo" () {
        given:
        def input =  Mock(WrapperOfCheckAvail.CheckAvailContextInfo);
        input.getHotelName() >> new MultipleLanguageText()
        expect:
        new MapperOfPaymentOrderCreateRequestType().buildPaymentOrderInfo(new CreateOrderResponseType(), input, new OrderCreateRequestType(hotelBookInput: new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo(checkIn: "2025-01-01", checkOut: "2025-01-02")),integrationSoaRequestType: new IntegrationSoaRequestType()), new PaymentInfo(), new OrderCreateToken(), Mock(WrapperOfAccount.AccountInfo), new ResourceToken(bookInitResourceToken: new BookInitResourceToken(cancelTip: "a"))).paymentDescription == "a"

    }
}
