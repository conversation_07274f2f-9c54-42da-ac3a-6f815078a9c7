package com.ctrip.corp.bff.hotel.book.common.util

import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @Date 2024/12/10 22:42
 */
class StrategyOfBookingInitUtilTest extends Specification {

    def "isBagashiCustom" () {
        expect:
        StrategyOfBookingInitUtil.isBagashiCustom([new StrategyInfo(strategyKey:"BAGASHI_CUSTOM", "strategyValue":"T")])
        !StrategyOfBookingInitUtil.isBagashiCustom([new StrategyInfo(strategyKey:"BAGASHI_CUSTOM", "strategyValue":"F")])

    }

    def "bagashiLocaleCheckAvailFailInfo" () {
        expect:
        StrategyOfBookingInitUtil.bagashiLocaleCheckAvailFailInfo([new StrategyInfo(strategyKey:"BAGASHI_LOCALE_CHECKAVAIL_FAIL_INFO", "strategyValue":"T")])
        !StrategyOfBookingInitUtil.bagashiLocaleCheckAvailFailInfo([new StrategyInfo(strategyKey:"BAGASHI_LOCALE_CHECKAVAIL_FAIL_INFO", "strategyValue":"F")])

    }

    def "needModifyRcInfo" () {
        expect:
        StrategyOfBookingInitUtil.needModifyRcInfo([new StrategyInfo(strategyKey:"NEED_MODIFY_RC_INFO", "strategyValue":"T")])
        !StrategyOfBookingInitUtil.needModifyRcInfo([new StrategyInfo(strategyKey:"NEED_MODIFY_RC_INFO", "strategyValue":"F")])

    }

    def "testFromBookingInit"() {
        when:
        List<StrategyInfo> strategyInfos = [new StrategyInfo(strategyKey:"BOOKING_INIT", "strategyValue":"T")]
        then:
        Assert.assertTrue(StrategyOfBookingInitUtil.fromBookingInit(strategyInfos))
        Assert.assertFalse(StrategyOfBookingInitUtil.fromBookingInit([new StrategyInfo(strategyKey:"BOOKING_INIT", "strategyValue":"F")]))
        Assert.assertTrue(StrategyOfBookingInitUtil.needArriveTimeInfo([new StrategyInfo(strategyKey:"NEED_ARRIVE_TIME_INFO", "strategyValue":"T")]))
        Assert.assertFalse(StrategyOfBookingInitUtil.needArriveTimeInfo([new StrategyInfo(strategyKey:"NEED_ARRIVE_TIME_INFO", "strategyValue":"F")]))
    }

    def "checkedMas" () {
        when:
        def strategyInfos = [new StrategyInfo(strategyKey:"CHECKED_MAS", "strategyValue":"T")]
        then:
        StrategyOfBookingInitUtil.checkedMas(strategyInfos)
        !StrategyOfBookingInitUtil.checkedMas([new StrategyInfo(strategyKey:"CHECKED_MAS", "strategyValue":"F")])
        !StrategyOfBookingInitUtil.checkedMas([])
    }

    def "cancelDescWithoutPaymentDesc" () {
        expect:
        StrategyOfBookingInitUtil.cancelDescWithoutPaymentDesc([new StrategyInfo(strategyKey:"CANCEL_DESC_WITHOUT_PAYMENT_DESC", "strategyValue":"T")])
        !StrategyOfBookingInitUtil.cancelDescWithoutPaymentDesc([new StrategyInfo(strategyKey:"CANCEL_DESC_WITHOUT_PAYMENT_DESC", "strategyValue":"F")])
    }

    def "getFirstNation" () {
        when:
        def strategyInfos = [new StrategyInfo(strategyKey:"FIRST_NATION", "strategyValue":"T")]
        then:
        StrategyOfBookingInitUtil.getFirstNation(strategyInfos) == "T"
        StrategyOfBookingInitUtil.getFirstNation([new StrategyInfo(strategyKey:"FIRST_NATION", "strategyValue":"F")]) == "F"
        StrategyOfBookingInitUtil.getFirstNation([]) == null
    }

    def "useFirstNation" () {
        expect:
        StrategyOfBookingInitUtil.useFirstNation([new StrategyInfo(strategyKey:"FIRST_NATION", "strategyValue":"T")])
        !StrategyOfBookingInitUtil.useFirstNation([new StrategyInfo(strategyKey:"NEED_CITY_BASE_INFO", "strategyValue":"T")])

    }

    def "needCityBaseInfo" () {
        expect:
        StrategyOfBookingInitUtil.needCityBaseInfo([new StrategyInfo(strategyKey:"NEED_CITY_BASE_INFO", "strategyValue":"T")])
        !StrategyOfBookingInitUtil.needCityBaseInfo([new StrategyInfo(strategyKey:"NEED_CITY_BASE_INFO", "strategyValue":"F")])
    }

    def "isBlueSpacePreChargeService" () {
        expect:
        StrategyOfBookingInitUtil.isBlueSpacePreChargeService([new StrategyInfo(strategyKey:"BLUE_SPACE_PRE_CHARGE_SERVICE", "strategyValue":"T")])
        !StrategyOfBookingInitUtil.isBlueSpacePreChargeService([new StrategyInfo(strategyKey:"BLUE_SPACE_PRE_CHARGE_SERVICE", "strategyValue":"F")])
    }

    def "needRcInfo" () {
        expect:
        StrategyOfBookingInitUtil.needRcInfo([new StrategyInfo(strategyKey:"NEED_RC_INFO", "strategyValue":"T")])
        !StrategyOfBookingInitUtil.needRcInfo([new StrategyInfo(strategyKey:"NEED_RC_INFO", "strategyValue":"F")])
    }

    def "needFirstPassengerMembershipCard" () {
        expect:
        StrategyOfBookingInitUtil.needFirstPassengerMembershipCard([new StrategyInfo(strategyKey:"FIRST_PASSENGER_MEMBERSHIP_CARD", "strategyValue":"T")])
        !StrategyOfBookingInitUtil.needFirstPassengerMembershipCard([new StrategyInfo(strategyKey:"FIRST_PASSENGER_MEMBERSHIP_CARD", "strategyValue":"F")])
    }

    def "enAndAccountRequireContactEmail" () {
        expect:
        StrategyOfBookingInitUtil.enAndAccountRequireContactEmail([new StrategyInfo(strategyKey:"EN_AND_ACCOUNT_REQUIRE_CONTACT_EMAIL", "strategyValue":"T")])
        !StrategyOfBookingInitUtil.enAndAccountRequireContactEmail([new StrategyInfo(strategyKey:"EN_AND_ACCOUNT_REQUIRE_CONTACT_EMAIL", "strategyValue":"F")])
    }

    def "needPriceStarOverStandard" () {
        expect:
        StrategyOfBookingInitUtil.needPriceStarOverStandard([new StrategyInfo(strategyKey:"PRICE_STAR_OVER_STANDARD", "strategyValue":"T")])
        !StrategyOfBookingInitUtil.needPriceStarOverStandard([new StrategyInfo(strategyKey:"PRICE_STAR_OVER_STANDARD", "strategyValue":"F")])
    }

    def "isGuestNameLanguageAggResult" () {
        expect:
        StrategyOfBookingInitUtil.isGuestNameLanguageAggResult([new StrategyInfo(strategyKey:"GUEST_NAME_LANGUAGE_AGG_RESULT", "strategyValue":"T")])
        !StrategyOfBookingInitUtil.isGuestNameLanguageAggResult([new StrategyInfo(strategyKey:"GUEST_NAME_LANGUAGE_AGG_RESULT", "strategyValue":"F")])
    }

    def "needContractCountryCodeLimitInfo" () {
        expect:
        StrategyOfBookingInitUtil.needContractCountryCodeLimitInfo([new StrategyInfo(strategyKey:"CONTRACT_COUNTRY_CODE_LIMIT", "strategyValue":"T")])
        !StrategyOfBookingInitUtil.needContractCountryCodeLimitInfo([new StrategyInfo(strategyKey:"CONTRACT_COUNTRY_CODE_LIMIT", "strategyValue":"F")])
    }

    def "needNationalityRestriction" () {
        expect:
        StrategyOfBookingInitUtil.needNationalityRestriction([new StrategyInfo(strategyKey:"NATIONALITY_RESTRICTION", "strategyValue":"T")])
        !StrategyOfBookingInitUtil.needNationalityRestriction([new StrategyInfo(strategyKey:"NATIONALITY_RESTRICTION", "strategyValue":"F")])
    }

    def "sortPayInfoList" () {
        expect:
        StrategyOfBookingInitUtil.sortPayInfoList([new StrategyInfo(strategyKey:"SORT_PAY_INFO_LIST", "strategyValue":"T")])
        !StrategyOfBookingInitUtil.sortPayInfoList([new StrategyInfo(strategyKey:"SORT_PAY_INFO_LIST", "strategyValue":"F")])

    }


    @Unroll
    def "testRelativeNotify with #description"() {
        given: "A mocked strategyInfoMap"
        def strategyInfoMap = strategyInfoMapInput

        when: "Calling relativeNotify"
        def result = StrategyOfBookingInitUtil.relativeNotify(strategyInfoMap)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                               | strategyInfoMapInput                                                                    || expectedResult
        "RELATIVE_NOTIFY key exists with value T" | [RELATIVE_NOTIFY: new StrategyInfo(strategyKey: "RELATIVE_NOTIFY", strategyValue: "T")] || true
        "RELATIVE_NOTIFY key exists with value F" | [RELATIVE_NOTIFY: new StrategyInfo(strategyKey: "RELATIVE_NOTIFY", strategyValue: "F")] || false
        "RELATIVE_NOTIFY key does not exist"      | [:]                                                                                     || false
    }

    @Unroll
    def "tripNeedSubProductLine with #description"() {
        given: "A mocked strategyInfoMap"
        def strategyInfoMap = strategyInfoMapInput

        when: "Calling relativeNotify"
        def result = StrategyOfBookingInitUtil.tripNeedSubProductLine(strategyInfoMap)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                                          | strategyInfoMapInput                                                                                          || expectedResult
        "TRIP_NEED_SUB_PRODUCT_LINE key exists with value T" | [TRIP_NEED_SUB_PRODUCT_LINE: new StrategyInfo(strategyKey: "TRIP_NEED_SUB_PRODUCT_LINE", strategyValue: "T")] || true
        "TRIP_NEED_SUB_PRODUCT_LINE key exists with value F" | [TRIP_NEED_SUB_PRODUCT_LINE: new StrategyInfo(strategyKey: "TRIP_NEED_SUB_PRODUCT_LINE", strategyValue: "F")] || false
        "TRIP_NEED_SUB_PRODUCT_LINE key does not exist"      | [:]                                                                                                           || false
    }

    @Unroll
    def "relativeRecall with #description"() {
        given: "A mocked strategyInfoMap"
        def strategyInfoMap = strategyInfoMapInput

        when: "Calling relativeNotify"
        def result = StrategyOfBookingInitUtil.relativeRecall(strategyInfoMap)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                               | strategyInfoMapInput                                                                    || expectedResult
        "RELATIVE_RECALL key exists with value T" | [RELATIVE_RECALL: new StrategyInfo(strategyKey: "RELATIVE_RECALL", strategyValue: "T")] || true
        "RELATIVE_RECALL key exists with value F" | [RELATIVE_RECALL: new StrategyInfo(strategyKey: "RELATIVE_RECALL", strategyValue: "F")] || false
        "RELATIVE_RECALL key does not exist"      | [:]                                                                                     || false
    }

    @Unroll
    def "verifyApprovalResultUseAggDetail with #description"() {
        given: "A mocked strategyInfoMap"
        def strategyInfoMap = strategyInfoMapInput

        when: "Calling relativeNotify"
        def result = StrategyOfBookingInitUtil.verifyApprovalResultUseAggDetail(strategyInfoMap)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                                                     | strategyInfoMapInput                                                                                                                || expectedResult
        "VERIFY_APPROVAL_RESULT_USE_AGG_DETAIL key exists with value T" | [VERIFY_APPROVAL_RESULT_USE_AGG_DETAIL: new StrategyInfo(strategyKey: "VERIFY_APPROVAL_RESULT_USE_AGG_DETAIL", strategyValue: "T")] || true
        "VERIFY_APPROVAL_RESULT_USE_AGG_DETAIL key exists with value F" | [VERIFY_APPROVAL_RESULT_USE_AGG_DETAIL: new StrategyInfo(strategyKey: "VERIFY_APPROVAL_RESULT_USE_AGG_DETAIL", strategyValue: "F")] || false
        "VERIFY_APPROVAL_RESULT_USE_AGG_DETAIL key does not exist"      | [:]                                                                                                                                 || false
    }

    @Unroll
    def "notRequiredConfirmOrder with #description"() {
        given: "A mocked strategyInfoMap"
        def strategyInfoMap = strategyInfoMapInput

        when: "Calling relativeNotify"
        def result = StrategyOfBookingInitUtil.notRequiredConfirmOrder(strategyInfoMap)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                                          | strategyInfoMapInput                                                                                          || expectedResult
        "NOT_REQUIRED_CONFIRM_ORDER key exists with value T" | [NOT_REQUIRED_CONFIRM_ORDER: new StrategyInfo(strategyKey: "NOT_REQUIRED_CONFIRM_ORDER", strategyValue: "T")] || true
        "NOT_REQUIRED_CONFIRM_ORDER key exists with value F" | [NOT_REQUIRED_CONFIRM_ORDER: new StrategyInfo(strategyKey: "NOT_REQUIRED_CONFIRM_ORDER", strategyValue: "F")] || false
        "NOT_REQUIRED_CONFIRM_ORDER key does not exist"      | [:]                                                                                                           || false
    }

    @Unroll
    def "tripPassNeedSingleApproval with #description"() {
        given: "A mocked strategyInfoMap"
        def strategyInfoMap = strategyInfoMapInput

        when: "Calling relativeNotify"
        def result = StrategyOfBookingInitUtil.tripPassNeedSingleApproval(strategyInfoMap)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                                              | strategyInfoMapInput                                                                                                  || expectedResult
        "TRIP_PASS_NEED_SINGLE_APPROVAL key exists with value T" | [TRIP_PASS_NEED_SINGLE_APPROVAL: new StrategyInfo(strategyKey: "TRIP_PASS_NEED_SINGLE_APPROVAL", strategyValue: "T")] || true
        "TRIP_PASS_NEED_SINGLE_APPROVAL key exists with value F" | [TRIP_PASS_NEED_SINGLE_APPROVAL: new StrategyInfo(strategyKey: "TRIP_PASS_NEED_SINGLE_APPROVAL", strategyValue: "F")] || false
        "TRIP_PASS_NEED_SINGLE_APPROVAL key does not exist"      | [:]                                                                                                                   || false
    }


    @Unroll
    def "personalAccountByAms with #description"() {
        given: "A mocked strategyInfoMap"
        def strategyInfoMap = strategyInfoMapInput

        when: "Calling relativeNotify"
        def result = StrategyOfBookingInitUtil.personalAccountByAms(strategyInfoMap)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                                       | strategyInfoMapInput                                                                                    || expectedResult
        "PERSONAL_ACCOUNT_BY_AMS key exists with value T" | [PERSONAL_ACCOUNT_BY_AMS: new StrategyInfo(strategyKey: "PERSONAL_ACCOUNT_BY_AMS", strategyValue: "T")] || true
        "PERSONAL_ACCOUNT_BY_AMS key exists with value F" | [PERSONAL_ACCOUNT_BY_AMS: new StrategyInfo(strategyKey: "PERSONAL_ACCOUNT_BY_AMS", strategyValue: "F")] || false
        "PERSONAL_ACCOUNT_BY_AMS key does not exist"      | [:]                                                                                                     || false
    }

    @Unroll
    def "bookingWithPersonalAccount with #description"() {
        given: "A mocked strategyInfoMap"
        def strategyInfoMap = strategyInfoMapInput

        when: "Calling relativeNotify"
        def result = StrategyOfBookingInitUtil.bookingWithPersonalAccount(strategyInfoMap)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                                             | strategyInfoMapInput                                                                                                || expectedResult
        "BOOKING_WITH_PERSONAL_ACCOUNT key exists with value T" | [BOOKING_WITH_PERSONAL_ACCOUNT: new StrategyInfo(strategyKey: "BOOKING_WITH_PERSONAL_ACCOUNT", strategyValue: "T")] || true
        "BOOKING_WITH_PERSONAL_ACCOUNT key exists with value F" | [BOOKING_WITH_PERSONAL_ACCOUNT: new StrategyInfo(strategyKey: "BOOKING_WITH_PERSONAL_ACCOUNT", strategyValue: "F")] || false
        "BOOKING_WITH_PERSONAL_ACCOUNT key does not exist"      | [:]                                                                                                                 || false
    }

    @Unroll
    def "needInvoice with #description"() {
        given: "A mocked strategyInfoMap"
        def strategyInfoMap = strategyInfoMapInput

        when: "Calling relativeNotify"
        def result = StrategyOfBookingInitUtil.needInvoice(strategyInfoMap)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                               | strategyInfoMapInput                                                                    || expectedResult
        "NO_NEED_INVOICE key exists with value T" | [NO_NEED_INVOICE: new StrategyInfo(strategyKey: "NO_NEED_INVOICE", strategyValue: "F")] || true
        "NO_NEED_INVOICE key exists with value F" | [NO_NEED_INVOICE: new StrategyInfo(strategyKey: "NO_NEED_INVOICE", strategyValue: "T")] || false
        "NO_NEED_INVOICE key does not exist"      | [:]                                                                                     || true
    }

    @Unroll
    def "buildStrategyInfoMap"() {
        given:
        when:
        def result = StrategyOfBookingInitUtil.buildStrategyInfoMap([
                new StrategyInfo(strategyKey: "strategyKey1", strategyValue: "strategyValue1"),
                new StrategyInfo(strategyKey: "strategyKey1", strategyValue: "strategyValue11"),
                new StrategyInfo(strategyKey: "strategyKey2", strategyValue: "strategyValue2")
        ])
        then:
        result != null
        result.size() == 2
        result.get("strategyKey1").getStrategyValue() == "strategyValue11"
        result.get("strategyKey2").getStrategyValue() == "strategyValue2"
    }

    @Unroll
    def "test hotelCheckAvail with Map parameter - #scenario"() {
        given:
        Map<String, StrategyInfo> strategyInfoMap = strategyMap

        when:
        boolean result = StrategyOfBookingInitUtil.hotelCheckAvail(strategyInfoMap)

        then:
        result == expectedResult

        where:
        scenario                           | strategyMap                                                                                    | expectedResult
        "empty map"                        | [:]                                                                                            | false
        "map without HOTEL_CHECK_AVAIL"    | ["OTHER_KEY": new StrategyInfo(strategyKey: "OTHER_KEY", strategyValue: "T")]                 | false
        "map with HOTEL_CHECK_AVAIL = T"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "T")] | true
        "map with HOTEL_CHECK_AVAIL = t"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "t")] | true
        "map with HOTEL_CHECK_AVAIL = F"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "F")] | false
        "map with HOTEL_CHECK_AVAIL = null"| ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: null)] | false
        "map with null StrategyInfo"       | ["HOTEL_CHECK_AVAIL": null]                                                                    | false
    }

    @Unroll
    def "test needApprovalTextInfo - #scenario"() {
        given:
        Map<String, StrategyInfo> strategyInfoMap = strategyMap

        when:
        boolean result = BookingInitProcessorOfUtil.needApprovalTextInfo(strategyInfoMap)

        then:
        result == expectedResult

        where:
        scenario                           | strategyMap                                                                                    | expectedResult
        "null map"                         | null                                                                                           | true
        "empty map"                        | [:]                                                                                            | true
        "map without HOTEL_CHECK_AVAIL"    | ["OTHER_KEY": new StrategyInfo(strategyKey: "OTHER_KEY", strategyValue: "T")]                 | true
        "map with HOTEL_CHECK_AVAIL = T"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "T")] | false
        "map with HOTEL_CHECK_AVAIL = t"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "t")] | false
        "map with HOTEL_CHECK_AVAIL = F"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "F")] | true
        "map with HOTEL_CHECK_AVAIL = null"| ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: null)] | true
        "map with null StrategyInfo"       | ["HOTEL_CHECK_AVAIL": null]                                                                    | true
    }

    @Unroll
    def "test needPayConfig - #scenario"() {
        given:
        Map<String, StrategyInfo> strategyInfoMap = strategyMap

        when:
        boolean result = BookingInitProcessorOfUtil.needPayConfig(strategyInfoMap)

        then:
        result == expectedResult

        where:
        scenario                           | strategyMap                                                                                    | expectedResult
        "null map"                         | null                                                                                           | true
        "empty map"                        | [:]                                                                                            | true
        "map without HOTEL_CHECK_AVAIL"    | ["OTHER_KEY": new StrategyInfo(strategyKey: "OTHER_KEY", strategyValue: "T")]                 | true
        "map with HOTEL_CHECK_AVAIL = T"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "T")] | false
        "map with HOTEL_CHECK_AVAIL = t"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "t")] | false
        "map with HOTEL_CHECK_AVAIL = F"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "F")] | true
        "map with HOTEL_CHECK_AVAIL = null"| ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: null)] | true
        "map with null StrategyInfo"       | ["HOTEL_CHECK_AVAIL": null]                                                                    | true
    }

    @Unroll
    def "test needGetContactInvoiceDefaultInfo - #scenario"() {
        given:
        Map<String, StrategyInfo> strategyInfoMap = strategyMap

        when:
        boolean result = BookingInitProcessorOfUtil.needGetContactInvoiceDefaultInfo(strategyInfoMap)

        then:
        result == expectedResult

        where:
        scenario                           | strategyMap                                                                                    | expectedResult
        "null map"                         | null                                                                                           | true
        "empty map"                        | [:]                                                                                            | true
        "map without HOTEL_CHECK_AVAIL"    | ["OTHER_KEY": new StrategyInfo(strategyKey: "OTHER_KEY", strategyValue: "T")]                 | true
        "map with HOTEL_CHECK_AVAIL = T"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "T")] | false
        "map with HOTEL_CHECK_AVAIL = t"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "t")] | false
        "map with HOTEL_CHECK_AVAIL = F"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "F")] | true
        "map with HOTEL_CHECK_AVAIL = null"| ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: null)] | true
        "map with null StrategyInfo"       | ["HOTEL_CHECK_AVAIL": null]                                                                    | true
    }

    @Unroll
    def "test needGetAuthDelayConfig - #scenario"() {
        given:
        Map<String, StrategyInfo> strategyInfoMap = strategyMap

        when:
        boolean result = BookingInitProcessorOfUtil.needGetAuthDelayConfig(strategyInfoMap)

        then:
        result == expectedResult

        where:
        scenario                           | strategyMap                                                                                    | expectedResult
        "null map"                         | null                                                                                           | true
        "empty map"                        | [:]                                                                                            | true
        "map without HOTEL_CHECK_AVAIL"    | ["OTHER_KEY": new StrategyInfo(strategyKey: "OTHER_KEY", strategyValue: "T")]                 | true
        "map with HOTEL_CHECK_AVAIL = T"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "T")] | false
        "map with HOTEL_CHECK_AVAIL = t"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "t")] | false
        "map with HOTEL_CHECK_AVAIL = F"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "F")] | true
        "map with HOTEL_CHECK_AVAIL = null"| ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: null)] | true
        "map with null StrategyInfo"       | ["HOTEL_CHECK_AVAIL": null]                                                                    | true
    }

    @Unroll
    def "test needQueryBizModeBindRelation - #scenario"() {
        given:
        Map<String, StrategyInfo> strategyInfoMap = strategyMap

        when:
        boolean result = BookingInitProcessorOfUtil.needQueryBizModeBindRelation(strategyInfoMap)

        then:
        result == expectedResult

        where:
        scenario                           | strategyMap                                                                                    | expectedResult
        "null map"                         | null                                                                                           | true
        "empty map"                        | [:]                                                                                            | true
        "map without HOTEL_CHECK_AVAIL"    | ["OTHER_KEY": new StrategyInfo(strategyKey: "OTHER_KEY", strategyValue: "T")]                 | true
        "map with HOTEL_CHECK_AVAIL = T"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "T")] | false
        "map with HOTEL_CHECK_AVAIL = t"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "t")] | false
        "map with HOTEL_CHECK_AVAIL = F"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "F")] | true
        "map with HOTEL_CHECK_AVAIL = null"| ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: null)] | true
        "map with null StrategyInfo"       | ["HOTEL_CHECK_AVAIL": null]                                                                    | true
    }

    @Unroll
    def "test needGetGroupVipPackage - #scenario"() {
        given:
        Map<String, StrategyInfo> strategyInfoMap = strategyMap

        when:
        boolean result = BookingInitProcessorOfUtil.needGetGroupVipPackage(strategyInfoMap)

        then:
        result == expectedResult

        where:
        scenario                           | strategyMap                                                                                    | expectedResult
        "null map"                         | null                                                                                           | true
        "empty map"                        | [:]                                                                                            | true
        "map without HOTEL_CHECK_AVAIL"    | ["OTHER_KEY": new StrategyInfo(strategyKey: "OTHER_KEY", strategyValue: "T")]                 | true
        "map with HOTEL_CHECK_AVAIL = T"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "T")] | false
        "map with HOTEL_CHECK_AVAIL = t"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "t")] | false
        "map with HOTEL_CHECK_AVAIL = F"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "F")] | true
        "map with HOTEL_CHECK_AVAIL = null"| ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: null)] | true
        "map with null StrategyInfo"       | ["HOTEL_CHECK_AVAIL": null]                                                                    | true
    }

    @Unroll
    def "test needCalculateTravelRewards - #scenario"() {
        given:
        Map<String, StrategyInfo> strategyInfoMap = strategyMap

        when:
        boolean result = BookingInitProcessorOfUtil.needCalculateTravelRewards(strategyInfoMap)

        then:
        result == expectedResult

        where:
        scenario                           | strategyMap                                                                                    | expectedResult
        "null map"                         | null                                                                                           | true
        "empty map"                        | [:]                                                                                            | true
        "map without HOTEL_CHECK_AVAIL"    | ["OTHER_KEY": new StrategyInfo(strategyKey: "OTHER_KEY", strategyValue: "T")]                 | true
        "map with HOTEL_CHECK_AVAIL = T"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "T")] | false
        "map with HOTEL_CHECK_AVAIL = t"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "t")] | false
        "map with HOTEL_CHECK_AVAIL = F"   | ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "F")] | true
        "map with HOTEL_CHECK_AVAIL = null"| ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: null)] | true
        "map with null StrategyInfo"       | ["HOTEL_CHECK_AVAIL": null]                                                                    | true
    }


    @Unroll
    def "cityWithinPointOfSale with #description"() {
        given: "A mocked strategyInfoMap"
        def strategyInfoMap = strategyInfoMapInput

        when: "Calling relativeNotify"
        def result = StrategyOfBookingInitUtil.cityWithinPointOfSale(strategyInfoMap)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                                         | strategyInfoMapInput                                                                                        || expectedResult
        "CITY_WITHIN_POINT_OF_SALE key exists with value T" | [CITY_WITHIN_POINT_OF_SALE: new StrategyInfo(strategyKey: "CITY_WITHIN_POINT_OF_SALE", strategyValue: "T")] || true
        "CITY_WITHIN_POINT_OF_SALE key exists with value F" | [CITY_WITHIN_POINT_OF_SALE: new StrategyInfo(strategyKey: "CITY_WITHIN_POINT_OF_SALE", strategyValue: "F")] || false
        "CITY_WITHIN_POINT_OF_SALE key does not exist"      | [:]                                                                                                         || false
    }

    @Unroll
    def "blockMatchOveStandardStageApprovalFlow with #description"() {
        given: "A mocked strategyInfoMap"
        def strategyInfoMap = strategyInfoMapInput

        when: "Calling relativeNotify"
        def result = StrategyOfBookingInitUtil.blockMatchOveStandardStageApprovalFlow(strategyInfoMap)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                                                             | strategyInfoMapInput                                                                                                                                || expectedResult
        "BLOCK_MATCH_OVER_STANDARD_STAGE_APPROVAL_FLOW key exists with value T" | [BLOCK_MATCH_OVER_STANDARD_STAGE_APPROVAL_FLOW: new StrategyInfo(strategyKey: "BLOCK_MATCH_OVER_STANDARD_STAGE_APPROVAL_FLOW", strategyValue: "T")] || true
        "BLOCK_MATCH_OVER_STANDARD_STAGE_APPROVAL_FLOW key exists with value F" | [BLOCK_MATCH_OVER_STANDARD_STAGE_APPROVAL_FLOW: new StrategyInfo(strategyKey: "BLOCK_MATCH_OVER_STANDARD_STAGE_APPROVAL_FLOW", strategyValue: "F")] || false
        "BLOCK_MATCH_OVER_STANDARD_STAGE_APPROVAL_FLOW key does not exist"      | [:]                                                                                                                                                 || false
    }


    @Unroll
    def "blockRetrieveTicket with #description"() {
        given: "A mocked strategyInfoMap"
        def strategyInfoMap = strategyInfoMapInput

        when: "Calling relativeNotify"
        def result = StrategyOfBookingInitUtil.blockRetrieveTicket(strategyInfoMap)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                                     | strategyInfoMapInput                                                                                    || expectedResult
        "BLOCK_RETRIEVE_TICKET key exists with value T" | [BLOCK_RETRIEVE_TICKET: new StrategyInfo(strategyKey: "BLOCK_RETRIEVE_TICKET", strategyValue: "T")] || true
        "BLOCK_RETRIEVE_TICKET key exists with value F" | [BLOCK_RETRIEVE_TICKET: new StrategyInfo(strategyKey: "BLOCK_RETRIEVE_TICKET", strategyValue: "F")] || false
        "BLOCK_RETRIEVE_TICKET key does not exist"      | [:]                                                                                                     || false
    }

    @Unroll
    def "hotelCheckAvail with #description"() {
        given: "A mocked strategyInfoMap"
        def strategyInfoMap = strategyInfoMapInput

        when: "Calling relativeNotify"
        def result = StrategyOfBookingInitUtil.hotelCheckAvail(strategyInfoMap)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                                 | strategyInfoMapInput                                                                        || expectedResult
        "HOTEL_CHECK_AVAIL key exists with value T" | [HOTEL_CHECK_AVAIL: new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "T")] || true
        "HOTEL_CHECK_AVAIL key exists with value F" | [HOTEL_CHECK_AVAIL: new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "F")] || false
        "HOTEL_CHECK_AVAIL key does not exist"      | [:]                                                                                         || false
    }

    @Unroll
    def "useHotelBookInputNew with #description"() {
        given: "A mocked strategyInfoMap"
        def strategyInfoMap = strategyInfoMapInput

        when: "Calling relativeNotify"
        def result = StrategyOfBookingInitUtil.useHotelBookInputNew(strategyInfoMap)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                                    | strategyInfoMapInput                                                                                  || expectedResult
        "HOTEL_BOOK_INPUT_NEW key exists with value T" | [HOTEL_BOOK_INPUT_NEW: new StrategyInfo(strategyKey: "HOTEL_BOOK_INPUT_NEW", strategyValue: "T")] || true
        "HOTEL_BOOK_INPUT_NEW key exists with value F" | [HOTEL_BOOK_INPUT_NEW: new StrategyInfo(strategyKey: "HOTEL_BOOK_INPUT_NEW", strategyValue: "F")] || false
        "HOTEL_BOOK_INPUT_NEW key does not exist"      | [:]                                                                                                   || false
    }
    @Unroll
    def "approvalReuseReBook with #description"() {
        given: "模拟 strategyInfoMap"
        def strategyInfoMap = strategyInfoMapInput

        when: "调用 approvalReuseReBook"
        def result = StrategyOfBookingInitUtil.approvalReuseReBook(strategyInfoMap)

        then: "结果应与预期一致"
        result == expectedResult

        where:
        description                                                    | strategyInfoMapInput                                                                                                 || expectedResult
        "BOOKING_SCENARIO=APPROVAL_CONTINUED_USE"                     | [BOOKING_SCENARIO: new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "APPROVAL_CONTINUED_USE")]      || true
        "BOOKING_SCENARIO=其他值"                                     | [BOOKING_SCENARIO: new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "OTHER")]                      || false
        "BOOKING_SCENARIO 不存在"                                     | [:]                                                                                                                   || false
    }

    @Unroll
    def "approvalFlowReuseNew with #description"() {
        given: "模拟 strategyInfoMap"
        def strategyInfoMap = strategyInfoMapInput

        when: "调用 approvalFlowReuseNew"
        def result = StrategyOfBookingInitUtil.approvalFlowReuseNew(strategyInfoMap)

        then: "结果应与预期一致"
        result == expectedResult

        where:
        description                                 | strategyInfoMapInput                                                                                      || expectedResult
        "APPROVAL_FLOW_REUSE_NEW=\"T\""         | [APPROVAL_FLOW_REUSE_NEW: new StrategyInfo(strategyKey: "APPROVAL_FLOW_REUSE_NEW", strategyValue: "T")] || true
        "APPROVAL_FLOW_REUSE_NEW=\"F\""         | [APPROVAL_FLOW_REUSE_NEW: new StrategyInfo(strategyKey: "APPROVAL_FLOW_REUSE_NEW", strategyValue: "F")] || false
        "APPROVAL_FLOW_REUSE_NEW 不存在"           | [:]                                                                                                      || false
    }

    @Unroll
    def "approvalFlowReuseAiModify with #description"() {
        given: "模拟 strategyInfoMap"
        def strategyInfoMap = strategyInfoMapInput

        when: "调用 approvalFlowReuseAiModify"
        def result = StrategyOfBookingInitUtil.approvalFlowReuseAiModify(strategyInfoMap)

        then: "结果应与预期一致"
        result == expectedResult

        where:
        description                                        | strategyInfoMapInput                                                                                           || expectedResult
        "APPROVAL_FLOW_REUSE_AI_MODIFY=\"T\""         | [APPROVAL_FLOW_REUSE_AI_MODIFY: new StrategyInfo(strategyKey: "APPROVAL_FLOW_REUSE_AI_MODIFY", strategyValue: "T")] || true
        "APPROVAL_FLOW_REUSE_AI_MODIFY=\"F\""         | [APPROVAL_FLOW_REUSE_AI_MODIFY: new StrategyInfo(strategyKey: "APPROVAL_FLOW_REUSE_AI_MODIFY", strategyValue: "F")] || false
        "APPROVAL_FLOW_REUSE_AI_MODIFY 不存在"           | [:]                                                                                                             || false
    }

    @Unroll
    def "strategyValueMatch with #description"() {
        given: "准备参数"
        def method = StrategyOfBookingInitUtil.getDeclaredMethod("strategyValueMatch", Map, String, String)
        method.setAccessible(true)

        when: "调用私有方法"
        def result = method.invoke(null, strategyInfos, key, value)

        then: "结果应与预期一致"
        result == expectedResult

        where:
        description                        | strategyInfos                                                                                  | key                  | value                        || expectedResult
        "strategyInfos为null"              | null                                                                                           | "BOOKING_SCENARIO"  | "APPROVAL_CONTINUED_USE"     || false
        "strategyInfos为空"                | [:]                                                                                            | "BOOKING_SCENARIO"  | "APPROVAL_CONTINUED_USE"     || false
        "key为null"                        | [BOOKING_SCENARIO: new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "T")]   | null                 | "T"                         || false
        "key为空串"                        | [BOOKING_SCENARIO: new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "T")]   | ""                  | "T"                         || false
        "value为null"                      | [BOOKING_SCENARIO: new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "T")]   | "BOOKING_SCENARIO"  | null                         || false
        "value为空串"                      | [BOOKING_SCENARIO: new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "T")]   | "BOOKING_SCENARIO"  | ""                          || false
        "strategyInfos.get(key)为null"     | [OTHER_KEY: new StrategyInfo(strategyKey: "OTHER_KEY", strategyValue: "T")]                | "BOOKING_SCENARIO"  | "T"                         || false
        "value.equalsIgnoreCase匹配"        | [BOOKING_SCENARIO: new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "T")]   | "BOOKING_SCENARIO"  | "T"                         || true
        "value.equalsIgnoreCase不匹配"      | [BOOKING_SCENARIO: new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "F")]   | "BOOKING_SCENARIO"  | "T"                         || false
    }

    @Unroll
    def "applyModify with #description"() {
        given: "A list of StrategyInfo"
        def strategyInfos = strategyInfosInput

        when: "Calling applyModify"
        def result = StrategyOfBookingInitUtil.applyModify(strategyInfos)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                                 | strategyInfosInput                                                                 || expectedResult
        "List contains STRATEGY_VALUE_EXTEND"       | [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "EXTEND")]       || true
        "List contains STRATEGY_VALUE_APPLY_MODIFY" | [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "APPLY_MODIFY")] || true
        "List contains neither value"               | [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "OTHER")]        || false
        "List is empty"                             | []                                                                                 || false
    }

    @Unroll
    def "applyModifyOnly with #description"() {
        given: "A map of StrategyInfo"
        def strategyInfoMap = strategyInfoMapInput

        when: "Calling applyModifyOnly"
        def result = StrategyOfBookingInitUtil.applyModifyOnly(strategyInfoMap)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                                | strategyInfoMapInput                                                                                   || expectedResult
        "Map contains STRATEGY_VALUE_APPLY_MODIFY" | ["BOOKING_SCENARIO": new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "APPLY_MODIFY")] || true
        "Map contains other value"                 | ["BOOKING_SCENARIO": new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "OTHER")]        || false
        "Map is empty"                             | [:]                                                                                                    || false
        "Map is null"                              | null                                                                                                   || false
    }

    @Unroll
    def "applyModify MAP INPUT with #description"() {
        given: "A map of StrategyInfo"
        def strategyInfoMap = strategyInfoMapInput

        when: "Calling applyModify"
        def result = StrategyOfBookingInitUtil.applyModify(strategyInfoMap)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                                | strategyInfoMapInput                                                                                   || expectedResult
        "Map contains STRATEGY_VALUE_EXTEND"       | ["BOOKING_SCENARIO": new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "EXTEND")]       || true
        "Map contains STRATEGY_VALUE_APPLY_MODIFY" | ["BOOKING_SCENARIO": new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "APPLY_MODIFY")] || true
        "Map contains other value"                 | ["BOOKING_SCENARIO": new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "OTHER")]        || false
        "Map is empty"                             | [:]                                                                                                    || false
        "Key BOOKING_SCENARIO is null"             | ["BOOKING_SCENARIO": null]                                                                             || false
    }
}
