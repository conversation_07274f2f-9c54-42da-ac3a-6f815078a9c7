package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalFlowReuseInput
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification

class MapperOfGetOrderFoundationDataRequestTypeTest extends Specification{
    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }
    def mapper = new MapperOfGetOrderFoundationDataRequestType()

    def "convert 正常流程"() {
        given:
        def orderCreateRequestType = Mock(OrderCreateRequestType)
        def approvalFlowReuseInput = Mock(ApprovalFlowReuseInput)
        orderCreateRequestType.getApprovalFlowReuseInput() >> approvalFlowReuseInput
        approvalFlowReuseInput.getArtificialReuseNo() >> "12345"

        when:
        def result = mapper.convert(Tuple1.of(orderCreateRequestType))

        then:
        result != null
        result.getOrderId() == 12345L
    }

    def "convert approvalFlowReuseInput为null抛出NPE"() {
        given:
        def orderCreateRequestType = Mock(OrderCreateRequestType)
        orderCreateRequestType.getApprovalFlowReuseInput() >> null

        when:
        mapper.convert(Tuple1.of(orderCreateRequestType))

        then:
        thrown(NullPointerException)
    }

    def "convert artificialReuseNo为非法字符串 parseLong返回null抛出NPE"() {
        given:
        def orderCreateRequestType = Mock(OrderCreateRequestType)
        def approvalFlowReuseInput = Mock(ApprovalFlowReuseInput)
        orderCreateRequestType.getApprovalFlowReuseInput() >> approvalFlowReuseInput
        approvalFlowReuseInput.getArtificialReuseNo() >> "abc"

        when:
        mapper.convert(Tuple1.of(orderCreateRequestType))

        then:
        thrown(NullPointerException)
    }

    def "check 返回null"() {
        expect:
        mapper.check(Tuple1.of(Mock(OrderCreateRequestType))) == null
    }
}
