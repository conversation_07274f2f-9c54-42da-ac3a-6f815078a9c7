package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;

class MapperOfGetCorpUserInfoDetailListRequestTypeTest extends Specification {

    def tester = new MapperOfGetCorpUserInfoDetailListRequestType()
    def savePoint = new SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        expect:
        tester.convert(null) == null
        tester.convert(Tuple2.of(null, null)) == null
        tester.convert(Tuple2.of(null, ["a"])).uidList == ["a"]
    }
}
