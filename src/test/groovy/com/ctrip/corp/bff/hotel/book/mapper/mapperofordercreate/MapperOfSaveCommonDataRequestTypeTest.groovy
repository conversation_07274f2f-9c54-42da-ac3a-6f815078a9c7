package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.agg.hotel.roomavailable.entity.BookRoomInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.VerifyApprovalBillResultType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.VerifyDockingResultType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.VerifyPassengerDetailType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.VerifyPassengerResultType
import com.ctrip.corp.approve.ws.contract.FlowTmpl
import com.ctrip.corp.approve.ws.contract.MatchApprovalFlowResponseType
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig
import com.ctrip.corp.bff.framework.hotel.common.util.WaitFutureUtil
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.OrderCheckResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ReservationResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ApprovalInfoResult
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.FollowApprovalResult
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.specific.common.entity.costcenter.ApprovalNoInfoType
import com.ctrip.corp.bff.framework.specific.common.entity.costcenter.CostCenterKeyDto
import com.ctrip.corp.bff.framework.specific.common.entity.costcenter.old.SaveCostCenterInputItem
import com.ctrip.corp.bff.framework.specific.common.utils.CostCenterKeyUtils
import com.ctrip.corp.bff.framework.specific.common.utils.SaveCommonDataCostCenterInfoTypeUtil
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil
import com.ctrip.corp.bff.framework.template.common.serialize.ProtobufSerializerUtil
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalFlowInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.Approver
import com.ctrip.corp.bff.framework.template.entity.contract.integration.AttachmentInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.MiceInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.TripInput
import com.ctrip.corp.bff.hotel.book.common.enums.DistinguishReservationEnum
import com.ctrip.corp.bff.hotel.book.common.enums.RoomTypeEnum
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfSaveCommonData
import com.ctrip.corp.bff.hotel.book.contract.CostCenterInfo
import com.ctrip.corp.bff.hotel.book.contract.FollowApprovalInfoInput
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.contract.ReservationInfo
import com.ctrip.corp.bff.hotel.book.qconfig.QConfigOfCodeMappingConfig
import com.ctrip.corp.bff.profile.contract.SSOBaseInfo
import com.ctrip.corp.bff.profile.contract.SSODingInfo
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryResponseType
import com.ctrip.corp.bff.specific.contract.ApprovalFlowComputeResponseType
import com.ctrip.corp.bff.specific.contract.ApprovalTextInfoResponseType
import com.ctrip.corp.bff.tools.contract.DataInfo
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType
import com.ctrip.soa._20183.ApprovalInfoType
import com.ctrip.soa._20183.AttachmentInfoType
import com.ctrip.soa._20183.CorpDockingInfoType
import com.ctrip.soa._20183.CostCenterInfoType
import com.ctrip.soa._20183.PrepareApprovalInfoType
import com.ctrip.soa._20183.ReservationInfoType
import com.ctrip.soa._20183.TripInfoType
import com.ctrip.soa._21234.ApprovalInfo
import com.ctrip.soa._21234.CreateTripResponseType
import com.ctrip.soa._21234.SearchTripDetailResponseType
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

import java.time.LocalDateTime
import java.time.ZoneOffset

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/13
 */
class MapperOfSaveCommonDataRequestTypeTest extends Specification{
    def myTestClass = new MapperOfSaveCommonDataRequestType()

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "test getTravelControlPreApprovals"() {
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                hotelPolicyInput: new HotelPolicyInput(
                        policyInput: new PolicyInput(
                                "policyUid" : "policyUid",
                        ),
                        approvalInput: new ApprovalInput(
                                "subApprovalNo" : "subApprovalNo",
                                "masterApprovalNo" : "masterApprovalNo",
                                "emergency" : "T",
                        ),
                ),
                "hotelBookPassengerInputs": [
                        new HotelBookPassengerInput(
                                hotelPassengerInput: new HotelPassengerInput(
                                "uid" : "uid11",
                                "approvalInput" : new ApprovalInput(
                                        "subApprovalNo" : "subApprovalNo11",
                                        "masterApprovalNo" : "masterApprovalNo11",
                                        "emergency" : "T",
                                ),
                                "approvalPassengerId" : "11"),
                                "name" : "name11"
                        )
                ],
                "cityInput" : new CityInput(
                        "cityId" : 24
                )
        )
        CheckTravelPolicyResponseType checkTravelPolicyResponseType = new CheckTravelPolicyResponseType(
                "verifyPassengerResult": new VerifyPassengerResultType(
                        "verifyPassengerDetailList": [
                                new VerifyPassengerDetailType(
                                       "uid" : "uid11",
                                        "verifyResult" : "EMERGENCY"
                                )
                        ]
                )
        )
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(balanceType: "PP"))
        GetCorpUserInfoResponseType getCorpUserInfoResponseType = new GetCorpUserInfoResponseType(
                "name" : "policyName",
                nameENFirstName: "nameENFirstName",
                nameENMiddleName: "nameENMiddleName",
                nameENLastName: "nameENLastName"
        )
        new MockUp<OrderCreateProcessorOfUtil>(){
            @Mock
            public static boolean usePsgEname(Integer cityId, WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
                return true
            }
        }
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo();

        when:
        def result = myTestClass.getTravelControlPreApprovals(
                orderCreateRequestType, checkTravelPolicyResponseType,
                queryCheckAvailContextResponseType, getCorpUserInfoResponseType, checkAvailInfo, null, null)

        then:
        result.size() == 2
        result.get(0).getPreApprovalType() ==  "Policy"
        result.get(0).getPreApprovalNo() == "subApprovalNo"
        result.get(0).getPreApprovalResult() == null
        result.get(0).getUid() == "policyUid"
        result.get(0).getPassengerName() == "nameENLastName/nameENFirstName nameENMiddleName"

        result.get(1).getPreApprovalType() ==  "Client"
        result.get(1).getPreApprovalNo() == "subApprovalNo11"
        result.get(1).getPreApprovalResult() == "3"
        result.get(1).getUid() == "uid11"
        result.get(1).getPassengerNo() == 11
        result.get(1).getPassengerName() == ""
    }

    def "test getMiceInfo"() {
        given:
        MiceInput miceInput = new MiceInput(
                "miceToken" : "miceToken",
                "miceActivityId" : "12345",
        )


        when:
        def result = myTestClass.getMiceInfoType(miceInput)

        then:
        result.getMiceToken() == "miceToken"
        result.getMiceId() == 12345
    }

    def "test getCorpDockingInfoList"() {
        given:
        SSOInfoQueryResponseType ssoInfo = new SSOInfoQueryResponseType(
                "ssoBaseInfo": new SSOBaseInfo(
                        "ssoDingInfo": new SSODingInfo(
                                "dingJourneyBizNo" : "dingJourneyBizNo"
                        )
                )
        );


        when:
        def result = myTestClass.getCorpDockingInfoList(ssoInfo);

        then:
        result.get(0).getDockingOrderId() == "dingJourneyBizNo"
        result.get(0).getDockingSource() == "DingTalk"
    }

    def "test getAttachmentInfoList"() {
        given:
        List<AttachmentInfo> attachmentInfos = [
                new AttachmentInfo(
                        "attachmentName" : "attachmentName1",
                        "attachmentUrl" : "attachmentUrl1",
                ),
                null,
                new AttachmentInfo(
                        "attachmentName" : "attachmentName2",
                        "attachmentUrl" : "attachmentUrl2",
                ),
        ]


        when:
        def result = myTestClass.getAttachmentInfoList(attachmentInfos);

        then:
        result.size() == 2
        result.get(0).getAttachmentName() == "attachmentName1"
        result.get(0).getAttachmentUrl() == "attachmentUrl1"
        result.get(0).getAttachmentType() == "Approval"

        result.get(1).getAttachmentName() == "attachmentName2"
        result.get(1).getAttachmentUrl() == "attachmentUrl2"
        result.get(1).getAttachmentType() == "Approval"
    }

    def "test getReservationInfo"() {
        given:
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                if (corpId == "testclose") {
                    return false
                }
                return true
            }
        }
        CorpPayInfo corpPayInfo = new CorpPayInfo(corpPayType: "public")
        UserInfo userInfo = new UserInfo(corpId: "testopen", userId: "u1")
        IntegrationSoaRequestType integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: userInfo)
        List<HotelBookPassengerInput> hotelBookPassengerInputs = ["u1"].collect {
            new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: it))
        }

        when:
        def result = myTestClass.getReservationInfo(new ReservationInfo(reservationType: "reservationType"), null);

        then:
        result.getType() == "reservationType"

        when:
        result = myTestClass.getReservationInfo(new ReservationInfo(reservationType: ""), new OrderCreateRequestType(
                integrationSoaRequestType: integrationSoaRequestType,
                hotelBookPassengerInputs: hotelBookPassengerInputs,
                corpPayInfo: corpPayInfo
        ));

        then:
        result.getType() == "EmployeeTravel"
    }


    def "test getPrepareApprovalInfo"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                "approvalInput": new ApprovalInput(
                        "subApprovalNo" : "subApprovalNo",
                        "emergency" : "T",
                ),
                "integrationSoaRequestType": new IntegrationSoaRequestType(
                        "transactionID" : "transactionID"
                ),
                "cityInput" : new CityInput(
                        "cityId" : 24
                ),
                "corpPayInfo" : new CorpPayInfo(
                        "corpPayType" : "public"
                )
        )

        CheckTravelPolicyResponseType checkTravelPolicyResponseType = new CheckTravelPolicyResponseType(
                approvalBillControlDimension: "O",
                verifyApprovalBillResult: new VerifyApprovalBillResultType("transactionID" : "transactionID"))

        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {{
                    put("BillType", "A")
                    put("BillControlMode", "A")
                    put("isChkaheadapproveHotelI","T")
                }}))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>()))
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        when:
        def result = myTestClass.getPrepareApprovalInfo(orderCreateRequestType, checkTravelPolicyResponseType, accountInfo);

        then:
        result.getApprovalVerifyId() == "transactionID"
        result.getPrepareApprovalNo() == "subApprovalNo"
        result.getPrepareAuthStatus() == 3


        when:
        accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {{
                    put("BillType", "A")
                    put("BillControlMode", "B")
                    put("isChkaheadapproveHotelI","T")
                }}))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>()))
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        result = myTestClass.getPrepareApprovalInfo(orderCreateRequestType, checkTravelPolicyResponseType, accountInfo);

        then:
        result.getApprovalVerifyId() == "transactionID"
        result.getPrepareApprovalNo() == "subApprovalNo"
        result.getPrepareAuthStatus() == 3

        when:
        accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {{
                    put("BillType", "A")
                    put("BillControlMode", "B")
                    put("isChkaheadapproveHotelI","F")
                }}))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>()))
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        result = myTestClass.getPrepareApprovalInfo(orderCreateRequestType, checkTravelPolicyResponseType, accountInfo);

        then:
        result.getApprovalVerifyId() == "transactionID"
        result.getPrepareApprovalNo() == null
        result.getPrepareAuthStatus() == 3


        when:
        checkTravelPolicyResponseType.verifyApprovalBillResult = null
        accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {{
                    put("BillType", "A")
                    put("BillControlMode", "B")
                    put("isChkaheadapproveHotelI","F")
                }}))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>()))
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        result = myTestClass.getPrepareApprovalInfo(orderCreateRequestType, checkTravelPolicyResponseType, accountInfo);

        then:
        result == null
    }

    @Unroll
    def "test needPrepareApprovalInfo"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                "approvalInput": new ApprovalInput(
                        "subApprovalNo": "subApprovalNo",
                        "emergency": "T",
                ),
                "integrationSoaRequestType": new IntegrationSoaRequestType(
                        "transactionID": "transactionID"
                ),
                "cityInput": new CityInput(
                        "cityId": 24
                ),
                "corpPayInfo": new CorpPayInfo(
                        "corpPayType": "public"
                )
        )

        CheckTravelPolicyResponseType checkTravelPolicyResponseType = new CheckTravelPolicyResponseType(
                verifyApprovalBillResult: verifyApprovalBillResult,
                approvalBillControlDimension: approvalBillControlDimension,
                verifyDockingResult: verifyDockingResult)

        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("BillType", "A")
                        put("BillControlMode", BillControlMode)
                        put("isChkaheadapproveHotelI", isChkaheadapproveHotelI)
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>()))
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        expect:
        result == myTestClass.needPrepareApprovalInfo(orderCreateRequestType, checkTravelPolicyResponseType, accountInfo);

        where:
        billControlMode | isChkaheadapproveHotelI | verifyApprovalBillResult           | verifyDockingResult           | approvalBillControlDimension || result
        "A"             | "T"                     | new VerifyApprovalBillResultType() | null                          | "O"                          || true
        "B"             | "T"                     | new VerifyApprovalBillResultType() | null                          | "O"                          || true
        "B"             | "F"                     | null                               | null                          | "O"                          || false
        "B"             | "F"                     | null                               | new VerifyDockingResultType() | "O"                          || true
        "B"             | "F"                     | null                               | null                          | "P"                          || true
    }

    def "test buildApprovalNoByOrder"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                "approvalInput": new ApprovalInput(
                        "subApprovalNo" : "subApprovalNo",
                        "emergency" : "T",
                ),
                "integrationSoaRequestType": new IntegrationSoaRequestType(
                        "transactionID" : "transactionID"
                ),
                "cityInput" : new CityInput(
                        "cityId" : 24
                ),
                "corpPayInfo" : new CorpPayInfo(
                        "corpPayType" : "public"
                )
        )
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {{
                    put("BillType", "A")
                    put("BillControlMode", "A")
                    put("isChkaheadapproveHotelI","T")
                }}))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>()))
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        when:
        def result = myTestClass.buildApprovalNoByOrder(accountInfo, orderCreateRequestType);

        then:
        result == "subApprovalNo"


        when:
        accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {{
                    put("BillType", "A")
                    put("BillControlMode", "B")
                    put("isChkaheadapproveHotelI","T")
                }}))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>()))
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        result = myTestClass.buildApprovalNoByOrder(accountInfo, orderCreateRequestType);

        then:
        result == "subApprovalNo"

        when:
        accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {{
                    put("BillType", "A")
                    put("BillControlMode", "B")
                    put("isChkaheadapproveHotelI","F")
                }}))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>()))
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        result = myTestClass.buildApprovalNoByOrder(accountInfo, orderCreateRequestType);

        then:
        result == null
    }

    def "test getTripInfo"() {
        given:
        SearchTripDetailResponseType searchTripDetailResponseType = new SearchTripDetailResponseType(
                "approvalInfoList": [
                        new ApprovalInfo(
                                "externalId": "externalId",
                        )
                ]
        )
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                "followApprovalInfoInput": new FollowApprovalInfoInput(
                        "followSelected": "T"
                ),
                "tripInput": new TripInput(
                        "tripId": "1122"
                ),
                integrationSoaRequestType: new IntegrationSoaRequestType()
        )
        CreateTripResponseType createTripResponseType = new CreateTripResponseType()
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("BillType", "E")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        OrderCreateToken orderCreateToken = new OrderCreateToken(followApprovalResult: new FollowApprovalResult())
        orderCreateToken.followApprovalResult.tripId = "7788"
        orderCreateToken.followApprovalResult.followOrderNo = "34343"
        when:
        def result = myTestClass.getTripInfo(
                searchTripDetailResponseType, orderCreateRequestType, createTripResponseType, accountInfo, orderCreateToken);

        then:
        result.getContinueApprovalOrderId() == 34343
        result.getAuthFromTripId() == true
        result.getTripAdditionalOrder() == true


        when:
        orderCreateToken.followApprovalResult.followOrderNo = null
        result = myTestClass.getTripInfo(
                searchTripDetailResponseType, orderCreateRequestType, createTripResponseType, accountInfo, orderCreateToken);

        then:
        result.getContinueApprovalOrderId() == 0
        result.getAuthFromTripId() == true
        result.getTripAdditionalOrder() == true
    }


    @Unroll
    def "testGetProductLine with different scenarios"() {
        given:
        MapperOfSaveCommonDataRequestType mapper = new MapperOfSaveCommonDataRequestType()
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(
                roomInfo: new BookRoomInfoEntity(balanceType: "PP", roomType: roomType, tmcPriceType: tmcPriceType))
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo();

        when: "calling getProductLine with specific parameters"
        def result = mapper.getProductLine(checkAvailInfo)

        then: "the result should be as expected"
        result == expectedResult

        where:
        roomType | tmcPriceType || expectedResult
        "M"      | "M"          || "C2M"
        "M"      | "C"          || "C2M"
        "C"      | "M"          || "C2M"
        "M"      | "C"          || "C2M"
        "C"      | "C"          || "C"
        "C"      | "NONE"       || "C"
        "M"      | "NONE"       || "M"
    }

    def "testBuildApprovalVerifyId with default scenario"() {
        given:
        MapperOfSaveCommonDataRequestType mapper = new MapperOfSaveCommonDataRequestType()
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                cityInput: new CityInput(cityId: 1),
                approvalInput: new ApprovalInput(subApprovalNo: "subApprovalNo"),
                corpPayInfo: new CorpPayInfo(corpPayType: "public")
        )
        CheckTravelPolicyResponseType checkTravelPolicyResponseType = new CheckTravelPolicyResponseType(
                verifyApprovalBillResult: new VerifyApprovalBillResultType(transactionID: "transactionID")
        )
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>(){
                    {
                        put("BillControlMode", "B")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();

        when:
        String result = mapper.buildApprovalVerifyId(orderCreateRequestType, checkTravelPolicyResponseType, accountInfo)

        then:
        result == "transactionID"


        when:
        WrapperOfAccount.AccountInfo accountInfoA = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>(){
                    {
                        put("BillControlMode", "A")
                        put("isChkaheadapproveHotelI", "T")
                        put("isChkaheadapproveHotel", "T")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        result = mapper.buildApprovalVerifyId(orderCreateRequestType, checkTravelPolicyResponseType, accountInfoA)

        then:
        result == "transactionID"
    }

    @Unroll
    def "testBuildCostCenterInfoType with different scenarios"() {
        given:
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>(){
                    {
                        put("BillType", "A")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        new MockUp<OrderCreateProcessorOfUtil>(){
            @Mock
            public static boolean usePsgEname(Integer cityId, WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
                return true
            }
        }
        MapperOfSaveCommonDataRequestType mapper = new MapperOfSaveCommonDataRequestType()
        HotelBookPassengerInput hotelBookPassengerInput = JsonUtil.fromJson("{\n" +
                "        \"hotelPassengerInput\": {\n" +
                "            \"uid\": \"_SL2236978295\",\n" +
                "            \"employee\": \"T\",\n" +
                "            \"external\": \"F\",\n" +
                "            \"employeeId\": \"编XC295295123\"\n" +
                "        },\n" +
                "        \"passengerBasicInfo\": {\n" +
                "            \"preferFirstName\": \"PERIAYYA SARAVANAKUMAR\",\n" +
                "            \"preferLastName\": \"MUTHUKUMMAR\",\n" +
                "            \"gender\": \"U\",\n" +
                "            \"birth\": \"1990-11-08\"\n" +
                "        },\n" +
                "        \"name\": \"彭彭一\",\n" +
                "        \"isRegistrant\": \"F\",\n" +
                "        \"phoneInfo\": {\n" +
                "            \"countryCode\": \"86\",\n" +
                "            \"phoneNo\": \"***********\",\n" +
                "            \"transferPhoneNo\": \"***********\"\n" +
                "        },\n" +
                "        \"nationalityInfo\": {\n" +
                "            \"nationalityCode\": \"CN\"\n" +
                "        }\n" +
                "    }", HotelBookPassengerInput.class)
        WrapperOfSaveCommonData wrapperOfSaveCommonData = WrapperOfSaveCommonData.builder()
                .setAccountInfo(accountInfo)
                .setOrderCreateRequestType(new OrderCreateRequestType(
                        cityInput: new CityInput(cityId: 1),
                        hotelBookPassengerInputs: [hotelBookPassengerInput],
                        corpPayInfo: new CorpPayInfo(corpPayType: "public"),
                        approvalInput: new ApprovalInput(subApprovalNo: "***********"),
                        costCenterInfo: new CostCenterInfo(costCenterJsonString: "{\"items\":{\"fdefault\":{\"cost1\":\"\",\"cost3\":\"测试1\",\"TravelPurpose\":\"出差\",\"ProjectNo\":\"项目测试1\",\"journeyNo\":\"测试关联审批单\",\"D1\":\"\",\"D2\":\"\"},\"_SL2236978295\":{\"name\":\"彭彭一\",\"cost2\":\"测试3\"}}}")))
                .build();

        when:
        CostCenterInfoType result = mapper.buildCostCenterInfoType(wrapperOfSaveCommonData)

        then:
        result.costCenterExtendInfo.journeyNo == "测试关联审批单"
    }

    @Unroll
    def "testBuildJourneyNo with different scenarios"() {
        given:
        ApprovalTextInfoResponseType approvalTextInfoResponseType = new ApprovalTextInfoResponseType(emergencyName: "紧急预订")
        SaveCostCenterInputItem orderCostCenterInputItem = new SaveCostCenterInputItem(journeyNo: journeyNo)
        ApprovalInput approvalInput = new ApprovalInput(subApprovalNo: subApprovalNo, emergency: emergency)
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                cityInput: new CityInput(cityId: 22249),
                corpPayInfo: new CorpPayInfo(corpPayType: "public")
        )
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("BillType", "A")
                        put("docChooseDimensionHtl", "O")
                        put("isChkaheadapproveHotel", "T")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();

        when:
        String result = new MapperOfSaveCommonDataRequestType().buildJourneyNo(approvalTextInfoResponseType, orderCostCenterInputItem, approvalInput, orderCreateRequestType, accountInfo)

        then:
        result == expectedJourneyNo

        where:
        journeyNo    | subApprovalNo | emergency || expectedJourneyNo
        "journey123" | null          | "F"       || "journey123"
        null         | "approval456" | "F"       || "approval456"
        null         | null          | "F"       || null
        "journey789" | "approval789" | "F"       || "journey789"
        "journey789" | "approval789" | "T"       || "journey789"
        "journey789" | ""            | "T"       || "journey789"
    }

    def "buildJourneyNoNew"() {
        given: "Mocked dependencies for default case"
        def approvalTextInfoResponseType = Mock(ApprovalTextInfoResponseType)
        def approvalInput = Mock(ApprovalInput) {
            getEmergency() >> "F"
            getSubApprovalNo() >> "approvalno"
        }
        def orderCreateRequestType = Mock(OrderCreateRequestType) {
            getCityInput() >> Mock(CityInput) {
                getCityId() >> "123"
            }
            getCorpPayInfo() >> Mock(CorpPayInfo)
        }
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isPreApprovalRequired(_, _) >> true
        }
        def mapper = Spy(MapperOfSaveCommonDataRequestType)
        mapper.buildEmergencyName(_,_,_,_) >> "紧急预订" >> null

        when: "test emgrency"
        def result = mapper.buildJourneyNoNew(approvalTextInfoResponseType, approvalInput, orderCreateRequestType, accountInfo)

        then: "The result should be emgrency"
        result == "紧急预订"


        when: "test approvalno"
        result = mapper.buildJourneyNoNew(approvalTextInfoResponseType, approvalInput, orderCreateRequestType, accountInfo)

        then: "The result should be approvalno"
        result == "approvalno"


        when: "test approvalno"
        result = mapper.buildJourneyNoNew(approvalTextInfoResponseType, null, orderCreateRequestType, accountInfo)

        then: "The result should be approvalno"
        result == null
    }


    // 真实数据测试jar
    def "genCostCenterInfoType"() {
        given:
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            public static boolean usePsgEname(Integer cityId, WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
                return false
            }
        }
        CostCenterKeyDto costCenterKeyDto = CostCenterKeyUtils.analysisCostCenterKey("H4sIAAAAAAAA_wE1AMr_CgFQEgNDQzEaBjI5MjY2OSgBMiFBUFBST1ZBTOW8oOS4ieaIkOacrOS4reW_g-WGheWuuTH04UruNQAAAA");

        OrderCreateRequestType orderCreateRequestType = JsonUtil.fromJson("{\"integrationSoaRequestType\":{\"userInfo\":{\"userId\":\"_SL2236983098\",\"corpId\":\"p***********\",\"groupId\":\"Gr_00007155\",\"pos\":\"CHINA\"},\"token\":\"\",\"language\":\"zh-CN\",\"requestId\":\"2ff0f78cc7c448d89ee379464334b2a0\",\"sourceFrom\":\"Online\",\"transactionID\":\"TID.SEARCHHOTEL.2504.BBE4857FB0DF42C2BC772C7658A0717D\",\"gatewayHost\":\"ct.ctrip.fat14717.qa.nt.ctripcorp.com\",\"timezoneOffsetMinutesNew\":\"480\",\"logIndices\":[{\"key\":\"groupId\",\"value\":\"Gr_00007155\"},{\"key\":\"timezoneOffsetMinutesNew\",\"value\":\"480\"},{\"key\":\"gatewayHost\",\"value\":\"ct.ctrip.fat14717.qa.nt.ctripcorp.com\"},{\"key\":\"pvid\",\"value\":\"44\"},{\"key\":\"subChannel\",\"value\":\"\"},{\"key\":\"CID\",\"value\":\"09031114411153626451\"},{\"key\":\"EID\"},{\"key\":\"CIP\",\"value\":\"*************\"},{\"key\":\"miceToken\",\"value\":\"\"},{\"key\":\"groupName\",\"value\":\"ph测试集团1\"},{\"key\":\"gatewayIdc\",\"value\":\"NTGXH\"},{\"key\":\"traceID\",\"value\":\"\"},{\"key\":\"A\",\"value\":\"ordercheck\"},{\"key\":\"CorpID\",\"value\":\"p***********\"},{\"key\":\"C\",\"value\":\"corpbookinghotelbookservice\"},{\"key\":\"M\",\"value\":\"_SL2236983098\"},{\"key\":\"sid\",\"value\":\"2505\"},{\"key\":\"S\",\"value\":\"983f54d1ebdb8420a7d90da377be9793\"},{\"key\":\"T\",\"value\":\"\"},{\"key\":\"pos\",\"value\":\"CHINA\"},{\"key\":\"GE\",\"value\":\"1751264166381\"},{\"key\":\"sourceFrom\",\"value\":\"Online\"},{\"key\":\"corpName\",\"value\":\"测试p***********\"},{\"key\":\"RID\",\"value\":\"2ff0f78cc7c448d89ee379464334b2a0\"},{\"key\":\"TID\",\"value\":\"TID.SEARCHHOTEL.2504.BBE4857FB0DF42C2BC772C7658A0717D\"},{\"key\":\"VID\",\"value\":\"1687762854054.17onzr\"}],\"ticket\":\"tqlVz1kNq13OYqPl650ae9sWeGV2TwPRTd0LdPk+mFxvOm6yiInQYnnqTp2HPR024G+NtPkmumIQe9VR90bsEovpPVjgTFSRO+rke+CBovk\\u003d\",\"transferInfo\":[{\"key\":\"iframe\",\"value\":\"F\"},{\"key\":\"channel\",\"value\":\"Online\"}]},\"hotelBookPassengerInputs\":[{\"hotelPassengerInput\":{\"roomIndex\":2,\"uid\":\"_SL2236983153\",\"approvalPassengerId\":\"729908442329365574\",\"employee\":\"T\",\"external\":\"F\",\"employeeId\":\"130048\"},\"passengerBasicInfo\":{\"gender\":\"U\",\"birth\":\"1974-03-01\"},\"name\":\"彭彭五\",\"isRegistrant\":\"F\",\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"15123007899\",\"transferPhoneNo\":\"15123007899\"}},{\"hotelPassengerInput\":{\"roomIndex\":1,\"infoId\":\"292669\",\"approvalPassengerId\":\"1622173658068951313\",\"employee\":\"F\",\"external\":\"F\"},\"passengerBasicInfo\":{\"gender\":\"U\",\"birth\":\"2000-01-01\"},\"name\":\"张三\",\"isRegistrant\":\"F\",\"nationalityInfo\":{\"nationalityCode\":\"CN\"}},{\"hotelPassengerInput\":{\"roomIndex\":1,\"uid\":\"_SL2236982177\",\"approvalPassengerId\":\"3866191351995626601\",\"employee\":\"T\",\"external\":\"F\",\"employeeId\":\"177177\"},\"passengerBasicInfo\":{\"preferFirstName\":\"PENGSISI\",\"preferLastName\":\"PENGP\",\"gender\":\"U\",\"birth\":\"1991-02-06\"},\"name\":\"\",\"isRegistrant\":\"F\",\"phoneInfo\":{\"countryCode\":\"82\",\"phoneNo\":\"15123007123\",\"transferPhoneNo\":\"15123007123\"},\"nationalityInfo\":{\"nationalityCode\":\"CN\"}}],\"hotelInvoiceInfos\":[],\"cityInput\":{\"cityId\":2},\"hotelContactorInfo\":{\"name\":\"商旅客户\",\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"15123001112\",\"transferPhoneNo\":\"15123001112\"},\"emailInfo\":{\"email\":\"<EMAIL>\",\"transferEmail\":\"<EMAIL>\"}},\"approvalInput\":{\"masterApprovalNo\":\"差标模式不管控-管控出行人-成本中心\",\"subApprovalNo\":\"差标模式不管控-管控出行人-成本中心\",\"emergency\":\"F\"},\"miceInput\":{},\"hotelBookInput\":{\"hotelDateRangeInfo\":{\"checkIn\":\"2025-07-01\",\"checkOut\":\"2025-07-02\"},\"roomQuantity\":2},\"membershipInfo\":{},\"bookModeInfo\":{\"bookingType\":\"NONE\"},\"costAllocationInfo\":{\"costAllocationToken\":\"H4sIAAAAAAAAAOMS5Gi7wC/AIMGixG5kamagZ2AmJMXFZmRpZGZmKSTAcX0WK1iSzcLUWM/YVEiRizc+2MfIyNjM0sLY0NSYgBIjQ3NzoJIbKErMpPDYIEXYBinCNijhsUGJsA1KhG0AANYDiS45AQAA\",\"shareAmounts\":[{\"costAllocationAmount\":{\"amount\":\"853.35\"},\"shareAmountKey\":\"292669\"},{\"costAllocationAmount\":{\"amount\":\"853.35\"},\"shareAmountKey\":\"_SL2236983153\"},{\"costAllocationAmount\":{\"amount\":\"853.36\"},\"shareAmountKey\":\"_SL2236982177\"}]},\"resourceTokenInfo\":{\"resourceToken\":\"{\\\"hotelResourceToken\\\":{\\\"masterHotelId\\\":16934,\\\"hotelId\\\":16941,\\\"searchHotelTraceId\\\":\\\"886c823246164df28001cac82ef7fcb3\\\",\\\"hid\\\":\\\"ASIHMTcwOC4wM0IBR0oGCAAQABgAUgQQABgAWgNDTlliDQoDQ05ZEgNDTlkaATFiDwoDQ05ZEgNDTlkaAzEuMGgDcgYzMDAuMDB6BTEwLjAwggEgODg2YzgyMzI0NjE2NGRmMjgwMDFjYWM4MmVmN2ZjYjOKAQUxMDcxOJoBJDM0MmExZGM1LWUyOWYtNDg5Yi1hZThkLWM4ZTZhMDA1MTJiNKIBAggAqgEkZTFkMDlmNjctMjBjZC00NGJjLWE3NWYtZDI0NDM5MzRmNTQz\\\",\\\"hotelType\\\":\\\"M\\\",\\\"bonusPoint\\\":\\\"F\\\",\\\"hotelGeoInfoResourceToken\\\":{\\\"cityId\\\":2}},\\\"roomResourceToken\\\":{\\\"roomId\\\":10718,\\\"baseRoomId\\\":19825,\\\"ratePlanTraceLogId\\\":\\\"34939cc27fe947f5afdf3127dea2283e\\\",\\\"bedName\\\":\\\"1张1.8米大床\\\",\\\"windowName\\\":\\\"有窗\\\",\\\"breakfastInfo\\\":\\\"2份早餐\\\",\\\"confirmType\\\":\\\"IMMEDIATE\\\",\\\"roomType\\\":\\\"M\\\",\\\"balanceType\\\":\\\"PP\\\",\\\"tmcPrice\\\":false,\\\"adult\\\":3,\\\"pId\\\":\\\"AQoHMTcwOC4wMxIHMTcwOC4wMyoDQ05ZMg0KA0NOWRIDQ05ZGgExMg0KA0NOWRIDQ05ZGgExOgIIAEIHCOkPEAcYAUoHCOkPEAcYAlACWABoAXgDgAGthAGIAaaEAZABAKABAA\\\\u003d\\\\u003d\\\",\\\"checkAvlId\\\":0,\\\"amadeus\\\":false,\\\"supportAccommodation\\\":\\\"T\\\",\\\"roomOriginCurrency\\\":\\\"CNY\\\",\\\"roomScenarioEnum\\\":\\\"ROOM_LIST\\\",\\\"ratePlanRequestToken\\\":\\\"ASi1L/0AUEUXAJbysTsAD2W8+qelQE7k43ZeVXOS83beTHhqMrS1Cm1dTYehtbC1oREGgcygkZhGYBSrYNTgMFB/Bvaf/ZH3AaQAoAChACS1YmyRSbuOHqmCrTYn87FqXOZSkykuS+EGw6z8l/vWf8Nu/eZ/UY6raPLNdsZZmoKeZ24xj2Sq98X51CyPO478McSW3vLvl7Tlz6RGamQkAwUzVQZFiEevoBDhPXTPYQ03Cr9h8z5Dw1vweIO24oqCUCjIxKIsR6cLPzAUA5SYxoRwg4BMACJklYpCUZdVTSpagJVVqCurYJdlgSY74IG/0MEiFG1zTWp1NKUkzLIxKnEDO/JDx6rCaxJcyZLXnD9vZyy5JZmXENb23+TmtfHzqyco5lJ15+iG5fJWcxHJs9Q2KZpHsCZ+M7pUpKgWXbbjllOhF3TjuAa8mGiJ95GsoCaOTXj2aeERTk0QsHaV1TEP6Rh08wo0inAf3PRJGJybtPS6t9G3pnTGa12vE+Fi7V8sNgTUllW0ouEYG42cmJFymJ1P2/Pm3m3phtr7wEPzjgX+dItcGbZdGu/e9qTnU55n28DjV+xFQaoKclmUxZ5md7LaN/DQvCOBP0n4LNleZU66ETGhJPs15hKHRhUzHSmjNqND3l2PJ82xVMDIuebkSwjbLqA2RJPr18GlJzPdV6kSMVDsyS+T/1rUj4QiIx24g3IoHGcmWGgpqRIU5owUlqlFc01jHkoEvY8GxrBSBKW5rgiuIJoD+BPdn+h+RPcguv/QPYfuNXSPoXsLHe90B3UKXe1j9/qEnnWqO+gIukWvkHgTjYqGL+EtrknlLRwXnsTpH/I8lzZp9lx7Fr3fbc+mPM+lO2o1BY4GPsTvzLJLRfbTEjy+dzDwp+27iCA7llvJbrqQwON7R4GzFE320wQ8NO+48CeNKJYsF3Yajbh1JftpF4/vHQf8yfXMG413dwoANztskSLfChqIGgjZAOyE+d1NZ9L/FPPLZuNQyg\\\\u003d\\\\u003d\\\",\\\"ratePlanRoomToken\\\":\\\"ASi1L/0AUEEAAAoBTRAAGN5T\\\",\\\"roomUniqueKey\\\":\\\"V1-ASi1L/0AUIEAAAjeUxIBTRoETk9ORSICUFA\\\\u003d\\\",\\\"adultNumber\\\":3,\\\"windowType\\\":2,\\\"customRoomAmount\\\":2560.06,\\\"customRoomAmountCurrency\\\":\\\"CNY\\\"},\\\"reservationResourceToken\\\":{\\\"wsId\\\":\\\"47bf8913-bcad-4444-a590-2706a9246520\\\",\\\"policyToken\\\":\\\"v1KLUv/QBQDQoABtRAJBCpygCOCBlxGaQztnN7Dy7AB6I/wfAaYtK2TWR+gBUAAABwPDYANgA3APUjFrtDa8tzBlc1bAlV18Zm7TxtcZ5meW8hJTRsOTPs61iaVMXldVLNWgNobYH9cd7ocqoVAn15Y+KRX3wFfL4Y9j+uQxQfp+S+9DLTWGezPkwuMoyhcfwhKoxjeBRnahjFwfyiNC+dvyk27NlSCPwPmROUzgR1ib1V5c0ozRU2tuZiEie1EZ61gLKWAFufH5CInYHtQH2IQcTaRPXY7C3qDhgLCP1es3avWbfXrB/XB7DpS/w2KiQhHLO0t7oq4nqKK2TZik4XwJOasjTx6JoMxN/o6eT1axYAORsnY4ejtcN7c/HUYhCGEKZGoRgupBpg6qEOpRWaUz6rR5KfGrOz1UvbI4xaqgDODSDldWYOK5tN\\\"},\\\"bookInitResourceToken\\\":{\\\"aggBookPriceResourceToken\\\":{\\\"currency\\\":\\\"CNY\\\",\\\"accountPaymentAmount\\\":2560.06,\\\"individualPaymentAmount\\\":0,\\\"mixPayAccountExtraRatio\\\":0,\\\"mixPayAccountExtraAmount\\\":0},\\\"roomPriceResourceToken\\\":{},\\\"costCenterNewUse\\\":\\\"T\\\"},\\\"bookResourceToken\\\":{\\\"bookInfoStr\\\":\\\"c479b56d8448c7d19206f148e1937c17\\\",\\\"bookVersion\\\":\\\"2\\\"}}\"},\"corpPayInfo\":{\"corpPayType\":\"public\"},\"userActionInfo\":{\"userActionToken\":\"def\"},\"approvalFlowInput\":{\"password\":\"\",\"approvalFlowToken\":\"H4sIAAAAAAAA_wXBIRaAIAwAUK1Go4lg5T3HgI3jbINdxOwxrAbPZ_D_ZT8cWldrMUkaMWu2yOYYTQqAS9ehsM64BcxEPhhNRKqycSIBLGqpMnkb4XrO-3unH33Vg0NTAAAA\",\"approvers\":[{\"uid\":\"_SL2236983098\",\"level\":\"1\"}]},\"rcInfos\":[],\"followApprovalInfoInput\":{\"aiFollow\":\"F\",\"artificialFollow\":\"F\",\"followSelected\":\"F\"},\"bookUrlInfos\":[{\"urlType\":\"SUCCESS\",\"urlValue\":\"https://ct.ctrip.fat14717.qa.nt.ctripcorp.com/hotel-online/order/success?InterfaceName\\u003dCreate_PaymentBill_New\\u0026\"},{\"urlType\":\"COMPLETION\",\"urlValue\":\"https://ct.ctrip.fat14717.qa.nt.ctripcorp.com/hotel-booking/orderSuccess\"},{\"urlType\":\"S_BACK\",\"urlValue\":\"https://ct.ctrip.fat14717.qa.nt.ctripcorp.com/hotel-online/order/success?InterfaceName\\u003dCreate_PaymentBill_New\\u0026\"},{\"urlType\":\"E_BACK\"},{\"urlType\":\"FROM_URL\",\"urlValue\":\"https://ct.ctrip.fat14717.qa.nt.ctripcorp.com/hotel-online-after-booking/pages/orderdetail/index?orderId\\u003d\"},{\"urlType\":\"S_BACK_MICE\",\"urlValue\":\"https://ct.ctrip.fat24.qa.nt.ctripcorp.com/mice-activities/welfareTourism\"},{\"urlType\":\"FROM_URL_ORDER_DETAIL\"},{\"urlType\":\"OFFLINE_RETURN_URL\",\"urlValue\":\"http://corpint.ct.fat4.qa.nt.ctripcorp.com/hotel-online/offlineOrder/success\"},{\"urlType\":\"ORDER_DETAIL_URL\",\"urlValue\":\"https://ct.ctrip.fat14717.qa.nt.ctripcorp.com/hotel-online-after-booking/pages/orderdetail/index?\"}],\"paymentInfoInput\":{\"canWechatPayByOthers\":\"F\"},\"clientInfo\":{\"rmsToken\":\"fp\\u003dBAD79A-382481-11BD84\\u0026vid\\u003d1687762854054.17onzr\\u0026pageId\\u003d10650153220\\u0026r\\u003d57b5436586b048ef93581bba07e5c4d0\\u0026ip\\u003d***************\\u0026rg\\u003dfin\\u0026kpData\\u003d0_0_0\\u0026kpControl\\u003d0_0_0-0_0_0\\u0026kpEmp\\u003d0_0_0_0_0_0_0_0_0_0-0_0_0_0_0_0_0_0_0_0-0_0_0_0_0_0_0_0_0_0\\u0026screen\\u003d1536x864\\u0026tz\\u003d+8\\u0026blang\\u003dzh-CN\\u0026oslang\\u003dzh-CN\\u0026ua\\u003dMozilla%2F5.0%20(Windows%20NT%2010.0%3B%20Win64%3B%20x64)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Chrome%2F117.0.0.0%20Safari%2F537.36\\u0026d\\u003dct.ctrip.fat14717.qa.nt.ctripcorp.com\\u0026v\\u003d25\\u0026kpg\\u003d0_0_0_0_0_0_0_0_0_0\\u0026adblock\\u003dF\\u0026cck\\u003dF\\u0026ftoken\\u003d\",\"secondaryChannel\":\"Other-new\"},\"offlineInfo\":{\"forciblyAssure\":\"F\",\"especialAuth\":\"F\",\"autoConfirm\":\"F\",\"exigentOrder\":\"F\",\"operationRule\":\"\"},\"roomInfoInput\":{\"roomRemark\":\"\",\"roomCustomRemark\":\"\"},\"sendInfo\":{\"sendSMS\":\"F\",\"sendEmail\":\"F\"},\"ssoInput\":{\"ssoKey\":\"14bd8c90-5349-11f0-83d9-67800aa1dfde\"},\"flashStayInput\":{\"flashPlatFrom\":\"Online\"},\"hotelPayTypeInput\":[{\"payCode\":\"ROOM\",\"payType\":\"CORP_PAY\"}],\"strategyInfos\":[{\"strategyKey\":\"BOOKING_SCENARIO\",\"strategyValue\":\"HOTEL_LIST\"},{\"strategyKey\":\"BOOKING_WITH_PERSONAL_ACCOUNT\",\"strategyValue\":\"F\"}],\"orderCreateToken\":\"H4sIAAAAAAAAAOMSCfb0c/dxjXcMCAjyD3P0iXfz8Q/XYAQA4NUpBRgAAAA\\u003d\",\"useNewOrderCreate\":\"T\",\"costCenterInfoNew\":{\"costCenterInputs\":[{\"value\":\"张三成本中心内容1\",\"itemKey\":\"H4sIAAAAAAAA_wE1AMr_CgFQEgNDQzEaBjI5MjY2OSgBMiFBUFBST1ZBTOW8oOS4ieaIkOacrOS4reW_g-WGheWuuTH04UruNQAAAA\",\"optionItemKey\":\"H4sIAAAAAAAA_wEbAOT_EhnlvKDkuInmiJDmnKzkuK3lv4PlhoXlrrkxk621YBsAAAA\"},{\"value\":\"654321\",\"itemKey\":\"H4sIAAAAAAAA_-NiDBBidnY2kmIzsjQyM7PUYDTicwwICPIPc_QxMzUxNjIEAKG5c6UiAAAA\",\"optionItemKey\":\"H4sIAAAAAAAA_xNiMzM1MTYyBAA3b0RmCAAAAA\"},{\"value\":\"张三成本中心内容3\",\"itemKey\":\"H4sIAAAAAAAA_wE1AMr_CgFQEgNDQzMaBjI5MjY2OSgBMiFBUFBST1ZBTOW8oOS4ieaIkOacrOS4reW_g-WGheWuuTP-t9f_NQAAAA\",\"optionItemKey\":\"H4sIAAAAAAAA_wEbAOT_EhnlvKDkuInmiJDmnKzkuK3lv4PlhoXlrrkzv8y7jhsAAAA\"},{\"value\":\"张三成本中心内容4\",\"itemKey\":\"H4sIAAAAAAAA_wE1AMr_CgFQEgNDQzQaBjI5MjY2OSgBMiFBUFBST1ZBTOW8oOS4ieaIkOacrOS4reW_g-WGheWuuTTl5r7FNQAAAA\",\"optionItemKey\":\"H4sIAAAAAAAA_wEbAOT_EhnlvKDkuInmiJDmnKzkuK3lv4PlhoXlrrk0HFnfEBsAAAA\"},{\"value\":\"张三成本中心内容5\",\"itemKey\":\"H4sIAAAAAAAA_wE1AMr_CgFQEgNDQzUaBjI5MjY2OSgBMiFBUFBST1ZBTOW8oOS4ieaIkOacrOS4reW_g-WGheWuuTXgTXDNNQAAAA\",\"optionItemKey\":\"H4sIAAAAAAAA_wEbAOT_EhnlvKDkuInmiJDmnKzkuK3lv4PlhoXlrrk1imnYZxsAAAA\"},{\"value\":\"张三成本中心内容6\",\"itemKey\":\"H4sIAAAAAAAA_wE1AMr_CgFQEgNDQzYaBjI5MjY2OSgBMiFBUFBST1ZBTOW8oOS4ieaIkOacrOS4reW_g-WGheWuuTbvsCPUNQAAAA\",\"optionItemKey\":\"H4sIAAAAAAAA_wEbAOT_EhnlvKDkuInmiJDmnKzkuK3lv4PlhoXlrrk2MDjR_hsAAAA\"},{\"value\":\"654321\",\"itemKey\":\"H4sIAAAAAAAA_-NiDBBidnY2kuKND_YxMjI2s7QwMjQ312Aw4nMMCAjyD3P0MTM1MTYyBAAjV4d-KQAAAA\",\"optionItemKey\":\"H4sIAAAAAAAA_xNiMzM1MTYyBAA3b0RmCAAAAA\"}],\"costCenterToken\":\"H4sIAAAAAAAA_-Myerp93bMF7c9WLHy6p__Jjt7n6xY-61uuC6Getu96sbDnya5dus86Jjybs-bJjrVP9zcL8XPxxgf7GBkZm1laGBuaGgtxcLEZWRqZmVmiSBkZmptL-XIxhkh5eJgUezpCQbyuX6aLk1NmSl6kUXauZ1VxVmClr3lAaKRLSGZyebmnc4BnQHJ8YIVvVWiFX5anq6O3u2myWWgmSK9UNsi4NBTjyl0NHX2L4p3T3QJd0_1cAqsCE52yPE19syKN_IPTnXwz3ZxC3ZyCQwyjnEL8wy3y_YNNMlMTPbP9E5OLgOyi1PD4dN1w94zU8NLSkJAc0yJzN79Aoi2LpMCypLJi54BQqGXhIMuCcAZUqbefS3xkhW-Wp1GxeWC5b1agsaGRY7lJnq-vs2NWpYtxgIFviK-hb0hkpZOjY1aYSYquN9hgJUZDAGTMH4blAQAA\"}}", OrderCreateRequestType.class)
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType = JsonUtil.fromJson("{\"baseInfo\":{\"userInfo\":{\"uid\":\"_SL2236983098\",\"policyUid\":\"\",\"corpId\":\"p***********\",\"eid\":\"\",\"usersCityId\":2,\"userCountryCode\":\"CN\"},\"customCurrency\":\"CNY\",\"website\":\"Online\",\"feeType\":\"C\",\"quantity\":2,\"startTime\":\"2025-07-01T00:00:00+08\",\"endTime\":\"2025-07-02T00:00:00+08\",\"language\":\"CN\",\"locale\":\"zh-CN\",\"startTimeUTC\":\"2025-06-30T16:00:00Z\",\"endTimeUTC\":\"2025-07-01T16:00:00Z\",\"pOS\":\"zh-CN\",\"pOSType\":{\"pOS\":\"zh-CN\",\"countryId\":1},\"platform\":\"online\",\"bookingWithPersonalAccount\":false,\"bookingChannel\":\"ONLINE\"},\"hotelInfo\":{\"subHotelId\":16941,\"masterHotelId\":16934,\"star\":5,\"rStar\":0,\"starLicence\":false,\"customerEval\":0.0,\"hotelGroupId\":0,\"oversea\":false,\"tripBusinessHotel\":false,\"cuHotel\":false,\"hasServiceFee\":false,\"localTimeOffsetHours\":0,\"telephone\":\"+86-21-********\",\"geographicalInfo\":{\"locationInfo\":{},\"cityInfo\":{\"id\":2,\"name\":{\"textGB\":\"上海\",\"textEn\":\"Shanghai\"}},\"provinceInfo\":{\"id\":2},\"countryInfo\":{\"id\":1},\"coordinateInfo\":{\"googleLat\":31.210123,\"googleLng\":121.561703,\"gdLat\":31.209983,\"gdLng\":121.56165,\"bdLat\":31.216349,\"bdLng\":121.568067}},\"address\":{\"textGB\":\"梅花路1108号\",\"textEn\":\"No. 1108 Meihua Road\"},\"hotelName\":{\"textGB\":\"7119上海卓美亚喜玛拉雅酒店\",\"textEn\":\"7119Jumeirah Himalayas Hotel\"},\"currentCountry\":true,\"timeZoneId\":\"Asia/Shanghai\",\"vendorId\":0,\"hotelSustainable\":{\"sustainableCertification\":false}},\"roomInfo\":{\"subRoomId\":10718,\"roomName\":{\"textGB\":\"标准大床房1473\",\"textEn\":\"Standard Queen Room1473\"},\"basicRoomName\":{\"textGB\":\"标准大床房1473\",\"textEn\":\"Standard Queen Room1473\"},\"masterBasicRoomId\":19825,\"masterBasicRoomName\":{\"textGB\":\"标准大床房1473\"},\"roomType\":\"M\",\"tmcPriceType\":\"NONE\",\"balanceType\":\"PP\",\"mealType\":4,\"addPrice\":false,\"invoiceType\":\"Ordinary\",\"invoiceLimitType\":\"ByCtrip\",\"applyAreaPropertyId\":0,\"maxGuestCountPerRoom\":3,\"maskRoom\":false,\"hourlyRoom\":false,\"corpPrice\":false,\"manualGds\":false,\"joined30MinutesFreeCancel\":false,\"premiumRoom\":false,\"welfareRoom\":false,\"groupMemberShip\":false,\"earnHotelPoints\":false,\"bonusPointInfo\":{\"bonusPointRoom\":false},\"earlyArrivalTime\":\"2025-07-01T14:00:00+08\",\"lastCancelTime\":\"2025-07-01T00:00:00+08\",\"cnyAmount\":3416.06,\"originAmountInfo\":{\"amount\":3416.06,\"currency\":\"CNY\",\"exchange\":1},\"customAmountInfo\":{\"amount\":3416.06,\"currency\":\"CNY\",\"exchange\":1},\"originCostInfo\":{\"amount\":1366.42,\"currency\":\"CNY\",\"exchange\":1},\"originCostBeforeTaxInfo\":{\"amount\":1366.42,\"currency\":\"CNY\",\"exchange\":1},\"roomDiscountInfo\":{\"promotionSwitchOn\":true,\"customerTagTypes\":[{\"key\":\"IgnoreSpecialCancelPolicyNotMatchingFilter\",\"value\":\"T\"},{\"key\":\"EnableNewGroupMember\",\"value\":\"T\"},{\"key\":\"MaskCampaignRoomWhiteList\",\"value\":\"1\"},{\"key\":\"FilterRoomByPerson\",\"value\":\"2,999\"},{\"key\":\"PrepayDiscountCtripNewCustomer\",\"value\":\"F\"},{\"key\":\"PrepayDiscountHotelNewCustomer\",\"value\":\"T\"},{\"key\":\"BrandNewCustomer\",\"value\":\"T\"},{\"key\":\"CtripGroupID\",\"value\":\"-1\"},{\"key\":\"IsPrepayDiscountMoney\",\"value\":\"T\"},{\"key\":\"SwitchList\",\"value\":\"53,44,50,121,161,69,94,103,108,122,150,156,159,92,136,157,123\"},{\"key\":\"UserCountryCode\",\"value\":\"CN\"},{\"key\":\"packageUnifiedSalesStructure\",\"value\":\"13\"}],\"bookRulePromotionList\":[],\"roomDailyDiscountInfo\":[{\"effectDate\":\"2025-07-01T00:00:00+08\",\"tagId\":881,\"ruleConfigId\":50003675,\"prepayCampaignId\":4092,\"typeConfigId\":599,\"discountType\":1,\"discountCnyAmount\":428,\"discountAmount\":428,\"discountCustomAmount\":428}],\"strategyId\":\"10007\",\"originPromotionAmount\":{\"price\":856,\"currency\":\"CNY\"},\"customPromotionAmount\":{\"price\":856,\"currency\":\"CNY\"},\"customerPropertys\":[{\"propertyKey\":\"111\",\"propertyValue\":\"222\",\"propertyType\":0},{\"propertyKey\":\"146\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"151\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"152\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"153\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"154\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"168\",\"propertyValue\":\"413\",\"propertyType\":0},{\"propertyKey\":\"177\",\"propertyValue\":\"413\",\"propertyType\":0},{\"propertyKey\":\"180\",\"propertyValue\":\"413\",\"propertyType\":0},{\"propertyKey\":\"222\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"332\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"657\",\"propertyValue\":\"0\",\"propertyType\":0,\"propertyExtensionInfo\":{\"extensionItemList\":[{\"itemKey\":\"1036:2\",\"flag\":0},{\"itemKey\":\"1049:73\",\"flag\":0},{\"itemKey\":\"111:2,1\",\"flag\":0},{\"itemKey\":\"1163:1\",\"flag\":0},{\"itemKey\":\"1165:2\",\"flag\":0},{\"itemKey\":\"11:228\",\"flag\":0},{\"itemKey\":\"162:1\",\"flag\":0},{\"itemKey\":\"163:73\",\"flag\":0},{\"itemKey\":\"168:1\",\"flag\":0},{\"itemKey\":\"1768:2\",\"flag\":0},{\"itemKey\":\"19:2\",\"flag\":0},{\"itemKey\":\"20:1\",\"flag\":0},{\"itemKey\":\"2349:370\",\"flag\":0},{\"itemKey\":\"23:2,1\",\"flag\":0},{\"itemKey\":\"2549:2\",\"flag\":0},{\"itemKey\":\"25:73\",\"flag\":0},{\"itemKey\":\"35:1,2\",\"flag\":0},{\"itemKey\":\"41:1\",\"flag\":0},{\"itemKey\":\"434:2\",\"flag\":0},{\"itemKey\":\"49:2\",\"flag\":0},{\"itemKey\":\"56:2\",\"flag\":0},{\"itemKey\":\"631:2\",\"flag\":0},{\"itemKey\":\"6:73\",\"flag\":0},{\"itemKey\":\"75:2\",\"flag\":0},{\"itemKey\":\"77:73\",\"flag\":0},{\"itemKey\":\"835:2\",\"flag\":0},{\"itemKey\":\"83:228\",\"flag\":0},{\"itemKey\":\"948:1\",\"flag\":0},{\"itemKey\":\"960:2\",\"flag\":0},{\"itemKey\":\"9:1\",\"flag\":0}],\"type\":6}},{\"propertyKey\":\"102\",\"propertyValue\":\"100\",\"propertyType\":0},{\"propertyKey\":\"440\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"318\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"174\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"582\",\"propertyValue\":\"0\",\"propertyType\":0,\"propertyExtensionInfo\":{\"extensionItemList\":[{\"itemKey\":\"325\",\"flag\":0},{\"itemKey\":\"11\",\"flag\":0},{\"itemKey\":\"2671\",\"flag\":0},{\"itemKey\":\"2997\",\"flag\":0},{\"itemKey\":\"1974\",\"flag\":0},{\"itemKey\":\"344\",\"flag\":0}],\"type\":4}},{\"propertyKey\":\"589\",\"propertyValue\":\"0\",\"propertyType\":0,\"propertyExtensionInfo\":{\"extensionItemList\":[{\"itemKey\":\"770\",\"flag\":0},{\"itemKey\":\"5\",\"flag\":0},{\"itemKey\":\"135\",\"flag\":0},{\"itemKey\":\"1961\",\"flag\":0},{\"itemKey\":\"281\",\"flag\":0},{\"itemKey\":\"380\",\"flag\":0}],\"type\":4}},{\"propertyKey\":\"575\",\"propertyValue\":\"0\",\"propertyType\":0,\"propertyExtensionInfo\":{\"extensionItemList\":[{\"itemKey\":\"32\",\"flag\":0},{\"itemKey\":\"9\",\"flag\":0},{\"itemKey\":\"10\",\"flag\":0},{\"itemKey\":\"587\",\"flag\":0}],\"type\":4}},{\"propertyKey\":\"100038\",\"propertyValue\":\"0\",\"propertyType\":1},{\"propertyKey\":\"100\",\"propertyValue\":\"0\",\"propertyType\":1}]},\"roomDailyInfo\":[{\"effectDate\":\"2025-07-01T00:00:00+08\",\"cnyAmount\":1708.03,\"amount\":1708.03,\"customAmount\":1708.03,\"afterPromotionCnyAmount\":1280.03,\"afterPromotionCustomAmount\":1280.03,\"customizedCnyAmount\":0,\"mealNumber\":2,\"afterPromotionCustomAmountExcludeTax\":1280.03,\"afterPromotionCnyAmountExcludeTax\":1280.03,\"customAmountExcludeTax\":1708.03,\"cnyAmountExcludeTax\":1708.03}],\"paymentRulesInfo\":{\"paymentGuaranteePoly\":\"TRIP\",\"canTravelMoneyPay\":true,\"forceVccPay\":false,\"canCreditCardPay\":true},\"longRentalInfo\":{\"longRental\":false,\"needInquire\":false},\"holdRoomInfo\":{\"holdRoom\":false},\"extraPayTaxMultiCurrencyAmountInfo\":{\"customExtraPayTaxTotalAmount\":0,\"cnyExtraPayTaxTotalAmount\":0},\"pricedRoom\":false,\"generalizedPremiumRoom\":false,\"customAmountExcludeTaxInfo\":{\"amount\":3416.06,\"currency\":\"CNY\",\"exchange\":1},\"supportAnticipation\":false,\"roomRightInfoList\":[],\"invoiceTypeExcludeAddPrice\":\"Ordinary\",\"roomMealInfo\":{\"mealType\":4,\"mealDescList\":[\"2份早餐\"],\"dailyMealInfoList\":[{\"effectDateUTC\":\"2025-06-30T16:00:00Z\",\"mealDescList\":[\"\"]},{\"effectDateUTC\":\"2025-07-01T16:00:00Z\",\"mealDescList\":[\"2份早餐\"]}]},\"saleRoomTags\":[{\"tagCode\":\"ZSTY\",\"tagName\":\"钻石体验\",\"tagDesc\":\"\",\"configInfoList\":[]}],\"packageRoomInfo\":{\"packageRoom\":false,\"packageId\":0,\"xProductId\":[]},\"taxDetails\":[],\"pid\":\"AQoHMTcwOC4wMxIHMTcwOC4wMyoDQ05ZMg0KA0NOWRIDQ05ZGgExMg0KA0NOWRIDQ05ZGgExOgIIAEIHCOkPEAcYAUoHCOkPEAcYAlACWABoAXgDgAGthAE\\u003d\",\"supplierChannel\":\"0\",\"onlyGroupMemberCanBook\":false,\"vatAddPriceInfo\":{\"vatAddPriceRoomFlag\":false,\"vatCompensatePriceFlag\":false,\"vatInvoicePoint\":0.1000,\"vatSpecialInvoicePoint\":0.1000,\"needCompensateVatDiff\":false,\"vatAddPriceType\":\"BOTH_POST\"},\"individualInvoicePostType\":\"BY_ORDER\",\"resourceAgent\":\"TripBiz\"},\"bookingRules\":{\"cancelPenalties\":{\"paymentGuaranteePoly\":\"TRIP\",\"ladderDeduct\":true,\"cancelDeductDetail\":{\"originDeductionPrice\":{\"price\":2560.06,\"currency\":\"CNY\"},\"customDeductionPrice\":{\"price\":2560.06,\"currency\":\"CNY\"}}},\"authorizationRestriction\":{},\"confirmRules\":{\"justifyConfirm\":true,\"confirmDuration\":0,\"forbidNotImConfirm\":true},\"welfareRoomRules\":{},\"limitDepartureDateTime\":{\"departureEnd\":\"2025-07-02T12:00:00+08\",\"departureEndUTC\":\"2025-07-02T04:00:00Z\"},\"certificateInfo\":{\"supportCertificateType\":[]},\"minLOS\":1,\"cancelPolicyInfo\":{\"cancelType\":\"LADDER_FREE\",\"freeCancelPolicySceneType\":\"NOT_SUPPORTED\",\"lastCancelTimeUTC\":\"2025-06-30T16:00:00Z\",\"guaranteePolicyInfo\":{\"guaranteeType\":\"FULL_PREPAY\",\"paymentGuaranteePoly\":\"TRIP\",\"guaranteeReason\":\"NONE\",\"guaranteePriceInfo\":{\"originGuaranteePrice\":{\"price\":2560.06,\"currency\":\"CNY\"},\"customGuaranteePrice\":{\"price\":2560.06,\"currency\":\"CNY\"}},\"cancelDeductDetailInfo\":[{\"deductionStartTimeUTC\":\"2025-06-30T06:06:00Z\",\"deductionEndTimeUTC\":\"2025-06-30T16:00:00Z\",\"deductionRatio\":0.0,\"deductionType\":\"RADIO\",\"originDeductionPrice\":{\"price\":0,\"currency\":\"CNY\"},\"customDeductionPrice\":{\"price\":0,\"currency\":\"CNY\"}},{\"deductionStartTimeUTC\":\"2025-06-30T16:00:00Z\",\"deductionEndTimeUTC\":\"0001-12-29T16:00:00Z\",\"deductionRatio\":1,\"deductionType\":\"RADIO\",\"originDeductionPrice\":{\"price\":2560.06,\"currency\":\"CNY\"},\"customDeductionPrice\":{\"price\":2560.06,\"currency\":\"CNY\"}}]},\"localLastCancelTime\":\"2025-07-01 00:00:00\"},\"limitArrivalDateTime\":{\"arrivalFromUTC\":\"2025-07-01T06:00:00Z\",\"arrivalEndUTC\":\"2025-07-01T22:00:00Z\",\"defaultArrivalTimeEnd\":true,\"localEarlyArrivalTime\":\"2025-07-01 14:00:00\",\"localLastArrivalTime\":\"2025-07-02 06:00:00\"},\"rPLimitInfo\":{\"minLos\":1,\"maxLos\":999,\"minAdvancedBookingDays\":0,\"maxAdvancedBookingDays\":999},\"nationalityRestrictionInfo\":{\"allowCountryCodeList\":[],\"blockCountryCodeList\":[]},\"billingGuestInfo\":{\"guestsNameLanguages\":[\"zh\",\"en\"]},\"restrictRuleList\":[]},\"bookExtInfo\":{\"featureControlFlagMap\":{\"EnableMemberPoints\":false,\"EnableXMinutesFreeCancel\":true,\"BrandNewCustomer\":true,\"EnableShadowDiscount\":true,\"EnableYXH\":true,\"EnablePrepayDiscount\":true,\"EnableNewGroupMember\":true,\"ForbidMultiNationality\":false,\"NewUserCountryCode\":true,\"EnableCNYTWODecimal\":true,\"ShieldMaskRoom\":false,\"EnableAddPrice\":false,\"IgnoreSpecialCancelPolicyNotMatchingFilter\":true,\"StrictCancellationPolicy\":false,\"IgnoreRemitTax\":false,\"ShieldMultiplePrice\":false,\"Enable30MinuteFreeCancel\":true,\"EnablePromotionSubTag\":true,\"EnableHotelNewCustomer\":true,\"DisableIPBlock\":false,\"EnableDirectRate\":false,\"EnableOutputPackageRoom\":true,\"ShieldMaskNakedSale\":false,\"EnableMobileRate\":false,\"EnableMatchPolicyWithExtraPayTax\":false},\"prepayDiscountCampaignIdWhiteList\":[],\"prepayDiscountCampaignIdBlackList\":[],\"customTagKeysToken\":\"V11-d85f958d6176dee854037ca1e5e6cf948b40423c571dc921234c28b0d2e57553\",\"switchListValuesToken\":\"V4-766aefe9110e8a58f5f2d09e7234a8b9339711f60855cffb3f07b90b4796133b\",\"shieldSupplierIdList\":\"1234\",\"shieldVendorIdList\":\"2417\"},\"userRightsInfo\":{\"baseGroupLevel\":\"10003\",\"userChosenCouponInfo\":[],\"hotelNewCustomer\":true,\"userSelectedCountryCode\":\"CN,CN\",\"yxhGroupId\":\"-1\",\"multiCouponTotalCustomAmount\":0},\"reservationToken\":\"AQgBEgoyMDI1LTA3LTAxGhgKAU0SAlBQGgRUUklQMgNDTlk6ATFI3lMiAggAKg8KBXpoLUNOEgNDTlkaATE\\u003d\",\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2025-06-30 14:16:07.017+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", QueryCheckAvailContextResponseType.class)
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo();
        when:
        def result =  SaveCommonDataCostCenterInfoTypeUtil.genCostCenterInfoType(
                Optional.ofNullable(orderCreateRequestType.getCostCenterInfoNew()).map(
                        com.ctrip.corp.bff.framework.template.entity.contract.integration.CostCenterInfo::getCostCenterInputs)
                        .orElse(null), "",
                OrderCreateProcessorOfUtil.buildCheckCostCenterPassengers(
                        orderCreateRequestType.getHotelBookPassengerInputs(), orderCreateRequestType.getCityInput(),
                        checkAvailInfo, null, null, null))
        then:
        result != null
    }

    // 真实数据测试jar-入住人2个 有按人的成本中心 一个填了 一个没填
    def "genCostCenterInfoType-2"() {
        given:
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            public static boolean usePsgEname(Integer cityId, WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
                return false
            }
        }
        OrderCreateRequestType orderCreateRequestType = JsonUtil.fromJson("{\"integrationSoaRequestType\":{\"userInfo\":{\"userId\":\"**********\",\"corpId\":\"testnet\",\"groupId\":\"Gr_00008137\",\"pos\":\"CHINA\",\"accountPos\":\"zh-CN\"},\"token\":\"8C28E0E710FFCA93CFB0345827E40F0EADBD499B06A63D5C025618E9270BB9CC\",\"language\":\"zh-CN\",\"requestId\":\"92964fc97e65495bbbb693e1982a0104\",\"sourceFrom\":\"Online\",\"transactionID\":\"449640799a444990baa25a97de5192a8\",\"gatewayHost\":\"ct.ctrip.com\",\"timezoneOffsetMinutesNew\":\"480\",\"logIndices\":[{\"key\":\"gatewayOperation\",\"value\":\"ordercheck\"},{\"key\":\"gatewayServiceCode\",\"value\":\"20332\"},{\"key\":\"groupId\",\"value\":\"Gr_00008137\"},{\"key\":\"timezoneOffsetMinutesNew\",\"value\":\"480\"},{\"key\":\"gatewayHost\",\"value\":\"ct.ctrip.com\"},{\"key\":\"pvid\",\"value\":\"76\"},{\"key\":\"subChannel\",\"value\":\"\"},{\"key\":\"CID\",\"value\":\"09031088319758937635\"},{\"key\":\"EID\"},{\"key\":\"CIP\",\"value\":\"***************\"},{\"key\":\"miceToken\",\"value\":\"\"},{\"key\":\"groupName\",\"value\":\"test123update\"},{\"key\":\"gatewayIdc\",\"value\":\"SHARB\"},{\"key\":\"traceID\",\"value\":\"\"},{\"key\":\"A\",\"value\":\"ordercheck\"},{\"key\":\"CorpID\",\"value\":\"testnet\"},{\"key\":\"C\",\"value\":\"corpbookinghotelbookservice\"},{\"key\":\"M\",\"value\":\"**********\"},{\"key\":\"sid\",\"value\":\"9\"},{\"key\":\"S\",\"value\":\"8337ea0d0a06364a2e93dd0df2d4210f\"},{\"key\":\"T\",\"value\":\"8C28E0E710FFCA93CFB0345827E40F0EADBD499B06A63D5C025618E9270BB9CC\"},{\"key\":\"pos\",\"value\":\"CHINA\"},{\"key\":\"GE\",\"value\":\"1753282196983\"},{\"key\":\"sourceFrom\",\"value\":\"Online\"},{\"key\":\"corpName\",\"value\":\"测试账号1345（测试一、测试二）\"},{\"key\":\"RID\",\"value\":\"92964fc97e65495bbbb693e1982a0104\"},{\"key\":\"TID\",\"value\":\"449640799a444990baa25a97de5192a8\"},{\"key\":\"VID\",\"value\":\"1753255925239.3d58S7au7lXs\"}],\"ticket\":\"RU0eFNFp71EtJ2BCUUYLNOlGBJv7UAzn5qPw1oteGOcT/8z8eoQ8g+XHr2vdL7k5RfCYAzt/9m65HpqFIF+k2044pLwxT2s60LjJLA0CJs4\\u003d\",\"transferInfo\":[{\"key\":\"iframe\",\"value\":\"F\"},{\"key\":\"channel\",\"value\":\"Online\"}]},\"hotelBookPassengerInputs\":[{\"hotelPassengerInput\":{\"uid\":\"**********\",\"employee\":\"T\",\"external\":\"F\",\"employeeId\":\"skskdk\"},\"passengerBasicInfo\":{\"gender\":\"U\",\"birth\":\"1992-07-11\"},\"name\":\"韩飞龙\",\"isRegistrant\":\"F\",\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"18817570781\",\"transferPhoneNo\":\"18817570781\"},\"nationalityInfo\":{\"nationalityCode\":\"CN\"}},{\"hotelPassengerInput\":{\"uid\":\"2120593229\",\"employee\":\"T\",\"external\":\"F\",\"employeeId\":\"2120593229\"},\"passengerBasicInfo\":{\"gender\":\"U\",\"birth\":\"1988-01-01\"},\"name\":\"杨之恺\",\"isRegistrant\":\"F\",\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"13395765723\",\"transferPhoneNo\":\"13395765723\"},\"nationalityInfo\":{\"nationalityCode\":\"CN\"}}],\"hotelInvoiceInfos\":[],\"cityInput\":{\"cityId\":2},\"hotelContactorInfo\":{\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"18817570781\",\"transferPhoneNo\":\"18817570781\"},\"emailInfo\":{\"email\":\"<EMAIL>\",\"transferEmail\":\"<EMAIL>\"}},\"miceInput\":{},\"hotelBookInput\":{\"hotelDateRangeInfo\":{\"checkIn\":\"2025-08-12\",\"checkOut\":\"2025-08-14\"},\"roomQuantity\":2},\"membershipInfo\":{},\"bookModeInfo\":{\"bookingType\":\"NONE\"},\"costAllocationInfo\":{\"costAllocationToken\":\"gB7duVuU1dv572MCheVzsCv73ZFyLPE3la8BUOfZFJluwWWta3Y4NjmPd7zX9tb8GF4sK7Vys01PN/YM1uW9ySZrjFKxWRG0Z6z7/RFWxEDKB3fXd7Rm9zJTAIpqyOEq\",\"shareAmounts\":[{\"costAllocationAmount\":{\"amount\":\"500\"},\"shareAmountKey\":\"**********\"},{\"costAllocationAmount\":{\"amount\":\"500\"},\"shareAmountKey\":\"2120593229\"}]},\"resourceTokenInfo\":{\"resourceToken\":\"{\\\"hotelResourceToken\\\":{\\\"masterHotelId\\\":********,\\\"hotelId\\\":********,\\\"searchHotelTraceId\\\":\\\"32a1e83537ae42aa9eea1a9e589f478c\\\",\\\"hid\\\":\\\"ASIGMjUwLjAwQgFGSgYIAhABGAFSaQpjdjFfQ0l6bDBRY1FtK1hSQnhvQlV5SURNalV3S2dNeU5UQXlEUW9EUTA1WkVnTkRUbGthQVRFNklETXlZVEZsT0RNMU16ZGhaVFF5WVdFNVpXVmhNV0U1WlRVNE9XWTBOemhqEAAYAVoDQ05ZYg0KA0NOWRIDQ05ZGgExYg0KA0NOWRIDQ05ZGgExaANyCDEwMDAwLjAwegQwLjAwggEgMzJhMWU4MzUzN2FlNDJhYTllZWExYTllNTg5ZjQ3OGOKAQkxNzA1NTA1NjaaASRhNTRhMmI3Yi05MzEwLTRmYzMtOWExOS1kODBlMjc2N2ZlYWOiAQIIAA\\\\u003d\\\\u003d\\\",\\\"hotelType\\\":\\\"C\\\",\\\"bonusPoint\\\":\\\"T\\\",\\\"hotelGeoInfoResourceToken\\\":{\\\"cityId\\\":2,\\\"locationId\\\":114},\\\"hotelMinPrice\\\":{\\\"amount\\\":250.00,\\\"currency\\\":\\\"CNY\\\"}},\\\"roomResourceToken\\\":{\\\"roomId\\\":*********,\\\"baseRoomId\\\":70451465,\\\"ratePlanTraceLogId\\\":\\\"8be707ce8fd847119d62b78375a64572\\\",\\\"allRoomType\\\":\\\"EXIST_C\\\",\\\"bedName\\\":\\\"1张1.8米大床\\\",\\\"windowName\\\":\\\"部分有窗且为天窗\\\",\\\"breakfastInfo\\\":\\\"1份早餐\\\",\\\"roomType\\\":\\\"C\\\",\\\"balanceType\\\":\\\"PP\\\",\\\"tmcPrice\\\":false,\\\"adult\\\":2,\\\"pId\\\":\\\"AQoGMjUwLjAwEgYyNTAuMDAicQpvdjFfQ0p2bDBRY1Fwc3FwVVJvRWJuVnNiQ0lBS2dGVE1nTXlOVEE2QXpJMU1FSU5DZ05EVGxrU0EwTk9XUm9CTVVvZ016SmhNV1U0TXpVek4yRmxOREpoWVRsbFpXRXhZVGxsTlRnNVpqUTNPR009KgNDTlkyDQoDQ05ZEgNDTlkaATEyDQoDQ05ZEgNDTlkaATE6AggAQgcI6Q8QCBgMSgcI6Q8QCBgOUAFYAGIETk9ORWhbeAKAAZvl0QeIAYzl0QegAQA\\\\u003d\\\",\\\"checkAvlId\\\":0,\\\"amadeus\\\":false,\\\"supportAccommodation\\\":\\\"F\\\",\\\"roomOriginCurrency\\\":\\\"CNY\\\",\\\"roomScenarioEnum\\\":\\\"ROOM_LIST\\\",\\\"ratePlanRequestToken\\\":\\\"ASi1L/0AUIUWAMYwrT8AD1UA/NUPWP1LMgB8XLW7591TWn2dTirrNNtRInVp/4UDN1WKMznbkhRFkiqSNk008r3mFDo4UMapU5v/uw+fAJoAnQCSLtap0gT0YM/465oTMl8UpdG1yEpbUhLpllzGbUhOXzLQ3ZEdnekMFhVL4FBQQ2WijJ2pV9hl5GorJuwTrVkj8ws5letdxohmKVy7yTbdJrJCNZlsZdFy5AuLEP8hvwcQ6Vl8B+9fwz+GZ707KWkZZMMgFA3TLoyPFDVkGWCPFRVID8JuAJgAqyg4pcICtIqGWSwWS5ZpVwQBJlJsKBBxhofduNvNhFIM4/zhkd+o6nGmP04diJ7icdPicLSF7BhBO3Hpw4onEw2WxDI/6KpGKmjF24jfbfo27G0gNoxbMTQ+sFJljliMWKbgyJFo7QltcfPTk56btt3th6TXENu7ZrKUUmcK26k+WWGtVm7tgruNMryzPl0Td1i4goqoNvbvQTmVmLP3rgU9mgyjlzIM1VaSgJrU1YsGKpxutU3hBA7wrwbfglHqidRVl/Wt6eny88R5z0QSDJJlEImGaaKT852ijkPg4P5a8C0KmgtVJ0oT8pCYVS2VXvu+sXZBq4y2sdJEMhtadlKt6e2U30taKZ77bviaqtwNSCRJe7RtsCRxVkpgSyMMpHArppuQMMtWOBgKV8XeeWyUS3p6mJ0E1Jz3EWpkH6hxIeOSTkuGWVpNNlV5AyqKZELxAI8iP4r8JPKLyA8iv4f8HPJnyI8hs7Kb2Wwhf7KZw1zht4xlCFlBLpAvJn+i4qLiS5zvOHG+u4qHaueoVYQ755w432oq7RhcD34E93H21ylFYrfAwf3l4FueCJYakbm9LfJ8JAEH99eBc5cckdgNcFz+MvAtXZZbdCE/rUvv2yKxM+Do/krwLXXEPZf1wRic27kXCABWKEDUQMgGYCfM1246k/3nm18gI7HyBA\\\\u003d\\\\u003d\\\",\\\"ratePlanRoomToken\\\":\\\"ASi1L/0AUHEAAAoBQxAAGKbKqVEiAlBQ\\\",\\\"roomUniqueKey\\\":\\\"V1-ASi1L/0AUJEAAAimyqlREgFDGgROT05FIgJQUA\\\\u003d\\\\u003d\\\",\\\"adultNumber\\\":2,\\\"windowType\\\":13,\\\"customRoomAmount\\\":500.00,\\\"customRoomAmountCurrency\\\":\\\"CNY\\\"},\\\"reservationResourceToken\\\":{\\\"wsId\\\":\\\"da82dd1b-3492-4ac3-bbd0-67c8021071bf\\\",\\\"policyToken\\\":\\\"v1KLUv/QBQ5QkA5pM/JDCp6AHEPcTYt01ji45wL0VXqPgk863PDIgYIP7TxVQKGgoqODIANwA2AOtNmb9rv/2n/nULH4nOt2sRDWutNYZp1i2M1S3ld1TOoNhjtIsxAMYYY9z2ixSiOFqGbil+VAwxfWqgK323LaE8YfSLFdqOhlU+j7u2NRDBYEADI90sjAPbwphzWA4JyJbmNIcExGnrAl5npPiWwLqQMi4kVyaVJN4lZXW3P8WC6kU5oY+OEM158FNXyeQ9N34aeE8SKHMunN+uniRHAg0L23atwK5N7FrEKriVXWH68Ac+/yqcVKJcQbV8f6YpycgEHyNIdTi/iiEqNhRNoZYQ9g4WADkbR2OHs7XDfHPx1GI0hhCm5qChCqkLmCKoQ5WqWSxU5Twr8u/PYq9ikzCqqQI6N6AU1ZkudO4cAw\\\\u003d\\\\u003d\\\"},\\\"bookInitResourceToken\\\":{\\\"aggBookPriceResourceToken\\\":{\\\"currency\\\":\\\"CNY\\\",\\\"accountPaymentAmount\\\":1000.00,\\\"individualPaymentAmount\\\":0,\\\"mixPayAccountExtraRatio\\\":0,\\\"mixPayAccountExtraAmount\\\":0},\\\"roomPriceResourceToken\\\":{},\\\"costCenterNewUse\\\":\\\"T\\\"},\\\"bookResourceToken\\\":{\\\"bookInfoStr\\\":\\\"c479b56d8448c7d19206f148e1937c17\\\",\\\"bookVersion\\\":\\\"2\\\"}}\"},\"corpPayInfo\":{\"corpPayType\":\"public\"},\"userActionInfo\":{\"userActionToken\":\"WEB_1_windows_1753282146549_912_912_1.5_mbu_corp_hotel_online_booking_fc408c65d10a451a_5.4.3_1\"},\"approvalFlowInput\":{\"password\":\"\",\"approvalFlowToken\":\"H4sIAAAAAAAA_wXBsRGAIAwAQG0tLa0obHOHJJBkHCBkEWdxAtdwDxdwAP-X3dgcR1KQjgREZKAoDr0ZIyt5Zlln3IJWFLM6RCSx2zFG6aXFhDFrbYTh-a73vKcfyXC0IFMAAAA\",\"approvers\":[{\"uid\":\"**********\",\"level\":\"1\"},{\"uid\":\"**********\",\"level\":\"1\"}],\"attachmentInfos\":[{\"attachmentName\":\"1iq1z12000jbbb2u55987.png\",\"attachmentUrl\":\"https://dimg04.c-ctrip.com/images/1iq1z12000jbbb2u55987.png\"}]},\"rcInfos\":[],\"followApprovalInfoInput\":{\"aiFollow\":\"F\",\"artificialFollow\":\"F\",\"followSelected\":\"F\"},\"bookUrlInfos\":[{\"urlType\":\"SUCCESS\",\"urlValue\":\"https://ct.ctrip.com/hotel-online/order/success?InterfaceName\\u003dCreate_PaymentBill_New\\u0026\"},{\"urlType\":\"COMPLETION\",\"urlValue\":\"https://ct.ctrip.com/hotel-booking/orderSuccess\"},{\"urlType\":\"S_BACK\",\"urlValue\":\"https://ct.ctrip.com/hotel-online/order/success?InterfaceName\\u003dCreate_PaymentBill_New\\u0026\"},{\"urlType\":\"E_BACK\"},{\"urlType\":\"FROM_URL\",\"urlValue\":\"https://ct.ctrip.com/hotel-online-after-booking/pages/orderdetail/index?orderId\\u003d\"},{\"urlType\":\"S_BACK_MICE\",\"urlValue\":\"https://ct.ctrip.com/mice-activities/welfareTourism\"},{\"urlType\":\"FROM_URL_ORDER_DETAIL\"},{\"urlType\":\"OFFLINE_RETURN_URL\",\"urlValue\":\"http://corpint.ct.ctripcorp.com/hotel-online/offlineOrder/success\"},{\"urlType\":\"ORDER_DETAIL_URL\",\"urlValue\":\"https://ct.ctrip.com/hotel-online-after-booking/pages/orderdetail/index?\"}],\"reservationInfo\":{\"reservationType\":\"EmployeeTravel\"},\"paymentInfoInput\":{\"canWechatPayByOthers\":\"F\"},\"clientInfo\":{\"rmsToken\":\"fp\\u003d2F679A-EB4072-2C0B7D\\u0026vid\\u003d1753255925239.3d58S7au7lXs\\u0026pageId\\u003d10650153220\\u0026r\\u003d3034664b850a4d459eb64156db397777\\u0026ip\\u003d***************\\u0026rg\\u003dfin\\u0026kpData\\u003d0_0_0\\u0026kpControl\\u003d0_0_0-0_0_0\\u0026kpEmp\\u003d0_0_0_0_0_0_0_0_0_0-0_0_0_0_0_0_0_0_0_0-0_0_0_0_0_0_0_0_0_0\\u0026screen\\u003d2560x1440\\u0026tz\\u003d+8\\u0026blang\\u003dzh-CN\\u0026oslang\\u003dzh-CN\\u0026ua\\u003dMozilla%2F5.0%20(Windows%20NT%2010.0%3B%20Win64%3B%20x64)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Chrome%2F138.0.0.0%20Safari%2F537.36\\u0026d\\u003dct.ctrip.com\\u0026v\\u003d25\\u0026kpg\\u003d0_0_0_0_0_0_0_0_0_0\\u0026adblock\\u003dF\\u0026cck\\u003dF\\u0026ftoken\\u003d\",\"secondaryChannel\":\"Other-new\"},\"offlineInfo\":{\"forciblyAssure\":\"F\",\"especialAuth\":\"F\",\"autoConfirm\":\"F\",\"exigentOrder\":\"F\",\"operationRule\":\"\"},\"roomInfoInput\":{\"roomRemark\":\"\",\"roomCustomRemark\":\"\"},\"sendInfo\":{\"sendSMS\":\"T\",\"sendEmail\":\"F\"},\"ssoInput\":{\"ssoKey\":\"68a489f0-67c4-11f0-b777-2709e601f219\"},\"flashStayInput\":{\"flashPlatFrom\":\"Online\"},\"hotelPayTypeInput\":[{\"payCode\":\"ROOM\",\"payType\":\"CORP_PAY\"}],\"strategyInfos\":[{\"strategyKey\":\"BOOKING_SCENARIO\",\"strategyValue\":\"HOTEL_LIST\"},{\"strategyKey\":\"BOOKING_WITH_PERSONAL_ACCOUNT\",\"strategyValue\":\"F\"}],\"orderCreateToken\":\"P2GE4XzG1pwyjOrdcsX/qrrtKQPKRC1MjBuykl3dYLAPs13X7AJhHM+b5faodaGD7qg7HK2ceZIF+CVzsPYIFyp4476Ndj8IEY0H43vwcRo\\u003d\",\"useNewOrderCreate\":\"T\",\"costCenterInfoNew\":{\"costCenterInputs\":[{\"value\":\"商旅事业部\",\"itemKey\":\"H4sIAAAAAAAA_-NiDBBidnY2lOIyMjS0BAIDIzMlzpfzV75cPO_lvpkajEbMjr7BAE7c8gMmAAAA\",\"optionItemKey\":\"H4sIAAAAAAAA_-Pifzq17dn01ie7up_smPWyeYUQuoAUt5GJgbmBgaGZuYEhAEfLI7svAAAA\"},{\"value\":\"\",\"itemKey\":\"H4sIAAAAAAAA_-NiDBBidnY2kuIyMjS0BAIDIzMlzpfzV75cPO_lvpkajEbMjr7BAD3l2hAmAAAA\",\"optionItemKey\":\"\"},{\"value\":\"\",\"itemKey\":\"H4sIAAAAAAAA_-NiDBBidnY2lOIyMjQyMLU0NjKyVOJ8NnfFk53dzxp3aTAaMTv6BgMAUePdQSYAAAA\",\"optionItemKey\":\"\"},{\"value\":\"\",\"itemKey\":\"H4sIAAAAAAAA_-NiDBBidnY2kuIyMjQyMLU0NjKyVOJ8NnfFk53dzxp3aTAaMTv6BgMAItr1UiYAAAA\",\"optionItemKey\":\"\"},{\"value\":\"\",\"itemKey\":\"H4sIAAAAAAAA_-Ni9BdiCgmQ4jIyNLQEAgMjMw1GI2ZH32AA1yZhwhoAAAA\",\"optionItemKey\":\"\"},{\"value\":\"\",\"itemKey\":\"H4sIAAAAAAAA_-Ni9BdiCvCT4jIyNLQEAgMjMw1GI2ZH32AALsKmKRoAAAA\",\"optionItemKey\":\"\"},{\"value\":\"\",\"itemKey\":\"H4sIAAAAAAAA_-Ni9BdicjGU4jIyNLQEAgMjMw1GI2ZH32AAYP_mwxoAAAA\",\"optionItemKey\":\"\"},{\"value\":\"\",\"itemKey\":\"H4sIAAAAAAAA_-Ni9BdicjGS4jIyNLQEAgMjMw1GI2ZH32AAqrJPbBoAAAA\",\"optionItemKey\":\"\"}],\"costCenterToken\":\"H4sIAAAAAAAA_xPi4eIyMjS0BAIDIzMhMM_IwNTS2MjIUonRUIvD2S_ewz_E1QcAWngCLikAAAA\"}}", OrderCreateRequestType.class)
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType = JsonUtil.fromJson("{\"baseInfo\":{\"userInfo\":{\"uid\":\"**********\",\"policyUid\":\"\",\"corpId\":\"testnet\",\"eid\":\"\",\"usersCityId\":2,\"userCountryCode\":\"CN\"},\"customCurrency\":\"CNY\",\"website\":\"Online\",\"feeType\":\"C\",\"quantity\":2,\"startTime\":\"2025-08-12T00:00:00+08\",\"endTime\":\"2025-08-14T00:00:00+08\",\"language\":\"CN\",\"locale\":\"zh-CN\",\"startTimeUTC\":\"2025-08-11T16:00:00Z\",\"endTimeUTC\":\"2025-08-13T16:00:00Z\",\"pOS\":\"zh-CN\",\"pOSType\":{\"pOS\":\"zh-CN\",\"countryId\":1},\"platform\":\"online\",\"bookingWithPersonalAccount\":false,\"bookingChannel\":\"ONLINE\",\"iPBLockParam\":[{\"type\":2,\"value\":[1]}]},\"hotelInfo\":{\"subHotelId\":********,\"masterHotelId\":********,\"star\":4,\"rStar\":0,\"starLicence\":false,\"customerEval\":4.0,\"hotelGroupId\":5,\"hotelBrandId\":1584,\"balancePeriod\":\"M\",\"oversea\":false,\"tripBusinessHotel\":true,\"cuHotel\":false,\"hasServiceFee\":false,\"localTimeOffsetHours\":0,\"telephone\":\"+86-21-********-0\",\"geographicalInfo\":{\"locationInfo\":{\"id\":114},\"cityInfo\":{\"id\":2,\"name\":{\"textGB\":\"上海\",\"textEn\":\"Shanghai\"}},\"provinceInfo\":{\"id\":2},\"countryInfo\":{\"id\":1},\"coordinateInfo\":{}},\"address\":{\"textGB\":\"福泉路99号\",\"textEn\":\"No.99 Fuquan Road\"},\"hotelName\":{\"textGB\":\"上海携程美居酒店\",\"textEn\":\"Mercure Shanghai Hongqiao SOHO\"},\"currentCountry\":true,\"timeZoneId\":\"Asia/Shanghai\",\"hotelSustainable\":{\"sustainableCertification\":false}},\"roomInfo\":{\"subRoomId\":*********,\"roomName\":{\"textGB\":\"标准大床房12333-reseller测试partner预付资源\",\"textEn\":\"Standard Room\"},\"basicRoomName\":{\"textGB\":\"标准大床房12333-reseller测试partner预付资源\",\"textEn\":\"Standard Room\"},\"masterBasicRoomId\":70451465,\"masterBasicRoomName\":{\"textGB\":\"标准大床房\"},\"roomType\":\"C\",\"tmcPriceType\":\"NONE\",\"balanceType\":\"PP\",\"gdsType\":\"None\",\"mealType\":4,\"priceSuitPropertyValueId\":0,\"ratePlanKey\":{\"checkAvlID\":0,\"ratePlanID\":\"0\"},\"addPrice\":false,\"invoiceType\":\"Special\",\"invoiceLimitType\":\"NotSupported\",\"applyAreaPropertyId\":0,\"maxGuestCountPerRoom\":2,\"maskRoom\":false,\"hourlyRoom\":false,\"corpPrice\":true,\"manualGds\":false,\"joined30MinutesFreeCancel\":false,\"premiumRoom\":false,\"welfareRoom\":false,\"groupMemberShip\":false,\"earnHotelPoints\":false,\"bonusPointInfo\":{\"bonusPointRoom\":false},\"earlyArrivalTime\":\"2025-08-12T14:00:00+08\",\"lastCancelTime\":\"2025-08-11T12:00:00+08\",\"cnyAmount\":1000.00,\"originAmountInfo\":{\"amount\":1000.00,\"currency\":\"CNY\",\"exchange\":1},\"customAmountInfo\":{\"amount\":1000.00,\"currency\":\"CNY\",\"exchange\":1},\"originCostInfo\":{\"amount\":1000,\"currency\":\"CNY\",\"exchange\":1},\"originCostBeforeTaxInfo\":{\"amount\":1000,\"currency\":\"CNY\",\"exchange\":1},\"roomDiscountInfo\":{\"promotionSwitchOn\":false,\"roomDailyDiscountInfo\":[],\"originPromotionAmount\":{\"price\":0,\"currency\":\"CNY\"},\"customPromotionAmount\":{\"price\":0,\"currency\":\"CNY\"}},\"roomDailyInfo\":[{\"effectDate\":\"2025-08-12T00:00:00+08\",\"cnyAmount\":250.00,\"amount\":250.00,\"customAmount\":250.00,\"afterPromotionCnyAmount\":250.00,\"afterPromotionCustomAmount\":250.00,\"cost\":250,\"costBeforeTax\":250,\"customizedCnyAmount\":0,\"mealNumber\":1,\"holdDeadline\":0,\"afterPromotionCustomAmountExcludeTax\":250.00,\"afterPromotionCnyAmountExcludeTax\":250.00,\"customAmountExcludeTax\":250.00,\"cnyAmountExcludeTax\":250.00},{\"effectDate\":\"2025-08-13T00:00:00+08\",\"cnyAmount\":250.00,\"amount\":250.00,\"customAmount\":250.00,\"afterPromotionCnyAmount\":250.00,\"afterPromotionCustomAmount\":250.00,\"cost\":250,\"costBeforeTax\":250,\"customizedCnyAmount\":0,\"mealNumber\":1,\"holdDeadline\":0,\"afterPromotionCustomAmountExcludeTax\":250.00,\"afterPromotionCnyAmountExcludeTax\":250.00,\"customAmountExcludeTax\":250.00,\"cnyAmountExcludeTax\":250.00}],\"paymentRulesInfo\":{\"paymentGuaranteePoly\":\"TRIP\",\"canTravelMoneyPay\":false,\"forceVccPay\":false,\"canCreditCardPay\":false},\"longRentalInfo\":{\"longRental\":false,\"needInquire\":false},\"holdRoomInfo\":{\"holdRoom\":false},\"extraPayTaxMultiCurrencyAmountInfo\":{\"customExtraPayTaxTotalAmount\":0,\"cnyExtraPayTaxTotalAmount\":0},\"pricedRoom\":false,\"generalizedPremiumRoom\":false,\"customAmountExcludeTaxInfo\":{\"amount\":1000.00,\"currency\":\"CNY\",\"exchange\":1},\"supportAnticipation\":false,\"roomRightInfoList\":[],\"invoiceTypeExcludeAddPrice\":\"Special\",\"roomMealInfo\":{\"mealType\":4,\"mealDescList\":[\"1份早餐\"],\"dailyMealInfoList\":[{\"effectDateUTC\":\"2025-08-11T16:00:00Z\",\"mealDescList\":[\"\"]},{\"effectDateUTC\":\"2025-08-12T16:00:00Z\",\"mealDescList\":[\"1份早餐\"]},{\"effectDateUTC\":\"2025-08-13T16:00:00Z\",\"mealDescList\":[\"1份早餐\"]}]},\"saleRoomTags\":[{\"tagCode\":\"GDXYJ\",\"tagDesc\":\"\",\"configInfoList\":[{\"position\":301,\"priority\":10,\"style\":\"CONTRACT\"}]}],\"taxDetails\":[],\"pid\":\"AQoGMjUwLjAwEgYyNTAuMDAicQpvdjFfQ0p2bDBRY1Fwc3FwVVJvRWJuVnNiQ0lBS2dGVE1nTXlOVEE2QXpJMU1FSU5DZ05EVGxrU0EwTk9XUm9CTVVvZ016SmhNV1U0TXpVek4yRmxOREpoWVRsbFpXRXhZVGxsTlRnNVpqUTNPR009KgNDTlkyDQoDQ05ZEgNDTlkaATEyDQoDQ05ZEgNDTlkaATE6AggAQgcI6Q8QCBgMSgcI6Q8QCBgOUAFYAGIETk9ORWhbeAKAAZvl0QeIAYzl0QegAQA\\u003d\",\"supplierChannel\":\"HolidayTours\",\"onlyGroupMemberCanBook\":false,\"vatAddPriceInfo\":{\"vatAddPriceRoomFlag\":false,\"vatCompensatePriceFlag\":false,\"vatInvoicePoint\":0.0600,\"vatSpecialInvoicePoint\":0.0000,\"supplierInvoicePoint\":0.06000,\"needCompensateVatDiff\":false,\"vatAddPriceType\":\"BOTH_POST\"},\"individualInvoicePostType\":\"BY_ORDER\",\"resourceAgent\":\"TripBiz\",\"basicRoomStaticInfo\":{\"ranOfHouseInfo\":{\"ranOfHouse\":false}}},\"bookingRules\":{\"cancelPenalties\":{\"paymentGuaranteePoly\":\"TRIP\",\"cancelDeductDetail\":{\"originDeductionPrice\":{\"price\":1000.00,\"currency\":\"CNY\"},\"customDeductionPrice\":{\"price\":1000.00,\"currency\":\"CNY\"}},\"guaranteePolicyInfo\":{}},\"authorizationRestriction\":{\"latestAuthorizeTime\":\"\"},\"confirmRules\":{\"justifyConfirm\":false,\"forbidNotImConfirm\":false},\"welfareRoomRules\":{},\"limitDepartureDateTime\":{\"departureEnd\":\"2025-08-14T11:00:00+08\",\"departureEndUTC\":\"2025-08-14T03:00:00Z\"},\"certificateInfo\":{\"needCertificate\":false,\"supportCertificateType\":[]},\"minLOS\":1,\"cancelPolicyInfo\":{\"cancelType\":\"LADDER_FREE\",\"freeCancelPolicySceneType\":\"NOT_SUPPORTED\",\"lastCancelTimeUTC\":\"2025-08-11T04:00:00Z\",\"guaranteePolicyInfo\":{\"guaranteeType\":\"FULL_PREPAY\",\"paymentGuaranteePoly\":\"TRIP\",\"guaranteeReason\":\"NONE\",\"guaranteePriceInfo\":{\"originGuaranteePrice\":{\"price\":1000.00,\"currency\":\"CNY\"},\"customGuaranteePrice\":{\"price\":1000.00,\"currency\":\"CNY\"}},\"cancelDeductDetailInfo\":[{\"deductionStartTimeUTC\":\"1990-01-01T00:00:00Z\",\"deductionEndTimeUTC\":\"2025-08-11T04:00:00Z\",\"deductionRatio\":0,\"deductionType\":\"RADIO\",\"originDeductionPrice\":{\"price\":0,\"currency\":\"CNY\"},\"customDeductionPrice\":{\"price\":0,\"currency\":\"CNY\"}},{\"deductionStartTimeUTC\":\"2025-08-11T04:00:00Z\",\"deductionEndTimeUTC\":\"0001-01-01T00:00:00Z\",\"deductionRatio\":1,\"deductionType\":\"RADIO\",\"originDeductionPrice\":{\"price\":1000.00,\"currency\":\"CNY\"},\"customDeductionPrice\":{\"price\":1000.00,\"currency\":\"CNY\"}}]},\"localLastCancelTime\":\"2025-08-11 12:00:00\",\"needGuarantee\":false},\"limitArrivalDateTime\":{\"arrivalFromUTC\":\"2025-08-12T06:00:00Z\",\"arrivalEndUTC\":\"2025-08-12T22:00:00Z\",\"defaultArrivalTimeEnd\":false,\"localEarlyArrivalTime\":\"2025-08-12 14:00:00\",\"localLastArrivalTime\":\"2025-08-13 06:00:00\"},\"rPLimitInfo\":{\"minLos\":1,\"maxLos\":999,\"minAdvancedBookingDays\":0,\"maxAdvancedBookingDays\":0},\"billingGuestInfo\":{\"guestsNameLanguages\":[\"zh\",\"en\"]},\"restrictRuleList\":[]},\"bookExtInfo\":{\"featureControlFlagMap\":{\"EnableMemberPoints\":false,\"EnableXMinutesFreeCancel\":false,\"BrandNewCustomer\":true,\"EnableShadowDiscount\":false,\"EnableYXH\":false,\"EnablePrepayDiscount\":false,\"EnableNewGroupMember\":false,\"ForbidMultiNationality\":false,\"NewUserCountryCode\":true,\"EnableCNYTWODecimal\":true,\"ShieldMaskRoom\":true,\"EnableAddPrice\":false,\"IgnoreSpecialCancelPolicyNotMatchingFilter\":false,\"StrictCancellationPolicy\":false,\"IgnoreRemitTax\":false,\"ShieldMultiplePrice\":false,\"Enable30MinuteFreeCancel\":false,\"EnablePromotionSubTag\":true,\"EnableHotelNewCustomer\":false,\"DisableIPBlock\":false,\"EnableDirectRate\":false,\"EnableOutputPackageRoom\":false,\"ShieldMaskNakedSale\":false,\"EnableMobileRate\":true,\"EnableMatchPolicyWithExtraPayTax\":false}},\"userRightsInfo\":{\"userChosenCouponInfo\":[],\"hotelNewCustomer\":true,\"userSelectedCountryCode\":\"CN,CN\"},\"reservationToken\":\"AQgCEgoyMDI1LTA4LTEyGhgKAUMSAlBQGgRUUklQIgROb25lSKbKqVEiAggAKgcKBXpoLUNO\",\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2025-07-23 22:49:56.922+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", QueryCheckAvailContextResponseType.class);
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo();
        when:
        def result =  SaveCommonDataCostCenterInfoTypeUtil.genCostCenterInfoType(
                Optional.ofNullable(orderCreateRequestType.getCostCenterInfoNew()).map(
                        com.ctrip.corp.bff.framework.template.entity.contract.integration.CostCenterInfo::getCostCenterInputs)
                        .orElse(null), "",
                OrderCreateProcessorOfUtil.buildCheckCostCenterPassengers(
                        orderCreateRequestType.getHotelBookPassengerInputs(), orderCreateRequestType.getCityInput(),
                        checkAvailInfo, null, null, null))
        then:
        result != null
    }
}
