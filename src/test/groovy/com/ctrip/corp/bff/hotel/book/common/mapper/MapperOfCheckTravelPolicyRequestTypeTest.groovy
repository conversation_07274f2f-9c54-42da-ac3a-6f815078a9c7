package com.ctrip.corp.bff.hotel.book.common.mapper

import com.ctrip.corp.agg.hotel.expense.contract.model.BaseChargeAmount
import com.ctrip.corp.agg.hotel.expense.contract.model.ChargeAmountInfoType
import com.ctrip.corp.agg.hotel.expense.contract.model.ServiceChargePriceType
import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckAvailResponseType
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelBrandItem
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelItem
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelRatePlan
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomItem
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.AmountInfoType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.ApprovalBillInfoType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.IntlServiceChargeInfoType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.RoomDailyPriceInfoType
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.FlashStayInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceDetailInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.AllocationResultToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.BookInitResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ServiceChargeResourceToken
import com.ctrip.corp.bff.framework.specific.common.entity.costcenter.old.CheckCostCenterPassenger
import com.ctrip.corp.bff.framework.specific.common.utils.SaveCommonDataCostCenterInfoTypeUtil
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.PosEnum
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CostCenterInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CostCenterInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerBasicInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.RCInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.hotel.book.common.enums.HotelGuaranteeTypeEnum
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum
import com.ctrip.corp.bff.hotel.book.common.util.BookingInitUtil
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType
import com.ctrip.corp.hotelbook.commonws.entity.CorpXProductInfoType
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodResponseType
import com.ctrip.corp.hotelbook.commonws.entity.PaymentMethodInfoType
import com.ctrip.model.CalculateServiceChargeV2ResponseType
import com.ctrip.soa._20183.CostCenterInfoType
import com.ctrip.soa._20183.CostCenterItemInfoType
import com.ctrip.soa._20183.PassengerCostCenterInfoType
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2024/12/2 15:22
 *
 */
class MapperOfCheckTravelPolicyRequestTypeTest extends Specification {


    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }
    @Unroll
    def "testBuildServiceChargePaymentType with #description"() {
        given:
        MapperOfCheckTravelPolicyRequestType mapper = new MapperOfCheckTravelPolicyRequestType()
        List<HotelPayTypeInput> hotelPayTypeInputs = hotelPayTypeInputsList
        ResourceToken resourceToken = new ResourceToken(bookInitResourceToken: new BookInitResourceToken(serviceChargeResourceToken: new ServiceChargeResourceToken(serviceChargeAmount: new BigDecimal(20))))

        expect:
        mapper.buildServiceChargePaymentType(hotelPayTypeInputs, selectedPayType, resourceToken) == expectedResult

        where:
        description                   | hotelPayTypeInputsList                                                                                                                  | selectedPayType                     | expectedResult
        "selectedPayType is CORP_PAY" | [new HotelPayTypeInput(payType: "CORP_PAY", payCode: "ROOM")]                                                                           | HotelPayTypeEnum.CORP_PAY           | null
        "selectedPayType is SELF_PAY" | [new HotelPayTypeInput(payType: "SELF_PAY", payCode: "ROOM")]                                                                           | HotelPayTypeEnum.SELF_PAY           | null
        "selectedPayType is MIX_PAY"  | [new HotelPayTypeInput(payType: "CASH", payCode: "ROOM")]                                                                               | HotelPayTypeEnum.CASH               | "UNKNOWN"
        "selectedPayType is NONE"     | [new HotelPayTypeInput(payType: "GUARANTEE_CORP_PAY", payCode: "ROOM"), new HotelPayTypeInput(payType: "CORP_PAY", payCode: "SERVICE")] | HotelPayTypeEnum.GUARANTEE_CORP_PAY | "CORP_PAY"
        "selectedPayType is null"     | [new HotelPayTypeInput(payType: "GUARANTEE_SELF_PAY", payCode: "ROOM"), new HotelPayTypeInput(payType: "SELF_PAY", payCode: "SERVICE")] | HotelPayTypeEnum.GUARANTEE_SELF_PAY | "PERSONAL_PAY"
    }

    @Unroll
    def "buildServiceChargePaymentType"() {
        given:
        MapperOfCheckTravelPolicyRequestType mapper = new MapperOfCheckTravelPolicyRequestType()
        List<HotelPayTypeInput> hotelPayTypeInputs = hotelPayTypeInputsList

        expect:
        mapper.buildServiceChargePaymentType(hotelPayTypeInputs, selectedPayType, servicePayType) == expectedResult

        where:
        description                   | hotelPayTypeInputsList                                                                                                                  | selectedPayType                     | servicePayType            | expectedResult
        "selectedPayType is CORP_PAY" | [new HotelPayTypeInput(payType: "CORP_PAY", payCode: "ROOM")]                                                                           | HotelPayTypeEnum.CORP_PAY           | HotelPayTypeEnum.CORP_PAY | null
        "selectedPayType is SELF_PAY" | [new HotelPayTypeInput(payType: "SELF_PAY", payCode: "ROOM")]                                                                           | HotelPayTypeEnum.SELF_PAY           | HotelPayTypeEnum.CORP_PAY | null
        "selectedPayType is CASH"     | [new HotelPayTypeInput(payType: "CASH", payCode: "ROOM")]                                                                               | HotelPayTypeEnum.CASH               | HotelPayTypeEnum.NONE     | "UNKNOWN"
        "selectedPayType is NONE"     | [new HotelPayTypeInput(payType: "GUARANTEE_CORP_PAY", payCode: "ROOM"), new HotelPayTypeInput(payType: "CORP_PAY", payCode: "SERVICE")] | HotelPayTypeEnum.GUARANTEE_CORP_PAY | HotelPayTypeEnum.CORP_PAY | "CORP_PAY"
        "selectedPayType is null"     | [new HotelPayTypeInput(payType: "GUARANTEE_SELF_PAY", payCode: "ROOM"), new HotelPayTypeInput(payType: "SELF_PAY", payCode: "SERVICE")] | HotelPayTypeEnum.GUARANTEE_SELF_PAY | HotelPayTypeEnum.SELF_PAY | "PERSONAL_PAY"
    }

    @Unroll
    def "testBuildGuaranteeMethod with different scenarios"() {
        given:
        MapperOfCheckTravelPolicyRequestType mapper = new MapperOfCheckTravelPolicyRequestType()

        when: "calling buildGuaranteeMethod with specific parameters"
        def result = mapper.buildGuaranteeMethod(roomPayType, guaranteePayType)

        then: "the result should be as expected"
        result == expectedResult

        where:
        roomPayType                         | guaranteePayType                      || expectedResult
        HotelPayTypeEnum.CASH               | null                                  || "NONE"
        HotelPayTypeEnum.CASH               | HotelGuaranteeTypeEnum.CORP_GUARANTEE || "ACCOUNT_GUARANTEE"
        HotelPayTypeEnum.CASH               | HotelGuaranteeTypeEnum.SELF_GUARANTEE || "INDIVIDUAL_GUARANTEE"
        HotelPayTypeEnum.GUARANTEE_CORP_PAY | HotelGuaranteeTypeEnum.CORP_GUARANTEE || null
        HotelPayTypeEnum.GUARANTEE_SELF_PAY | HotelGuaranteeTypeEnum.SELF_GUARANTEE || null
        HotelPayTypeEnum.CORP_PAY           | null                                  || null
        HotelPayTypeEnum.SELF_PAY           | null                                  || null
    }


    @Unroll
    def "testGetBookInfoType with different scenarios"() {
        given:
        MapperOfCheckTravelPolicyRequestType mapper = new MapperOfCheckTravelPolicyRequestType()
        HotelInsuranceInput hotelInsuranceInput = new HotelInsuranceInput()
        List<HotelPayTypeInput> hotelPayTypeInputs = [new HotelPayTypeInput()]
        GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponseType = new GetSupportedPaymentMethodResponseType()
        FlashStayInput flashStayInput = new FlashStayInput()
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>(){
                    {
                        put("RepeatOrderControlCorp", "T")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        CalculateServiceChargeV2ResponseType calculateServiceChargeV2ResponseType = new CalculateServiceChargeV2ResponseType()
        List<RCInput> rcInfos = []
        ResourceToken resourceToken = new ResourceToken()
        ApprovalInput approvalInput = new ApprovalInput()
        HotelPayTypeEnum selectedPayType = HotelPayTypeEnum.CORP_PAY

        when: "scene is SCENE_PAY_TYPE_FROM_INPUT"
        def result = mapper.getBookInfoType(hotelInsuranceInput, hotelPayTypeInputs, getSupportedPaymentMethodResponseType, flashStayInput, accountInfo, calculateServiceChargeV2ResponseType, rcInfos, "PAY_TYPE_FROM_INPUT", resourceToken, approvalInput, selectedPayType, null, null, new IntegrationSoaRequestType(userInfo:new UserInfo(pos: PosEnum.CHINA)))

        then: "verify the result"
        result.paymentType == "CORP_PAY"
        result.guaranteeMethod == null

        when: "scene is not SCENE_PAY_TYPE_FROM_INPUT"
        getSupportedPaymentMethodResponseType.setPaymentMethodList(Arrays.asList(new PaymentMethodInfoType(paymentMethod: "SELF_PAY")))
        result = mapper.getBookInfoType(hotelInsuranceInput, hotelPayTypeInputs, getSupportedPaymentMethodResponseType, flashStayInput, accountInfo, calculateServiceChargeV2ResponseType, rcInfos, "OTHER_SCENE", resourceToken, approvalInput, selectedPayType, null, null, new IntegrationSoaRequestType(userInfo:new UserInfo(pos: PosEnum.CHINA)))

        then: "verify the result"
        result.paymentType == "PERSONAL_PAY"



        when: "scene is HOTEL_CHECK_AVAIL"
        getSupportedPaymentMethodResponseType.setPaymentMethodList(Arrays.asList(new PaymentMethodInfoType(paymentMethod: "MIX_PAY")))
        hotelPayTypeInputs.add(new HotelPayTypeInput(payCode: "ROOM", payType: "MIX_PAY"))
        result = mapper.getBookInfoType(hotelInsuranceInput, hotelPayTypeInputs, getSupportedPaymentMethodResponseType, flashStayInput, accountInfo, calculateServiceChargeV2ResponseType, rcInfos, "OTHER_SCENE", resourceToken, approvalInput, selectedPayType, Arrays.asList(new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "T")), null, new IntegrationSoaRequestType(userInfo:new UserInfo(pos: PosEnum.CHINA)))

        then: "verify the result"
        result.corpXProductInfo == null
    }

    @Unroll
    def "testBuildServiceChargeType with #description"() {
        given:
        MapperOfCheckTravelPolicyRequestType mapper = new MapperOfCheckTravelPolicyRequestType()
        List<HotelPayTypeInput> hotelPayTypeInputs = hotelPayTypeInputsList

        when:
        String result = mapper.buildServiceChargeType(hotelPayTypeInputs)

        then:
        result == expectedResult

        where:
        description               | hotelPayTypeInputsList                                           | expectedResult
        "CORP_PAY"                | [new HotelPayTypeInput(payType: "CORP_PAY", payCode: "SERVICE")] | "CORP_PAY_SERVICE_CHARGE"
        "SELF_PAY"                | [new HotelPayTypeInput(payType: "SELF_PAY", payCode: "SERVICE")] | "PERSONAL_PAY_SERVICE_CHARGE"
        "ROOM_PAY_TYPE_CORP_PAY"  | [new HotelPayTypeInput(payType: "CORP_PAY", payCode: "ROOM")]    | "CORP_PAY_SERVICE_CHARGE"
        "DEFAULT_TO_PERSONAL_PAY" | [new HotelPayTypeInput(payType: "MIX_PAY", payCode: "ROOM")]     | "PERSONAL_PAY_SERVICE_CHARGE"
    }

    @Unroll
    def "testBuildPolicyUid with #description"() {
        given:
        MapperOfCheckTravelPolicyRequestType mapper = new MapperOfCheckTravelPolicyRequestType()
        HotelPolicyInput policyInput = new HotelPolicyInput(policyInput: new PolicyInput(policyUid: policyUid))
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("HotelBookPolicy", "P")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();

        when:
        String result = mapper.buildPolicyUid(policyInput, scene, accountInfo)

        then:
        result == expectedResult

        where:
        description                        | policyUid      | scene                 | isPolicyModel | expectedResult
        "scene is PAY_TYPE_FROM_INPUT"     | "policyUid123" | "PAY_TYPE_FROM_INPUT" | "P"           | "policyUid123"
        "scene is PAY_TYPE_FROM_INPUT"     | null           | "PAY_TYPE_FROM_INPUT" | "P"           | ""
        "scene is not PAY_TYPE_FROM_INPUT" | "policyUid456" | "OTHER_SCENE"         | "P"           | "policyUid456"
        "scene is not PAY_TYPE_FROM_INPUT" | null           | "OTHER_SCENE"         | "P"           | ""
    }

    @Unroll
    def "testBuildCardUid with #description"() {
        given:
        MapperOfCheckTravelPolicyRequestType mapper = new MapperOfCheckTravelPolicyRequestType()
        HotelPolicyInput policyInput = new HotelPolicyInput(policyInput: new PolicyInput(policyUid: policyUid))
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("HotelBookPolicy", "C")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();

        when:
        String result = mapper.buildPolicyUid(policyInput, scene, accountInfo)

        then:
        result == expectedResult

        where:
        description                        | policyUid      | scene                 | isPolicyModel | expectedResult
        "scene is PAY_TYPE_FROM_INPUT"     | "policyUid123" | "PAY_TYPE_FROM_INPUT" | "C"           | null
    }

    @Unroll
    def "test getCorpXProductInfos"() {
        given:
        MapperOfCheckTravelPolicyRequestType mapper = new MapperOfCheckTravelPolicyRequestType()
        List<StrategyInfo> strategyInfos = Arrays.asList(new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "T"))

        when:
        List<CorpXProductInfoType> result = mapper.getCorpXProductInfos(null, null, strategyInfos)

        then:
        result == null
    }

    def "test getAmountInfoType"() {
        given:
        def checkAvailResponseType = new CheckAvailResponseType(
                "hotelRatePlan": new HotelRatePlan(
                        "roomInfo": new RoomItem(balanceType: "PP"),
                        "hotelInfo": new HotelItem(
                                "hotelBrandInfo": new HotelBrandItem(
                                        "groupId": 1234
                                )
                        )
                )
        )
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo()
        HotelBookInput hotelBookInput = new HotelBookInput()
        CalculateServiceChargeV2ResponseType calculateServiceChargeV2ResponseType = new CalculateServiceChargeV2ResponseType()
        List<HotelPayTypeInput> hotelPayTypeInputs = new ArrayList<>();
        ResourceToken resourceToken = new ResourceToken()
        MapperOfCheckTravelPolicyRequestType mapper = new MapperOfCheckTravelPolicyRequestType()
        List<StrategyInfo> strategyInfos = Arrays.asList(new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "T"))
        def integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: new UserInfo(pos: PosEnum.CHINA))

        when:
        AmountInfoType result = mapper.getAmountInfoType(checkAvailInfo, null, hotelBookInput, calculateServiceChargeV2ResponseType, "", hotelPayTypeInputs, resourceToken, HotelPayTypeEnum.CORP_PAY, strategyInfos, null, null, integrationSoaRequestType)

        then:
        result.roomDailyPriceList != null
        result.roomDailyPriceList.size() == 0


        when:
        strategyInfos = Arrays.asList(new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "F"))
        result = mapper.getAmountInfoType(checkAvailInfo, null, hotelBookInput, calculateServiceChargeV2ResponseType, "", hotelPayTypeInputs, resourceToken, HotelPayTypeEnum.CORP_PAY, strategyInfos, null, null, integrationSoaRequestType)

        then:
        result.roomDailyPriceList == null
    }

    @Unroll
    def "testGetPaymentType with #description"() {
        given: "A HotelPayTypeEnum value"
        MapperOfCheckTravelPolicyRequestType mapper = new MapperOfCheckTravelPolicyRequestType()
        // No additional setup required

        when: "Calling getPaymentType"
        def result = mapper.getPaymentType(hotelPayTypeEnum)

        then: "The result should match the expected value"
        result == expectedResult

        where:
        description                   | hotelPayTypeEnum                    || expectedResult
        "CORP_PAY scenario"           | HotelPayTypeEnum.CORP_PAY           || "CORP_PAY"
        "ADVANCE_PAY scenario"        | HotelPayTypeEnum.ADVANCE_PAY        || "CORP_PAY"
        "FLASH_STAY_PAY scenario"     | HotelPayTypeEnum.FLASH_STAY_PAY     || "CORP_PAY"
        "SELF_PAY scenario"           | HotelPayTypeEnum.SELF_PAY           || "PERSONAL_PAY"
        "CASH scenario"               | HotelPayTypeEnum.CASH               || "PERSONAL_PAY"
        "UNION_PAY scenario"          | HotelPayTypeEnum.UNION_PAY          || "PERSONAL_PAY"
        "GUARANTEE_SELF_PAY scenario" | HotelPayTypeEnum.GUARANTEE_SELF_PAY || "UNKNOWN"
        "GUARANTEE_CORP_PAY scenario" | HotelPayTypeEnum.GUARANTEE_CORP_PAY || "UNKNOWN"
        "MIX_PAY scenario"            | HotelPayTypeEnum.MIX_PAY            || "MIX_PAY"
        "PRBAL scenario"              | HotelPayTypeEnum.PRBAL              || hotelPayTypeEnum.getCode()
        "UNKNOWN scenario"            | null                                || "UNKNOWN"
    }


    @Unroll
    def "testBuildPaymentType with #description"() {
        given: "A HotelPayTypeEnum value"
        MapperOfCheckTravelPolicyRequestType mapper = new MapperOfCheckTravelPolicyRequestType()
        // No additional setup required

        when: "Calling buildPaymentType"
        def result = mapper.buildPaymentType(hotelPayTypeEnum)

        then: "The result should match the expected value"
        result == expectedResult

        where:
        description                   | hotelPayTypeEnum                            || expectedResult
        "CORP_PAY scenario"           | HotelPayTypeEnum.CORP_PAY                   || "CORP_PAY"
        "ADVANCE_PAY scenario"        | HotelPayTypeEnum.ADVANCE_PAY                || "CORP_PAY"
        "FLASH_STAY_PAY scenario"     | HotelPayTypeEnum.FLASH_STAY_PAY             || "CORP_PAY"
        "SELF_PAY scenario"           | HotelPayTypeEnum.SELF_PAY                   || "PERSONAL_PAY"
        "CASH scenario"               | HotelPayTypeEnum.CASH                       || "PERSONAL_PAY"
        "UNION_PAY scenario"          | HotelPayTypeEnum.UNION_PAY                  || "PERSONAL_PAY"
        "GUARANTEE_SELF_PAY scenario" | HotelPayTypeEnum.GUARANTEE_SELF_PAY         || "PERSONAL_PAY"
        "GUARANTEE_CORP_PAY scenario" | HotelPayTypeEnum.GUARANTEE_CORP_PAY         || "PERSONAL_PAY"
        "MIX_PAY scenario"            | HotelPayTypeEnum.MIX_PAY                    || "MIX_PAY"
        "PRBAL scenario"              | HotelPayTypeEnum.PRBAL                      || "PRBAL"
        "PRBAL scenario"              | HotelPayTypeEnum.CORP_CREDIT_CARD_GUARANTEE || "PERSONAL_PAY"
        "UNKNOWN scenario"            | null                                        || "PERSONAL_PAY"
    }


    def "testGetApprovalBillInfo - first if condition"() {
        given: "Mocked inputs and dependencies"
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            public static boolean hotelCheckAvail(List<StrategyInfo> strategyInfos) {
                return true
            }
        }
        def mapper = new MapperOfCheckTravelPolicyRequestType()
        def approvalInput = new ApprovalInput(subApprovalNo: "subApprovalNo")
        def accountInfo = Mock(WrapperOfAccount.AccountInfo)
        def checkAvailInfo = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo)
        def allocationResultToken = Mock(AllocationResultToken)
        def hotelBookPassengerInputs = []
        def cityInput = new CityInput(cityId: 123)
        def corpPayInfo = Mock(CorpPayInfo)
        def costCenterStr = null
        def scene = "someScene"
        def strategyInfos = Arrays.asList(new StrategyInfo(strategyKey: "HOTEL_CHECK_AVAIL", strategyValue: "T"))

        accountInfo.isPreApprovalRequired(_ as Boolean, _ as CorpPayInfo) >> true

        when: "Calling getApprovalBillInfo"
        def result = mapper.getApprovalBillInfo(approvalInput, accountInfo, checkAvailInfo, allocationResultToken,
                hotelBookPassengerInputs, cityInput, corpPayInfo, costCenterStr, scene, strategyInfos, null, new IntegrationSoaRequestType(userInfo: new UserInfo(pos: PosEnum.CHINA)), null, null, null)

        then: "The result should contain the pre-approval number"
        result instanceof ApprovalBillInfoType
        result.preApprovalNumber == "subApprovalNo"


        when: "Calling getApprovalBillInfo"
        result = mapper.getApprovalBillInfo(approvalInput, accountInfo, checkAvailInfo, allocationResultToken,
                hotelBookPassengerInputs, cityInput, corpPayInfo, costCenterStr, scene, strategyInfos, null, new IntegrationSoaRequestType(userInfo: new UserInfo(pos: PosEnum.AUSTRIA)), null, null, null)

        then: "The result should contain the pre-approval number"
        result == null
    }

    def "testBuildIntlServiceChargeInfoType"() {
        given: "A MapperOfCheckTravelPolicyRequestType instance and mocked inputs"
        def mapper = new MapperOfCheckTravelPolicyRequestType()
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            public static boolean intlServiceChargeInfo(Map<String, StrategyInfo> strategyInfoMap) {
                return true
            }
        }
        new MockUp<BookingInitUtil>() {
            @Mock
            public static ChargeAmountInfoType getChargeAmountInfoType(CalculateServiceChargeV2ResponseType responseType,
                                                                       HotelPayTypeEnum servicePayType, HotelPayTypeEnum roomPayType) {
                return new ChargeAmountInfoType(chargeAmountPack: new BaseChargeAmount(chargeAmountCustomCurrency: new ServiceChargePriceType(amount: new BigDecimal(30))))
            }
        }

        when: "Calling buildIntlServiceChargeInfoType with valid inputs"
        IntlServiceChargeInfoType result = mapper.buildIntlServiceChargeInfoType(
                new HashMap<String, StrategyInfo>(), "PAY_TYPE_FROM_INPUT",
                new ResourceToken(bookInitResourceToken: new BookInitResourceToken(serviceChargeResourceToken: new ServiceChargeResourceToken(serviceChargeAmount: new BigDecimal(20)))),
                null, HotelPayTypeEnum.CORP_PAY, HotelPayTypeEnum.CORP_PAY
        )

        then: "The result should not be null and should match expected values"
        result != null
        result.serviceChargeType == "CORP_PAY_SERVICE_CHARGE"
        result.amount == new BigDecimal(20)


        when: "Calling buildIntlServiceChargeInfoType with null strategyInfoMap"
        result = mapper.buildIntlServiceChargeInfoType(
                null, "", new ResourceToken(),
                new CalculateServiceChargeV2ResponseType(), HotelPayTypeEnum.CORP_PAY, HotelPayTypeEnum.CORP_PAY
        )

        then: "The result should be null"
        result != null
        result.serviceChargeType == null
        result.amount == new BigDecimal(30)

        when: "Calling buildIntlServiceChargeInfoType with empty strategyInfoMap"
        result = mapper.buildIntlServiceChargeInfoType(
                new HashMap<String, StrategyInfo>(), "PAY_TYPE_FROM_INPUT", new ResourceToken(),
                null, HotelPayTypeEnum.CORP_PAY, HotelPayTypeEnum.CORP_PAY
        )

        then: "The result should be null"
        result == null
    }

    def "getBaseInfoType" () {
        expect:
        new MapperOfCheckTravelPolicyRequestType().getBaseInfoType(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a"), sourceFrom: SourceFrom.Offline), null, null, null).uid == "a"
    }

    def "buildRoomDailyPriceList-hotelCheckAvail"() {
        given:
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            public static boolean hotelCheckAvail(List<StrategyInfo> strategyInfos) {
                return true;
            }
        }
        def mapper = new MapperOfCheckTravelPolicyRequestType()
        def checkAvailInfo = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo) {
            getRoomDailyPriceInfoTypeList() >> [new RoomDailyPriceInfoType()]
        }
        def integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: new UserInfo(pos: PosEnum.AUSTRIA))
        when:
        def result = mapper.buildRoomDailyPriceList(checkAvailInfo, null, integrationSoaRequestType, "PAY_TYPE_FROM_INPUT")

        then:
        result != null
        result.size() == 1

        when:
        integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: new UserInfo(pos: PosEnum.CHINA))
        result = mapper.buildRoomDailyPriceList(checkAvailInfo, null, integrationSoaRequestType, "PAY_TYPE_FROM_INPUT")
        then:
        result != null
        result.size() == 0
    }

    def "buildRoomDailyPriceList-ordercreate"() {
        given:
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            public static boolean hotelCheckAvail(List<StrategyInfo> strategyInfos) {
                return false;
            }
        }
        def mapper = new MapperOfCheckTravelPolicyRequestType()
        def checkAvailInfo = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo) {
            getRoomDailyPriceInfoTypeList() >> [new RoomDailyPriceInfoType()]
        }
        def integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: new UserInfo(pos: PosEnum.CHINA))
        when:
        def result = mapper.buildRoomDailyPriceList(checkAvailInfo, null, integrationSoaRequestType, "SCENE_PAY_TYPE_FROM_DEFAULT")

        then:
        result != null
        result.size() == 1

        when:
        integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: new UserInfo(pos: PosEnum.AUSTRIA))
        result = mapper.buildRoomDailyPriceList(checkAvailInfo, null, integrationSoaRequestType, "SCENE_PAY_TYPE_FROM_DEFAULT")
        then:
        result != null
        result.size() == 1
    }

    def "getServiceChargeInfoList"() {
        given:
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            public static boolean intlServiceChargeInfo(Map<String, StrategyInfo> strategyInfoMap) {
                return true
            }
            @Mock
            public static boolean hotelCheckAvail(Map<String, StrategyInfo> strategyInfoMap) {
                return true
            }
        }
        def mapper = new MapperOfCheckTravelPolicyRequestType()
        CalculateServiceChargeV2ResponseType calculateServiceChargeV2ResponseType = new CalculateServiceChargeV2ResponseType()
        ResourceToken resourceToken = new ResourceToken()
        List<HotelPayTypeInput> hotelPayTypeInputs = []
        String scene = "SCENE_PAY_TYPE_FROM_DEFAULT"
        Map<String, StrategyInfo> strategyInfoMap = new HashMap<>()
        when:
        def result = mapper.getServiceChargeInfoList(
                calculateServiceChargeV2ResponseType, resourceToken, hotelPayTypeInputs, scene, strategyInfoMap)
        then:
        result == null
    }

    @Unroll
    def "testBuildCostCenterInfoList with #description"() {
        given: "A MapperOfCheckTravelPolicyRequestType instance and mocked inputs"
        def mapper = new MapperOfCheckTravelPolicyRequestType()
        def costCenter = new CostCenterInfoType(passengerCostCenterList: [
                new PassengerCostCenterInfoType(passengerUid: "passengerUid1", costCenterList: [
                        new CostCenterItemInfoType(level: 1, costCenterValue: "costCenterValue1"),
                        new CostCenterItemInfoType(level: 2, costCenterValue: "costCenterValue2"),
                        new CostCenterItemInfoType(level: 3, costCenterValue: "costCenterValue3"),
                        new CostCenterItemInfoType(level: 4, costCenterValue: "costCenterValue4"),
                        new CostCenterItemInfoType(level: 5, costCenterValue: "costCenterValue5"),
                        new CostCenterItemInfoType(level: 6, costCenterValue: "costCenterValue6")
                ]),
                new PassengerCostCenterInfoType(infoId: "123", costCenterList: [
                        null,
                        new CostCenterItemInfoType(level: 2, costCenterValue: ""),
                        new CostCenterItemInfoType(level: 3, costCenterValue: null),
                        new CostCenterItemInfoType(level: 4, costCenterValue: "CostCenterD"),
                        new CostCenterItemInfoType(level: 5, costCenterValue: "CostCenterE"),
                        new CostCenterItemInfoType(level: 6, costCenterValue: "CostCenterF"),
                        new CostCenterItemInfoType(level: 7, costCenterValue: "CostCenterG")
                ]),
                new PassengerCostCenterInfoType(passengerUid: "passengerUid3"),
                new PassengerCostCenterInfoType(passengerUid: "passengerUid4", costCenterList: [
                        null
                ]),
                null
        ])
        HotelBookPassengerInput hotelBookPassengerInput = new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "passengerUid1"))
        when:
        def result = mapper.buildCostCenterInfoList(costCenter, hotelBookPassengerInput)
        then:
        result != null
        result.size() == 6
        result.get(0).costCenterType == "CostCenter1"
        result.get(1).costCenterType == "CostCenter2"
        result.get(2).costCenterType == "CostCenter3"
        result.get(3).costCenterType == "CostCenter4"
        result.get(4).costCenterType == "CostCenter5"
        result.get(5).costCenterType == "CostCenter6"
        result.get(0).costCenterValue == "costCenterValue1"
        result.get(1).costCenterValue == "costCenterValue2"
        result.get(2).costCenterValue == "costCenterValue3"
        result.get(3).costCenterValue == "costCenterValue4"
        result.get(4).costCenterValue == "costCenterValue5"
        result.get(5).costCenterValue == "costCenterValue6"

        when:
        hotelBookPassengerInput = new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(infoId: "123"))
        result = mapper.buildCostCenterInfoList(costCenter, hotelBookPassengerInput)
        then:
        result != null
        result.size() == 3
        result.get(0).costCenterType == "CostCenter4"
        result.get(1).costCenterType == "CostCenter5"
        result.get(2).costCenterType == "CostCenter6"
        result.get(0).costCenterValue == "CostCenterD"
        result.get(1).costCenterValue == "CostCenterE"
        result.get(2).costCenterValue == "CostCenterF"

        when:
        hotelBookPassengerInput = new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "passengerUid3"))
        result = mapper.buildCostCenterInfoList(costCenter, hotelBookPassengerInput)
        then:
        result == null

        when:
        hotelBookPassengerInput = new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "passengerUid4"))
        result = mapper.buildCostCenterInfoList(costCenter, hotelBookPassengerInput)
        then:
        result == null
    }

    def "genCostCenterInfoType"() {
        given:
        new MockUp<SaveCommonDataCostCenterInfoTypeUtil>() {
            @Mock
            public static CostCenterInfoType genCostCenterInfoType(List<CostCenterInput> costCenterInfoListInput,
                                                                   String approvalNumber, List<CheckCostCenterPassenger> passengerList) {
                return new CostCenterInfoType(
                        passengerCostCenterList: [
                                new PassengerCostCenterInfoType(passengerUid: "passengerUid1", costCenterList: [
                                        new CostCenterItemInfoType(level: 1, costCenterValue: "CostCenter1")
                                ])
                        ]
                )
            }
        }
        def mapper = Spy(new MapperOfCheckTravelPolicyRequestType())
        CostCenterInfo costCenterInfo = new CostCenterInfo(costCenterInputs: [new CostCenterInput()])
        List<HotelBookPassengerInput> hotelBookPassengerInputs = new ArrayList<>()
        CityInput cityInput = new CityInput()
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig = new QconfigOfCertificateInitConfig()
        Map<String, StrategyInfo> strategyInfoMap = new HashMap<>()
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo)
        HotelBookPassengerInput hotelBookPassengerInput = new HotelBookPassengerInput()
        mapper.buildCostCenterInfoList(_ as CostCenterInfoType, _ as HotelBookPassengerInput) >> [new CostCenterInfoType(costCenterType: "CostCenter1")]
        when:
        def result = mapper.buildCostCenterInfoListNew(costCenterInfo, hotelBookPassengerInputs, cityInput, qconfigOfCertificateInitConfig, strategyInfoMap, checkAvailInfo, hotelBookPassengerInput, null)
        then:
        result != null
        result.get(0).costCenterType == "CostCenter1"
    }
}
