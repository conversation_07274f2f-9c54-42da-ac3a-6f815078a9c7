package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderDetailRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification

class MapperOfOrderDetailRequestTypeTest extends Specification{
    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }
    def mapper = new MapperOfOrderDetailRequestType()

    def "convert should map all fields correctly"() {
        given:
        def orderId = 123L
        def integrationSoaRequestType = new IntegrationSoaRequestType()
        integrationSoaRequestType.setLanguage("zh")
        integrationSoaRequestType.setSourceFrom(sourceFrom)
        def tuple = Tuple2.of(orderId, integrationSoaRequestType)

        when:
        OrderDetailRequestType result = mapper.invokeMethod("convert", tuple)

        then:
        result.getOrderIdList() == [orderId]
        result.getLanguage() == "zh"
        result.getOperationChannel() == expectedChannel
        result.getCacheable()
        !result.getDeleteFlag()

        where:
        sourceFrom            | expectedChannel
        SourceFrom.Offline    | "Offline"
        SourceFrom.Online     | "Online"
        SourceFrom.H5         | "App"
        SourceFrom.CRN        | "App"
        SourceFrom.Native     | "App"
        null                  | "App"
    }

    def "check should return null"() {
        expect:
        mapper.invokeMethod("check", Tuple2.of(1L, new IntegrationSoaRequestType())) == null
    }
}
