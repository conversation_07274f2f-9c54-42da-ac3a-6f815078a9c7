package com.ctrip.corp.bff.hotel.book.common.util

import com.ctrip.corp.agg.hotel.expense.contract.model.ApprovalType
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookingRulesType
import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckAvailResponseType
import com.ctrip.corp.agg.hotel.roomavailable.entity.GroupMemberShipType
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelBrandItem
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelItem
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelRatePlan
import com.ctrip.corp.agg.hotel.roomavailable.entity.PackageRoomInfoType
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomAttributesType
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomItem
import com.ctrip.corp.agg.hotel.roomavailable.entity.XProductEntityType
import com.ctrip.corp.agg.hotel.roomavailable.entity.XProductInfoType
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelGeoInfoResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.enums.BooleanValueEnum
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.framework.template.handler.WaitFuture
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import com.ctrip.corp.bff.hotel.book.contract.TripInfoInput
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType
import com.ctrip.corp.order.data.aggregation.query.contract.TravelInfoType
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType
import corp.user.service.corp4jservice.GetCorpInfoResponseType
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType
import corp.user.service.group4j.accounts.BizModeBindRelationData
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType
import mockit.Mock
import mockit.MockUp
import org.junit.Assert
import spock.lang.Specification
import com.ctrip.corp.order.data.aggregation.query.contract.OrderBasicInfoType;

/**
 * <AUTHOR>
 * @Date 2024/12/5 17:08
 */
class BookingInitProcessorOfUtilTest extends Specification {
    void testRequireRegisterGroupMemberMember_GroupMemberShipTypeAndRoomItemNull() {
        when:
        CheckAvailResponseType checkAvailResponseType = new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: new RoomItem(balanceType: "PP"),hotelInfo: new HotelItem(hotelBrandInfo: new HotelBrandItem(groupId: 12))));

        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();
        then:
        Assert.assertFalse(BookingInitProcessorOfUtil.needQueryMrgMemberUserInfo(checkAvailInfo, [:]));
        Assert.assertFalse(BookingInitProcessorOfUtil.needSearchRegistrationFields(checkAvailInfo, [:]));
    }

    void testRequireRegisterGroupMemberMember_RoomAttributesNotSupportGroupMemberShip() {
        when:
        RoomItem roomItem = new RoomItem();
        roomItem.setBalanceType("PP")
        CheckAvailResponseType checkAvailResponseType = new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: roomItem));

        RoomAttributesType roomAttributes = new RoomAttributesType();
        roomAttributes.setGroupMemberShip(false);
        roomItem.setRoomAttributes(roomAttributes);
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();
        then:
        Assert.assertFalse(BookingInitProcessorOfUtil.needQueryMrgMemberUserInfo(checkAvailInfo, [:]));
    }

    void testRequireRegisterGroupMemberMember_GroupMemberShipTypeNotNeedRegister() {

        when:

        BookingRulesType bookingRules = new BookingRulesType();
        GroupMemberShipType groupMemberShipType = new GroupMemberShipType();
        groupMemberShipType.setNeedRegister(false);
        bookingRules.setGroupMemberShipInfo(groupMemberShipType);

        RoomItem roomItem = new RoomItem();
        roomItem.setBalanceType("PP")
        CheckAvailResponseType checkAvailResponseType = new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: roomItem, bookingRules: bookingRules));

        RoomAttributesType roomAttributes = new RoomAttributesType();
        roomAttributes.setGroupMemberShip(true);
        roomItem.setRoomAttributes(roomAttributes);
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();
        then:
        Assert.assertFalse(BookingInitProcessorOfUtil.needQueryMrgMemberUserInfo(checkAvailInfo, [:]));

        when :
        groupMemberShipType = new GroupMemberShipType();
        groupMemberShipType.setNeedRegister(true);
        groupMemberShipType.setGroupRegisterRule(BookingInitProcessorOfUtil.NO_REGISTER);
        bookingRules.setGroupMemberShipInfo(groupMemberShipType);
        checkAvailResponseType = new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: roomItem, bookingRules: bookingRules));
        checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();
        then:
        Assert.assertFalse(BookingInitProcessorOfUtil.needQueryMrgMemberUserInfo(checkAvailInfo, [:]));

        when :
        groupMemberShipType = new GroupMemberShipType();
        groupMemberShipType.setNeedRegister(true);
        groupMemberShipType.setGroupRegisterRule(null);
        bookingRules.setGroupMemberShipInfo(groupMemberShipType);
        checkAvailResponseType = new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: roomItem, bookingRules: bookingRules, hotelInfo: new HotelItem(hotelBrandInfo: new HotelBrandItem(groupId: 12))));
        checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();
        then:
        Assert.assertTrue(BookingInitProcessorOfUtil.needQueryMrgMemberUserInfo(checkAvailInfo, [:]));
    }

    def "requireDefaultApproval"() {
        given:
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public"))
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("BillType", "A")
                        put("BillControlMode", "A")
                        put("isChkaheadapproveHotelI", "T")
                        put("isChkaheadapproveHotel", "T")
                    }
                }))
                .policyAccountInfo(null)
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build()
        ResourceToken resourceToken = new ResourceToken(hotelResourceToken: new HotelResourceToken(hotelGeoInfoResourceToken: new HotelGeoInfoResourceToken(cityId: 1)))
        when:
        def result = BookingInitProcessorOfUtil.needBatchApprovalDefault(bookingInitRequestType, accountInfo, resourceToken)
        then:
        result

        when:
        resourceToken = new ResourceToken(hotelResourceToken: new HotelResourceToken(hotelGeoInfoResourceToken: new HotelGeoInfoResourceToken(cityId: 22249)))
        result = BookingInitProcessorOfUtil.needBatchApprovalDefault(bookingInitRequestType, accountInfo, resourceToken)
        then:
        result



        when:
        accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("BillType", "A")
                        put("BillControlMode", "A")
                        put("isChkaheadapproveHotelI", "F")
                        put("isChkaheadapproveHotel", "F")
                    }
                }))
                .policyAccountInfo(null)
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build()
        resourceToken = new ResourceToken(hotelResourceToken: new HotelResourceToken(hotelGeoInfoResourceToken: new HotelGeoInfoResourceToken(cityId: 22249)))
        result = BookingInitProcessorOfUtil.needBatchApprovalDefault(bookingInitRequestType, accountInfo, resourceToken)
        then:
        !result
    }

    def "requireQueryPackageRoom" () {
        expect:
        !BookingInitProcessorOfUtil.needGetPackageRoomList(WrapperOfCheckAvail.checkAvailBuilder().setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).setCheckAvailResponseType(new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: new RoomItem(balanceType: "pp", packageRoomInfo: new PackageRoomInfoType(packageId: 0))))).build().getCheckAvailInfo(), [:])
        BookingInitProcessorOfUtil.needGetPackageRoomList(WrapperOfCheckAvail.checkAvailBuilder().setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).setCheckAvailResponseType(new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: new RoomItem(balanceType: "pp", packageRoomInfo: new PackageRoomInfoType(packageId: 1))))).build().getCheckAvailInfo(), [:])
        !BookingInitProcessorOfUtil.needGetPackageRoomList(WrapperOfCheckAvail.checkAvailBuilder().setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).setCheckAvailResponseType(new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: new RoomItem(balanceType: "pp", packageRoomInfo: new PackageRoomInfoType(packageId: 0, packageRoomToken: ""))))).build().getCheckAvailInfo(), [:])
        BookingInitProcessorOfUtil.needGetPackageRoomList(WrapperOfCheckAvail.checkAvailBuilder().setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).setCheckAvailResponseType(new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: new RoomItem(balanceType: "pp", packageRoomInfo: new PackageRoomInfoType(packageId: 0, packageRoomToken: "NotEmpty"))))).build().getCheckAvailInfo(), [:])
    }

    def "needGetPackageRoomList should return false when hotelCheckAvail strategy is true"() {
        given:
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
            .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken()))
            .setCheckAvailResponseType(new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: new RoomItem(balanceType: "pp", packageRoomInfo: new PackageRoomInfoType(packageId: 1, packageRoomToken: "NotEmpty")))))
            .build().getCheckAvailInfo()
        Map<String, StrategyInfo> strategyInfoMap = ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "T")]

        when:
        boolean result = BookingInitProcessorOfUtil.needGetPackageRoomList(checkAvailInfo, strategyInfoMap)

        then:
        !result
    }

    def "needSearchRegistrationFields should return false when hotelCheckAvail strategy is true"() {
        given:
        CheckAvailResponseType checkAvailResponseType = new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(
            hotelInfo: new HotelItem(hotelBrandInfo: new HotelBrandItem(groupId: 123)),
            roomInfo: new RoomItem(balanceType: "PP", roomAttributes: new RoomAttributesType(canRegister: true))
        ))
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
            .setCheckAvailResponseType(checkAvailResponseType)
            .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken()))
            .build().getCheckAvailInfo()
        Map<String, StrategyInfo> strategyInfoMap = ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "T")]

        when:
        boolean result = BookingInitProcessorOfUtil.needSearchRegistrationFields(checkAvailInfo, strategyInfoMap)

        then:
        !result
    }


    def "needGeneralSearchAccountInfoOfPolicy" () {
        expect:
        !BookingInitProcessorOfUtil.needGeneralSearchAccountInfoOfPolicy(null)
        !BookingInitProcessorOfUtil.needGeneralSearchAccountInfoOfPolicy(new PolicyInput())
        BookingInitProcessorOfUtil.needGeneralSearchAccountInfoOfPolicy(new PolicyInput(policyUid: "a"))
    }

    def "needGetPackageRoomSnapshot should return false when hotelCheckAvail strategy is true"() {
        given:
        ResourceToken resourceToken = new ResourceToken(roomResourceToken: new RoomResourceToken())
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        Map<String, StrategyInfo> strategyInfoMap = ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "T")]

        when:
        boolean result = BookingInitProcessorOfUtil.needGetPackageRoomSnapshot(resourceToken, checkAvailInfo, strategyInfoMap)

        then:
        !result
    }

    def "needGetPackageRoomSnapshot should return false when supportAccommodation is not T"() {
        given:
        ResourceToken resourceToken = new ResourceToken(roomResourceToken: new RoomResourceToken(supportAccommodation: BooleanValueEnum.F))
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        Map<String, StrategyInfo> strategyInfoMap = [:]

        when:
        boolean result = BookingInitProcessorOfUtil.needGetPackageRoomSnapshot(resourceToken, checkAvailInfo, strategyInfoMap)

        then:
        !result
    }

    def "needGetPackageRoomSnapshot should return false when tokens list is empty"() {
        given:
        ResourceToken resourceToken = new ResourceToken(roomResourceToken: new RoomResourceToken(supportAccommodation: BooleanValueEnum.T))
        RoomItem roomItem = new RoomItem(xProductInfo: new XProductInfoType(xProductEntity: []))
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailInfo) {
            getRoomItem() >> roomItem
        }
        Map<String, StrategyInfo> strategyInfoMap = [:]

        when:
        boolean result = BookingInitProcessorOfUtil.needGetPackageRoomSnapshot(resourceToken, checkAvailInfo, strategyInfoMap)

        then:
        !result
    }

    def "needGetPackageRoomSnapshot should return false when no XProductEntity with type 1"() {
        given:
        ResourceToken resourceToken = new ResourceToken(roomResourceToken: new RoomResourceToken(supportAccommodation: BooleanValueEnum.T))
        List<XProductEntityType> xProductEntities = [
            new XProductEntityType(xProductType: 2, xProductToken: "token1"),
            new XProductEntityType(xProductType: 3, xProductToken: "token2")
        ]
        RoomItem roomItem = new RoomItem(xProductInfo: new XProductInfoType(xProductEntity: xProductEntities))
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailInfo) {
            getRoomItem() >> roomItem
        }
        Map<String, StrategyInfo> strategyInfoMap = [:]

        when:
        boolean result = BookingInitProcessorOfUtil.needGetPackageRoomSnapshot(resourceToken, checkAvailInfo, strategyInfoMap)

        then:
        !result
    }

    def "needGetPackageRoomSnapshot should return true when supportAccommodation is T and has XProductEntity with type 1"() {
        given:
        ResourceToken resourceToken = new ResourceToken(roomResourceToken: new RoomResourceToken(supportAccommodation: BooleanValueEnum.T))
        List<XProductEntityType> xProductEntities = [
            new XProductEntityType(xProductType: 1, xProductToken: "token1"),
            new XProductEntityType(xProductType: 2, xProductToken: "token2")
        ]
        RoomItem roomItem = new RoomItem(xProductInfo: new XProductInfoType(xProductEntity: xProductEntities))
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailInfo) {
            getRoomItem() >> roomItem
        }
        Map<String, StrategyInfo> strategyInfoMap = [:]

        when:
        boolean result = BookingInitProcessorOfUtil.needGetPackageRoomSnapshot(resourceToken, checkAvailInfo, strategyInfoMap)

        then:
        result
    }

    def "needGetPackageRoomSnapshot should handle null resourceToken"() {
        given:
        ResourceToken resourceToken = null
        List<XProductEntityType> xProductEntities = [
            new XProductEntityType(xProductType: 1, xProductToken: "token1")
        ]
        RoomItem roomItem = new RoomItem(xProductInfo: new XProductInfoType(xProductEntity: xProductEntities))
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailInfo) {
            getRoomItem() >> roomItem
        }
        Map<String, StrategyInfo> strategyInfoMap = [:]

        when:
        boolean result = BookingInitProcessorOfUtil.needGetPackageRoomSnapshot(resourceToken, checkAvailInfo, strategyInfoMap)

        then:
        !result
    }

    def "needGetPackageRoomSnapshot should handle null checkAvailInfo"() {
        given:
        ResourceToken resourceToken = new ResourceToken(roomResourceToken: new RoomResourceToken(supportAccommodation: BooleanValueEnum.T))
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = null
        Map<String, StrategyInfo> strategyInfoMap = [:]

        when:
        boolean result = BookingInitProcessorOfUtil.needGetPackageRoomSnapshot(resourceToken, checkAvailInfo, strategyInfoMap)

        then:
        !result
    }

    def "needGetPackageRoomSnapshot should handle null roomResourceToken"() {
        given:
        ResourceToken resourceToken = new ResourceToken(roomResourceToken: null)
        List<XProductEntityType> xProductEntities = [
            new XProductEntityType(xProductType: 1, xProductToken: "token1")
        ]
        RoomItem roomItem = new RoomItem(xProductInfo: new XProductInfoType(xProductEntity: xProductEntities))
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailInfo) {
            getRoomItem() >> roomItem
        }
        Map<String, StrategyInfo> strategyInfoMap = [:]

        when:
        boolean result = BookingInitProcessorOfUtil.needGetPackageRoomSnapshot(resourceToken, checkAvailInfo, strategyInfoMap)

        then:
        !result
    }

    def "needGetReasoncodes" () {
        expect:
        !BookingInitProcessorOfUtil.needGetReasoncodes(null)
        !BookingInitProcessorOfUtil.needGetReasoncodes([:])
        !BookingInitProcessorOfUtil.needGetReasoncodes(["HOTEL_CHECK_AVAIL":new StrategyInfo(strategyValue: "F")])
        BookingInitProcessorOfUtil.needGetReasoncodes(["HOTEL_CHECK_AVAIL":new StrategyInfo(strategyValue: "T")])
        !BookingInitProcessorOfUtil.needGetReasoncodes(["NEED_RC_INFO":new StrategyInfo(strategyValue: "F")])
        BookingInitProcessorOfUtil.needGetReasoncodes(["NEED_RC_INFO":new StrategyInfo(strategyValue: "T")])
    }

    def "needSearchApproval" () {
        given:
        def queryHotelOrder = Mock(WaitFuture)
        queryHotelOrder.get() >> new QueryHotelOrderDataResponseType(travelInfo: new TravelInfoType(approval: new com.ctrip.corp.order.data.aggregation.query.contract.ApprovalType(subPreApprovalNo: "a")))
        def account = Mock(WrapperOfAccount.AccountInfo)
        def query =Mock(WaitFuture)
        account.isPreApprovalRequired(_, _) >> true
        expect:
        !BookingInitProcessorOfUtil.needSearchApproval(
                new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "private")),
                queryHotelOrder, new ResourceToken(), account)
        !BookingInitProcessorOfUtil.needSearchApproval(
                new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public")),
                query, new ResourceToken(), account)
        BookingInitProcessorOfUtil.needSearchApproval(
                new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public"), approvalInput: new ApprovalInput(subApprovalNo: "a")),
                queryHotelOrder, new ResourceToken(), account)
        BookingInitProcessorOfUtil.needSearchApproval(
                new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public")),
                queryHotelOrder, new ResourceToken(), account)
    }

    def "needGetCorpUserHotelVipCard" () {
        expect:
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(null, null, null, null, ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "T")])
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(
                new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")),
                new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: "a", primaryDimensionId: "b")]),
                null, null, ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "T")])
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(null, null, [new StrategyInfo(strategyKey: "FIRST_PASSENGER_MEMBERSHIP_CARD", strategyValue: "T")], null, [:])
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(null, null,
                [new StrategyInfo(strategyKey: "FIRST_PASSENGER_MEMBERSHIP_CARD", strategyValue: "T")], [], [:])
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(null, null,
                [new StrategyInfo(strategyKey: "FIRST_PASSENGER_MEMBERSHIP_CARD", strategyValue: "T")], [null], [:])
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(null, null,
                [new StrategyInfo(strategyKey: "FIRST_PASSENGER_MEMBERSHIP_CARD", strategyValue: "T")], [new HotelBookPassengerInput()], [:])
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(null, null,
                [new StrategyInfo(strategyKey: "FIRST_PASSENGER_MEMBERSHIP_CARD", strategyValue: "T")], [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput())], [:])
        BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(null, null,
                [new StrategyInfo(strategyKey: "FIRST_PASSENGER_MEMBERSHIP_CARD", strategyValue: "T")], [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "A"))], [:])
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(null, null,
                null, null, [:])
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(new IntegrationSoaRequestType(), new QueryBizModeBindRelationResponseType(),
                null, null, [:])
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(new IntegrationSoaRequestType(), new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: []),
                null, null, [:])
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(new IntegrationSoaRequestType(), new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData()]),
                null, null, [:])
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")),
                new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData()]),
                null, null, [:])
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")),
                new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: "b", primaryDimensionId: "b")]),
                null, null, [:])
        BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")),
                new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: "a", primaryDimensionId: "b")]),
                null, null, [:])
    }

    def "needSearchTripDetail" () {
        expect:
        !BookingInitProcessorOfUtil.needSearchTripDetail(null, [:])
        !BookingInitProcessorOfUtil.needSearchTripDetail(new BookingInitRequestType(), [:])
        !BookingInitProcessorOfUtil.needSearchTripDetail(new BookingInitRequestType(tripInfoInput: new TripInfoInput()), [:])
        !BookingInitProcessorOfUtil.needSearchTripDetail(new BookingInitRequestType(tripInfoInput: new TripInfoInput(tripId: 0)), [:])
        BookingInitProcessorOfUtil.needSearchTripDetail(new BookingInitRequestType(tripInfoInput: new TripInfoInput(tripId: 1)), [:])
    }

    def "needReimbursementQuery" () {
        expect:
        !BookingInitProcessorOfUtil.needReimbursementQuery(null, [:])
        BookingInitProcessorOfUtil.needReimbursementQuery(1l, [:])
    }

    def "needGetPlatformRelationByUid" () {
        expect:
        !BookingInitProcessorOfUtil.needGetPlatformRelationByUid(null,null, [:])
        !BookingInitProcessorOfUtil.needGetPlatformRelationByUid(new IntegrationSoaRequestType(),null, [:])
        !BookingInitProcessorOfUtil.needGetPlatformRelationByUid(new IntegrationSoaRequestType(userInfo: new UserInfo()),null, [:])
        !BookingInitProcessorOfUtil.needGetPlatformRelationByUid(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")),null, [:])
        !BookingInitProcessorOfUtil.needGetPlatformRelationByUid(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")),
                new QueryBizModeBindRelationResponseType(), [:])
        !BookingInitProcessorOfUtil.needGetPlatformRelationByUid(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")),
                new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: []), [:])
        !BookingInitProcessorOfUtil.needGetPlatformRelationByUid(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")),
                new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [null]), [:])
        !BookingInitProcessorOfUtil.needGetPlatformRelationByUid(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")),
                new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData()]), [:])
        !BookingInitProcessorOfUtil.needGetPlatformRelationByUid(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")),
                new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: "b", primaryDimensionId: "b")]), [:])
        BookingInitProcessorOfUtil.needGetPlatformRelationByUid(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")),
                new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: "a", primaryDimensionId: "b")]), [:])
    }

    def "testNeedQueryAuth"() {
        expect:
        BookingInitProcessorOfUtil.needQueryAuth(corpInfoResponseType) == result
        where:
        corpInfoResponseType                              | result
        null                                              | false
        new GetCorpInfoResponseType(corporationFlag: "A") | false
        new GetCorpInfoResponseType(corporationFlag: "B") | true
    }

    def "needGetCorpUserInfoDetailList" () {
        given:
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                return true
            }
            }
        expect:
        !BookingInitProcessorOfUtil.needGetCorpUserInfoDetailList(null, null, null)
        !BookingInitProcessorOfUtil.needGetCorpUserInfoDetailList(null, null, ["NEED_REALTIME_PASSENGER_INFO":new StrategyInfo(strategyValue: "F")])
        !BookingInitProcessorOfUtil.needGetCorpUserInfoDetailList(null, null, ["NEED_REALTIME_PASSENGER_INFO":new StrategyInfo(strategyValue: "T")])
        !BookingInitProcessorOfUtil.needGetCorpUserInfoDetailList(null, [], ["NEED_REALTIME_PASSENGER_INFO":new StrategyInfo(strategyValue: "T")])
        !BookingInitProcessorOfUtil.needGetCorpUserInfoDetailList(null, [null], ["NEED_REALTIME_PASSENGER_INFO":new StrategyInfo(strategyValue: "T")])
        BookingInitProcessorOfUtil.needGetCorpUserInfoDetailList(null, [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "a"))], ["NEED_REALTIME_PASSENGER_INFO":new StrategyInfo(strategyValue: "T")])
    }

    def "needGetCommonPassenger" () {
        given:
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                return true
            }
        }
        expect:
        !BookingInitProcessorOfUtil.needGetCommonPassenger(null, null, null)
        !BookingInitProcessorOfUtil.needGetCommonPassenger(null, null, ["NEED_REALTIME_PASSENGER_INFO":new StrategyInfo(strategyValue: "F")])
        !BookingInitProcessorOfUtil.needGetCommonPassenger(null, null, ["NEED_REALTIME_PASSENGER_INFO":new StrategyInfo(strategyValue: "T")])
        !BookingInitProcessorOfUtil.needGetCommonPassenger(null, [], ["NEED_REALTIME_PASSENGER_INFO":new StrategyInfo(strategyValue: "T")])
        !BookingInitProcessorOfUtil.needGetCommonPassenger(null, [null], ["NEED_REALTIME_PASSENGER_INFO":new StrategyInfo(strategyValue: "T")])
        BookingInitProcessorOfUtil.needGetCommonPassenger(null, [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(infoId: "a"))], ["NEED_REALTIME_PASSENGER_INFO":new StrategyInfo(strategyValue: "T")])

    }

    def "needSearchTripBasicInfoOfOriginalOrderId"() {
        expect:
        // 测试参数为null的情况
        !BookingInitProcessorOfUtil.needSearchTripBasicInfoOfOriginalOrderId(null, null)
        !BookingInitProcessorOfUtil.needSearchTripBasicInfoOfOriginalOrderId(null, [:])

        // 测试hotelCheckAvail为true的情况，应该返回false
        !BookingInitProcessorOfUtil.needSearchTripBasicInfoOfOriginalOrderId(
            new QueryHotelOrderDataResponseType(),
            ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "T")]
        )

        // 测试hotelCheckAvail为false，但queryHotelOrderDataResponseType为null的情况
        !BookingInitProcessorOfUtil.needSearchTripBasicInfoOfOriginalOrderId(
            null,
            ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "F")]
        )

        // 测试hotelCheckAvail为false，queryHotelOrderDataResponseType不为null，但orderBasicInfo为null的情况
        !BookingInitProcessorOfUtil.needSearchTripBasicInfoOfOriginalOrderId(
            new QueryHotelOrderDataResponseType(),
            ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "F")]
        )

        // 测试hotelCheckAvail为false，orderBasicInfo不为null，但tripOrderId为null的情况
        !BookingInitProcessorOfUtil.needSearchTripBasicInfoOfOriginalOrderId(
            new QueryHotelOrderDataResponseType(orderBasicInfo: new OrderBasicInfoType()),
            ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "F")]
        )

        // 测试hotelCheckAvail为false，tripOrderId为0的情况
        !BookingInitProcessorOfUtil.needSearchTripBasicInfoOfOriginalOrderId(
            new QueryHotelOrderDataResponseType(orderBasicInfo: new OrderBasicInfoType(tripOrderId: 0L)),
            ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "F")]
        )

        // 测试hotelCheckAvail为false，tripOrderId为负数的情况，应该返回true（因为isNotZeroAndNull(-1)为true）
        BookingInitProcessorOfUtil.needSearchTripBasicInfoOfOriginalOrderId(
            new QueryHotelOrderDataResponseType(orderBasicInfo: new OrderBasicInfoType(tripOrderId: -1L)),
            ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "F")]
        )

        // 测试hotelCheckAvail为false，tripOrderId为正数的情况，应该返回true
        BookingInitProcessorOfUtil.needSearchTripBasicInfoOfOriginalOrderId(
            new QueryHotelOrderDataResponseType(orderBasicInfo: new OrderBasicInfoType(tripOrderId: 123L)),
            ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "F")]
        )

        // 测试strategyInfoMap为空的情况（hotelCheckAvail默认为false）
        BookingInitProcessorOfUtil.needSearchTripBasicInfoOfOriginalOrderId(
            new QueryHotelOrderDataResponseType(orderBasicInfo: new OrderBasicInfoType(tripOrderId: 456L)),
            [:]
        )

        // 测试strategyInfoMap为null的情况（hotelCheckAvail默认为false）
        BookingInitProcessorOfUtil.needSearchTripBasicInfoOfOriginalOrderId(
            new QueryHotelOrderDataResponseType(orderBasicInfo: new OrderBasicInfoType(tripOrderId: 789L)),
            null
        )

        // 测试HOTEL_CHECK_AVAIL策略不存在的情况（hotelCheckAvail默认为false）
        BookingInitProcessorOfUtil.needSearchTripBasicInfoOfOriginalOrderId(
            new QueryHotelOrderDataResponseType(orderBasicInfo: new OrderBasicInfoType(tripOrderId: 999L)),
            ["OTHER_STRATEGY": new StrategyInfo(strategyValue: "T")]
        )

        // 测试HOTEL_CHECK_AVAIL策略值为非T的情况（hotelCheckAvail为false）
        BookingInitProcessorOfUtil.needSearchTripBasicInfoOfOriginalOrderId(
            new QueryHotelOrderDataResponseType(orderBasicInfo: new OrderBasicInfoType(tripOrderId: 111L)),
            ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "F")]
        )

        // 测试HOTEL_CHECK_AVAIL策略值为小写t的情况（hotelCheckAvail为true，因为equalsIgnoreCase）
        !BookingInitProcessorOfUtil.needSearchTripBasicInfoOfOriginalOrderId(
            new QueryHotelOrderDataResponseType(orderBasicInfo: new OrderBasicInfoType(tripOrderId: 222L)),
            ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "t")]
        )
    }

    def "needXProductEnquire should return false when hotelCheckAvail strategy is true"() {
        given: "Strategy map with HOTEL_CHECK_AVAIL set to true"
        Map<String, StrategyInfo> strategyInfoMap = ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "T")]
        Long orderId = 123L

        when: "Calling needXProductEnquire"
        boolean result = BookingInitProcessorOfUtil.needXProductEnquire(orderId, strategyInfoMap)

        then: "Should return false regardless of orderId value"
        !result
    }

    def "needXProductEnquire should return false when hotelCheckAvail strategy is true with lowercase t"() {
        given: "Strategy map with HOTEL_CHECK_AVAIL set to lowercase t"
        Map<String, StrategyInfo> strategyInfoMap = ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "t")]
        Long orderId = 123L

        when: "Calling needXProductEnquire"
        boolean result = BookingInitProcessorOfUtil.needXProductEnquire(orderId, strategyInfoMap)

        then: "Should return false due to case-insensitive comparison"
        !result
    }

    def "needXProductEnquire should return false when hotelCheckAvail strategy is false but orderId is null"() {
        given: "Strategy map with HOTEL_CHECK_AVAIL set to false and null orderId"
        Map<String, StrategyInfo> strategyInfoMap = ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "F")]
        Long orderId = null

        when: "Calling needXProductEnquire"
        boolean result = BookingInitProcessorOfUtil.needXProductEnquire(orderId, strategyInfoMap)

        then: "Should return false because orderId is null"
        !result
    }

    def "needXProductEnquire should return false when hotelCheckAvail strategy is false but orderId is zero"() {
        given: "Strategy map with HOTEL_CHECK_AVAIL set to false and zero orderId"
        Map<String, StrategyInfo> strategyInfoMap = ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "F")]
        Long orderId = 0L

        when: "Calling needXProductEnquire"
        boolean result = BookingInitProcessorOfUtil.needXProductEnquire(orderId, strategyInfoMap)

        then: "Should return false because orderId is zero"
        !result
    }

    def "needXProductEnquire should return true when hotelCheckAvail strategy is false and orderId is positive"() {
        given: "Strategy map with HOTEL_CHECK_AVAIL set to false and positive orderId"
        Map<String, StrategyInfo> strategyInfoMap = ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "F")]
        Long orderId = 123L

        when: "Calling needXProductEnquire"
        boolean result = BookingInitProcessorOfUtil.needXProductEnquire(orderId, strategyInfoMap)

        then: "Should return true because hotelCheckAvail is false and orderId is positive"
        result
    }

    def "needXProductEnquire should return true when strategy map is empty and orderId is positive"() {
        given: "Empty strategy map and positive orderId"
        Map<String, StrategyInfo> strategyInfoMap = [:]
        Long orderId = 456L

        when: "Calling needXProductEnquire"
        boolean result = BookingInitProcessorOfUtil.needXProductEnquire(orderId, strategyInfoMap)

        then: "Should return true because hotelCheckAvail defaults to false and orderId is positive"
        result
    }

    def "needXProductEnquire should return false when strategy map is empty and orderId is null"() {
        given: "Empty strategy map and null orderId"
        Map<String, StrategyInfo> strategyInfoMap = [:]
        Long orderId = null

        when: "Calling needXProductEnquire"
        boolean result = BookingInitProcessorOfUtil.needXProductEnquire(orderId, strategyInfoMap)

        then: "Should return false because orderId is null"
        !result
    }

    def "needXProductEnquire should return false when strategy map is null and orderId is positive"() {
        given: "Null strategy map and positive orderId"
        Map<String, StrategyInfo> strategyInfoMap = null
        Long orderId = 789L

        when: "Calling needXProductEnquire"
        boolean result = BookingInitProcessorOfUtil.needXProductEnquire(orderId, strategyInfoMap)

        then: "Should return true because hotelCheckAvail defaults to false with null map and orderId is positive"
        result
    }

    def "needXProductEnquire should return false when strategy map is null and orderId is null"() {
        given: "Null strategy map and null orderId"
        Map<String, StrategyInfo> strategyInfoMap = null
        Long orderId = null

        when: "Calling needXProductEnquire"
        boolean result = BookingInitProcessorOfUtil.needXProductEnquire(orderId, strategyInfoMap)

        then: "Should return false because orderId is null"
        !result
    }

    def "needXProductEnquire should return true when HOTEL_CHECK_AVAIL key exists but value is not T"() {
        given: "Strategy map with HOTEL_CHECK_AVAIL set to non-T value and positive orderId"
        Map<String, StrategyInfo> strategyInfoMap = ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "OTHER")]
        Long orderId = 999L

        when: "Calling needXProductEnquire"
        boolean result = BookingInitProcessorOfUtil.needXProductEnquire(orderId, strategyInfoMap)

        then: "Should return true because hotelCheckAvail is false and orderId is positive"
        result
    }

    def "needGetTripBookingInfos should return false when hotelCheckAvail strategy is true"() {
        given: "Strategy map with HOTEL_CHECK_AVAIL set to true"
        Map<String, StrategyInfo> strategyInfoMap = ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "T")]
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
            tripInfoInput: new TripInfoInput(tripId: "123")
        )

        when: "Calling needGetTripBookingInfos"
        boolean result = BookingInitProcessorOfUtil.needGetTripBookingInfos(bookingInitRequestType, strategyInfoMap)

        then: "Should return false regardless of tripId value"
        !result
    }

    def "needGetTripBookingInfos should return false when hotelCheckAvail strategy is true with lowercase t"() {
        given: "Strategy map with HOTEL_CHECK_AVAIL set to lowercase t"
        Map<String, StrategyInfo> strategyInfoMap = ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "t")]
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
            tripInfoInput: new TripInfoInput(tripId: "456")
        )

        when: "Calling needGetTripBookingInfos"
        boolean result = BookingInitProcessorOfUtil.needGetTripBookingInfos(bookingInitRequestType, strategyInfoMap)

        then: "Should return false due to case-insensitive comparison"
        !result
    }

    def "needGetTripBookingInfos should return false when hotelCheckAvail is false but tripId is null"() {
        given: "Strategy map with HOTEL_CHECK_AVAIL set to false and null tripId"
        Map<String, StrategyInfo> strategyInfoMap = ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "F")]
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
            tripInfoInput: new TripInfoInput(tripId: null)
        )

        when: "Calling needGetTripBookingInfos"
        boolean result = BookingInitProcessorOfUtil.needGetTripBookingInfos(bookingInitRequestType, strategyInfoMap)

        then: "Should return false because tripId is null"
        !result
    }

    def "needGetTripBookingInfos should return false when hotelCheckAvail is false but tripId is zero"() {
        given: "Strategy map with HOTEL_CHECK_AVAIL set to false and zero tripId"
        Map<String, StrategyInfo> strategyInfoMap = ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "F")]
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
            tripInfoInput: new TripInfoInput(tripId: "0")
        )

        when: "Calling needGetTripBookingInfos"
        boolean result = BookingInitProcessorOfUtil.needGetTripBookingInfos(bookingInitRequestType, strategyInfoMap)

        then: "Should return false because tripId is zero"
        !result
    }

    def "needGetTripBookingInfos should return false when hotelCheckAvail is false but tripId is negative"() {
        given: "Strategy map with HOTEL_CHECK_AVAIL set to false and negative tripId"
        Map<String, StrategyInfo> strategyInfoMap = ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "F")]
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
            tripInfoInput: new TripInfoInput(tripId: "-1")
        )

        when: "Calling needGetTripBookingInfos"
        boolean result = BookingInitProcessorOfUtil.needGetTripBookingInfos(bookingInitRequestType, strategyInfoMap)

        then: "Should return false because tripId is negative"
        !result
    }

    def "needGetTripBookingInfos should return true when hotelCheckAvail is false and tripId is positive"() {
        given: "Strategy map with HOTEL_CHECK_AVAIL set to false and positive tripId"
        Map<String, StrategyInfo> strategyInfoMap = ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "F")]
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
            tripInfoInput: new TripInfoInput(tripId: "123")
        )

        when: "Calling needGetTripBookingInfos"
        boolean result = BookingInitProcessorOfUtil.needGetTripBookingInfos(bookingInitRequestType, strategyInfoMap)

        then: "Should return true because hotelCheckAvail is false and tripId is positive"
        result
    }

    def "needGetTripBookingInfos should return false when bookingInitRequestType is null"() {
        given: "Strategy map with HOTEL_CHECK_AVAIL set to false and null bookingInitRequestType"
        Map<String, StrategyInfo> strategyInfoMap = ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "F")]
        BookingInitRequestType bookingInitRequestType = null

        when: "Calling needGetTripBookingInfos"
        boolean result = BookingInitProcessorOfUtil.needGetTripBookingInfos(bookingInitRequestType, strategyInfoMap)

        then: "Should return false because bookingInitRequestType is null"
        !result
    }

    def "needGetTripBookingInfos should return false when tripInfoInput is null"() {
        given: "Strategy map with HOTEL_CHECK_AVAIL set to false and null tripInfoInput"
        Map<String, StrategyInfo> strategyInfoMap = ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "F")]
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(tripInfoInput: null)

        when: "Calling needGetTripBookingInfos"
        boolean result = BookingInitProcessorOfUtil.needGetTripBookingInfos(bookingInitRequestType, strategyInfoMap)

        then: "Should return false because tripInfoInput is null"
        !result
    }

    def "needGetTripBookingInfos should return true when strategy map is empty and tripId is positive"() {
        given: "Empty strategy map and positive tripId"
        Map<String, StrategyInfo> strategyInfoMap = [:]
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
            tripInfoInput: new TripInfoInput(tripId: "789")
        )

        when: "Calling needGetTripBookingInfos"
        boolean result = BookingInitProcessorOfUtil.needGetTripBookingInfos(bookingInitRequestType, strategyInfoMap)

        then: "Should return true because hotelCheckAvail defaults to false and tripId is positive"
        result
    }

    def "needGetTripBookingInfos should return true when strategy map is null and tripId is positive"() {
        given: "Null strategy map and positive tripId"
        Map<String, StrategyInfo> strategyInfoMap = null
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
            tripInfoInput: new TripInfoInput(tripId: "999")
        )

        when: "Calling needGetTripBookingInfos"
        boolean result = BookingInitProcessorOfUtil.needGetTripBookingInfos(bookingInitRequestType, strategyInfoMap)

        then: "Should return true because hotelCheckAvail defaults to false with null map and tripId is positive"
        result
    }

    def "needGetTripBookingInfos should return false when tripId is invalid string"() {
        given: "Strategy map with HOTEL_CHECK_AVAIL set to false and invalid tripId string"
        Map<String, StrategyInfo> strategyInfoMap = ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "F")]
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
            tripInfoInput: new TripInfoInput(tripId: "invalid")
        )

        when: "Calling needGetTripBookingInfos"
        boolean result = BookingInitProcessorOfUtil.needGetTripBookingInfos(bookingInitRequestType, strategyInfoMap)

        then: "Should return false because tripId cannot be parsed to a valid Long"
        !result
    }

    def "needGetTripBookingInfos should return false when tripId is empty string"() {
        given: "Strategy map with HOTEL_CHECK_AVAIL set to false and empty tripId string"
        Map<String, StrategyInfo> strategyInfoMap = ["HOTEL_CHECK_AVAIL": new StrategyInfo(strategyValue: "F")]
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
            tripInfoInput: new TripInfoInput(tripId: "")
        )

        when: "Calling needGetTripBookingInfos"
        boolean result = BookingInitProcessorOfUtil.needGetTripBookingInfos(bookingInitRequestType, strategyInfoMap)

        then: "Should return false because tripId is empty string"
        !result
    }

}
