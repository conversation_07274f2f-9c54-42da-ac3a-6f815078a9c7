package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.agg.hotel.expense.contract.model.BaseInfoType
import com.ctrip.corp.agg.hotel.expense.contract.model.ConditionInfoType
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelItem
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType
import com.ctrip.corp.hotel.book.query.entity.HotelBrandEntity
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2024/12/23 18:58
 *
 */
class MapperOfGetRoomChargePolicyListRequestTypeTest extends Specification {

    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }

    @Unroll
    def "testBuildBaseInfoType with different scenarios"() {
        given:
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>(){
                    {
                        put("RepeatOrderControlCorp", "T")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(requestId: "requestId", userInfo: new UserInfo(userId: "uid")),
                policyInput: new PolicyInput(policyUid: "policyUid")
        )

        when:
        BaseInfoType result = MapperOfGetRoomChargePolicyListRequestType.buildBaseInfoType(accountInfo, bookingInitRequestType)

        then:
        result != null
        result.getTraceId() == "requestId"
        result.uid == "uid"
    }

    @Unroll
    def "testBuildConditionInfoTypeSourceFrom with different scenarios"() {
        given:
        IntegrationSoaRequestType integrationSoaRequestType = new IntegrationSoaRequestType(sourceFrom: sourceFrom)

        when:
        ConditionInfoType result = MapperOfGetRoomChargePolicyListRequestType.buildConditionInfoTypeSourceFrom(integrationSoaRequestType)

        then:
        result != null
        result.getFieldName() == "OperationChannel"
        result.getFieldValue() == expectedFieldValue

        where:
        sourceFrom         || expectedFieldValue
        SourceFrom.Offline || "OFFLINE"
        SourceFrom.Online  || "ONLINE"
        SourceFrom.H5      || "APP"
        null               || "APP"
    }

    def "testBuildHotelInfoTypeWithHotelBrandInfo"() {
        given:
        def hotelItem = new HotelItem()
        def checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        checkAvailInfo.getHotelItem() >> hotelItem

        hotelItem.setCountry(1)
        hotelItem.setProvince(2)
        hotelItem.setCity(3)

        def getHotelDetailInfoResponseType = new com.ctrip.corp.hotel.book.query.entity.GetHotelDetailInfoResponseType()
        def hotelDetailInfoEntity = new com.ctrip.corp.hotel.book.query.entity.HotelDetailInfoEntity()
        def detailBaseInfoEntity = new com.ctrip.corp.hotel.book.query.entity.DetailBaseInfoEntity()
        def hotelBrandEntity = new HotelBrandEntity()
        hotelBrandEntity.setGroupId(100)
        hotelBrandEntity.setBrandId(200)
        detailBaseInfoEntity.setHotelBrandInfo(hotelBrandEntity)
        hotelDetailInfoEntity.setHotelBaseInfo(detailBaseInfoEntity)
        getHotelDetailInfoResponseType.setHotelDetailInfo(hotelDetailInfoEntity)

        when:
        def result = MapperOfGetRoomChargePolicyListRequestType.buildHotelInfoType(checkAvailInfo, getHotelDetailInfoResponseType)

        then:
        result != null
        result.countryId == 1
        result.provinceId == 2
        result.cityId == 3
        result.hotelGroupId == 100
        result.hotelBrandId == 200
    }

}
