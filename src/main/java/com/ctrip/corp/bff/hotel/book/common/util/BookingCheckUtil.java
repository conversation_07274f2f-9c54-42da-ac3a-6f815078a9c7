package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityInfoUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput;
import com.ctrip.corp.bff.hotel.book.common.constant.CustomConfigKeyConstant;
import com.ctrip.corp.bff.hotel.book.service.BookingCheckRequestType;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/11/13 21:08
 */
public class BookingCheckUtil {
    public static Boolean buildNeedCheckBookingLimition(WrapperOfAccount.AccountInfo accountInfo,
        BookingCheckRequestType bookingCheckRequestType) {
        if (!QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.RESOURCE_CHECK,
            bookingCheckRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
            return false;
        }
        if (CorpPayInfoUtil.isPrivate(bookingCheckRequestType.getCorpPayInfo())) {
            return true;
        }
        if (StrategyOfBookingInitUtil.passengerPage(bookingCheckRequestType.getStrategyInfos())) {
            return true;
        }
        if (StrategyOfBookingInitUtil.homePage(bookingCheckRequestType.getStrategyInfos())) {
            if (accountInfo.isPolicyModel()) {
                return false;
            }
            return true;
        }
        return false;
    }

    /**
     * 是否需要提前审批校验 提前审批+提前审批前置+qconfig不支持校验的哦开关没开启+非紧急预订
     *
     * @param accountInfo
     * @param bookingCheckRequestType
     * @return
     */
    public static Boolean buildNeedCheckApproval(WrapperOfAccount.AccountInfo accountInfo,
        BookingCheckRequestType bookingCheckRequestType) {
        if (QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.UN_SUPPORT_APPROVAL_CHECK,
            bookingCheckRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
            return false;
        }
        if ("T".equalsIgnoreCase(
            Optional.ofNullable(bookingCheckRequestType.getApprovalInput()).map(ApprovalInput::getEmergency)
                .orElse("F"))) {
            return false;
        }
        if (!accountInfo.isOaApprovalHead()) {
            return false;
        }
        if (!accountInfo.isPreApprovalRequired(CityInfoUtil.oversea(bookingCheckRequestType.getCityInput().getCityId()),
            bookingCheckRequestType.getCorpPayInfo())) {
            return false;
        }
        if (!StrategyOfBookingInitUtil.homePage(bookingCheckRequestType.getStrategyInfos())) {
            return false;
        }
        return true;
    }
}
