package com.ctrip.corp.bff.hotel.book.common.signature.param;

import io.protostuff.Tag;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/6 12:39
 * 注意！！！！！ 不要自己new，必须在OrderCreateProcessorOfUtil.buildBookInfoBO方法中new
 */
public class BookInfoBO {
    // 注意！！！！！ 若有新增或删除字段，需要修改BOOK_PARAMS_VERSION
    public static final String BOOK_VERSION = "3";
    @Tag(1)
    public String roomPayType;
    @Tag(2)
    public String servicePayType;
    @Tag(4)
    public ApprovalInfoBO approvalInfoBO;
    @Tag(5)
    public List<PassengerInfoBO> passengerInfoBOS;

    public ApprovalInfoBO getApprovalInfoBO() {
        return approvalInfoBO;
    }

    public void setApprovalInfoBO(ApprovalInfoBO approvalInfoBO) {
        this.approvalInfoBO = approvalInfoBO;
    }

    public List<PassengerInfoBO> getPassengerInfoBOS() {
        return passengerInfoBOS;
    }

    public void setPassengerInfoBOS(List<PassengerInfoBO> passengerInfoBOS) {
        this.passengerInfoBOS = passengerInfoBOS;
    }

    public String getRoomPayType() {
        return roomPayType;
    }

    public void setRoomPayType(String roomPayType) {
        this.roomPayType = roomPayType;
    }

    public String getServicePayType() {
        return servicePayType;
    }

    public void setServicePayType(String servicePayType) {
        this.servicePayType = servicePayType;
    }
}
