package com.ctrip.corp.bff.hotel.book.common.wrapper;

import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount.AccountInfo;
import com.ctrip.corp.bff.framework.hotel.entity.contract.AddPriceInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.FlashStayInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.AllocationResultToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CostCenterInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.RCInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.bff.specific.contract.ApprovalTextInfoResponseType;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodResponseType;
import com.ctrip.model.CalculateServiceChargeV2ResponseType;

import java.util.List;
import java.util.Map;

/**
 * @Author: rtlv
 * @Date: 2024/9/18 22:40
 */
public class WrapperOfCheckTravelPolicy {
    private ResourceToken resourceToken;
    private WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo;
    private WrapperOfAccount.AccountInfo accountInfo;
    private HotelPolicyInput hotelPolicyInput;
    private IntegrationSoaRequestType integrationSoaRequestType;
    private ApprovalInput approvalInput;
    private AddPriceInput addPriceInput;
    private HotelBookInput hotelBookInput;
    private String scene;
    private HotelInsuranceInput hotelInsuranceInput;
    private List<HotelPayTypeInput> hotelPayTypeInputs;
    private GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponseType;
    private FlashStayInput flashStayInput;
    private CalculateServiceChargeV2ResponseType calculateServiceChargeV2ResponseType;
    private List<RCInput> rcInfos;
    private AllocationResultToken allocationResultToken;
    private List<HotelBookPassengerInput> hotelBookPassengerInputs;
    private CityInput cityInput;
    private CorpPayInfo corpPayInfo;

    // 适用于刚刚查过差标还没往resourceToken里面塞的场景
    private String policyToken;
    // 提交订单成本中心串
    private String costCenterStr;
    private HotelPayTypeEnum roomPayType;

    private List<StrategyInfo> strategyInfos;

    private HotelPayTypeEnum servicePayType;
    private Map<String, StrategyInfo> strategyInfoMap;

    private QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig;
    private CostCenterInfo costCenterInfo;

    private ApprovalTextInfoResponseType approvalTextInfoResponseType;
    public CostCenterInfo getCostCenterInfo() {
        return costCenterInfo;
    }

    public QconfigOfCertificateInitConfig getQconfigOfCertificateInitConfig() {
        return qconfigOfCertificateInitConfig;
    }

    public HotelPayTypeEnum getServicePayType() {
        return servicePayType;
    }

    public Map<String, StrategyInfo> getStrategyInfoMap() {
        return strategyInfoMap;
    }
    public String getCostCenterStr() {
        return costCenterStr;
    }

    public String getPolicyToken() {
        return policyToken;
    }


    public ResourceToken getResourceToken() {
        return resourceToken;
    }

    public WrapperOfCheckAvail.BaseCheckAvailInfo getCheckAvailInfo() {
        return checkAvailInfo;
    }

    public AccountInfo getAccountInfo() {
        return accountInfo;
    }

    public HotelPolicyInput getHotelPolicyInput() {
        return hotelPolicyInput;
    }

    public IntegrationSoaRequestType getIntegrationSoaRequestType() {
        return integrationSoaRequestType;
    }

    public ApprovalInput getApprovalInput() {
        return approvalInput;
    }

    public AddPriceInput getAddPriceInput() {
        return addPriceInput;
    }

    public HotelBookInput getHotelBookInput() {
        return hotelBookInput;
    }

    public String getScene() {
        return scene;
    }

    public HotelInsuranceInput getHotelInsuranceInput() {
        return hotelInsuranceInput;
    }

    public List<HotelPayTypeInput> getHotelPayTypeInputs() {
        return hotelPayTypeInputs;
    }

    public GetSupportedPaymentMethodResponseType getGetSupportedPaymentMethodResponseType() {
        return getSupportedPaymentMethodResponseType;
    }

    public FlashStayInput getFlashStayInput() {
        return flashStayInput;
    }

    public CalculateServiceChargeV2ResponseType getCalculateServiceChargeV2ResponseType() {
        return calculateServiceChargeV2ResponseType;
    }

    public AllocationResultToken getAllocationResultToken() {
        return allocationResultToken;
    }

    public List<HotelBookPassengerInput> getHotelBookPassengerInputs() {
        return hotelBookPassengerInputs;
    }

    public CityInput getCityInput() {
        return cityInput;
    }

    public CorpPayInfo getCorpPayInfo() {
        return corpPayInfo;
    }

    public List<RCInput> getRcInfos() {
        return rcInfos;
    }

    public HotelPayTypeEnum getRoomPayType() {
        return roomPayType;
    }

    public List<StrategyInfo> getStrategyInfos() {
        return strategyInfos;
    }

    public static Builder builder() {
        return new Builder();
    }

    public ApprovalTextInfoResponseType getApprovalTextInfoResponseType() {
        return approvalTextInfoResponseType;
    }

    public static class Builder {
        private ResourceToken resourceToken;
        private WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo;
        private WrapperOfAccount.AccountInfo accountInfo;
        private HotelPolicyInput hotelPolicyInput;
        private IntegrationSoaRequestType integrationSoaRequestType;
        private ApprovalInput approvalInput;
        private AddPriceInput addPriceInput;
        private HotelBookInput hotelBookInput;
        private String scene;
        private HotelInsuranceInput hotelInsuranceInput;
        private List<HotelPayTypeInput> hotelPayTypeInputs;
        private GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponseType;
        private FlashStayInput flashStayInput;
        private CalculateServiceChargeV2ResponseType calculateServiceChargeV2ResponseType;
        private List<RCInput> rcInfos;
        private AllocationResultToken allocationResultToken;
        private List<HotelBookPassengerInput> hotelBookPassengerInputs;
        private HotelPayTypeEnum roomPayType;
        private CityInput cityInput;
        private CorpPayInfo corpPayInfo;

        private String policyToken;
        private String costCenterStr;

        private List<StrategyInfo> strategyInfos;

        private HotelPayTypeEnum servicePayType;
        private Map<String, StrategyInfo> strategyInfoMap;

        private QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig;
        private CostCenterInfo costCenterInfo;

        private ApprovalTextInfoResponseType approvalTextInfoResponseType;

        public Builder setApprovalTextInfoResponseType(ApprovalTextInfoResponseType approvalTextInfoResponseType) {
            this.approvalTextInfoResponseType = approvalTextInfoResponseType;
            return this;
        }
        public Builder setCostCenterInfo(CostCenterInfo costCenterInfo) {
            this.costCenterInfo = costCenterInfo;
            return this;
        }

        public Builder setQconfigOfCertificateInitConfig(
            QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {
            this.qconfigOfCertificateInitConfig = qconfigOfCertificateInitConfig;
            return this;
        }
        public Builder setServicePayType(HotelPayTypeEnum servicePayType) {
            this.servicePayType = servicePayType;
            return this;
        }
        public Builder setStrategyInfoMap(Map<String, StrategyInfo> strategyInfoMap) {
            this.strategyInfoMap = strategyInfoMap;
            return this;
        }

        public Builder setCostCenterStr(String costCenterStr) {
            this.costCenterStr = costCenterStr;
            return this;
        }

        public Builder setPolicyToken(String policyToken) {
            this.policyToken = policyToken;
            return this;
        }

        public Builder setResourceToken(ResourceToken resourceToken) {
            this.resourceToken = resourceToken;
            return this;
        }

        public Builder setCheckAvailInfo(WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
            this.checkAvailInfo = checkAvailInfo;
            return this;
        }

        public Builder setAccountInfo(WrapperOfAccount.AccountInfo accountInfo) {
            this.accountInfo = accountInfo;
            return this;
        }

        public Builder setHotelPolicyInput(HotelPolicyInput hotelPolicyInput) {
            this.hotelPolicyInput = hotelPolicyInput;
            return this;
        }

        public Builder setIntegrationSoaRequestType(IntegrationSoaRequestType integrationSoaRequestType) {
            this.integrationSoaRequestType = integrationSoaRequestType;
            return this;
        }

        public Builder setApprovalInput(ApprovalInput approvalInput) {
            this.approvalInput = approvalInput;
            return this;
        }

        public Builder setAddPriceInput(AddPriceInput addPriceInput) {
            this.addPriceInput = addPriceInput;
            return this;
        }

        public Builder setHotelBookInput(HotelBookInput hotelBookInput) {
            this.hotelBookInput = hotelBookInput;
            return this;
        }

        public Builder setScene(String scene) {
            this.scene = scene;
            return this;
        }

        public Builder setHotelInsuranceInput(HotelInsuranceInput hotelInsuranceInput) {
            this.hotelInsuranceInput = hotelInsuranceInput;
            return this;
        }

        public Builder setHotelPayTypeInputs(List<HotelPayTypeInput> hotelPayTypeInputs) {
            this.hotelPayTypeInputs = hotelPayTypeInputs;
            return this;
        }

        public Builder setGetSupportedPaymentMethodResponseType(GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponseType) {
            this.getSupportedPaymentMethodResponseType = getSupportedPaymentMethodResponseType;
            return this;
        }

        public Builder setFlashStayInput(FlashStayInput flashStayInput) {
            this.flashStayInput = flashStayInput;
            return this;
        }

        public Builder setCalculateServiceChargeV2ResponseType(CalculateServiceChargeV2ResponseType calculateServiceChargeV2ResponseType) {
            this.calculateServiceChargeV2ResponseType = calculateServiceChargeV2ResponseType;
            return this;
        }

        public Builder setRcInfos(List<RCInput> rcInfos) {
            this.rcInfos = rcInfos;
            return this;
        }

        public Builder setAllocationResultToken(AllocationResultToken allocationResultToken) {
            this.allocationResultToken = allocationResultToken;
            return this;
        }

        public Builder setHotelBookPassengerInputs(List<HotelBookPassengerInput> hotelBookPassengerInputs) {
            this.hotelBookPassengerInputs = hotelBookPassengerInputs;
            return this;
        }

        public Builder setCityInput(CityInput cityInput) {
            this.cityInput = cityInput;
            return this;
        }

        public Builder setCorpPayInfo(CorpPayInfo corpPayInfo) {
            this.corpPayInfo = corpPayInfo;
            return this;
        }

        public Builder setRoomPayType(HotelPayTypeEnum roomPayType) {
            this.roomPayType = roomPayType;
            return this;
        }

        public Builder setStrategyInfos(List<StrategyInfo> strategyInfos) {
            this.strategyInfos = strategyInfos;
            return this;
        }

        public WrapperOfCheckTravelPolicy build() {
            WrapperOfCheckTravelPolicy policy = new WrapperOfCheckTravelPolicy();
            policy.resourceToken = this.resourceToken;
            policy.checkAvailInfo = this.checkAvailInfo;
            policy.accountInfo = this.accountInfo;
            policy.hotelPolicyInput = this.hotelPolicyInput;
            policy.integrationSoaRequestType = this.integrationSoaRequestType;
            policy.approvalInput = this.approvalInput;
            policy.addPriceInput = this.addPriceInput;
            policy.hotelBookInput = this.hotelBookInput;
            policy.scene = this.scene;
            policy.hotelInsuranceInput = this.hotelInsuranceInput;
            policy.hotelPayTypeInputs = this.hotelPayTypeInputs;
            policy.getSupportedPaymentMethodResponseType = this.getSupportedPaymentMethodResponseType;
            policy.flashStayInput = this.flashStayInput;
            policy.calculateServiceChargeV2ResponseType = this.calculateServiceChargeV2ResponseType;
            policy.rcInfos = this.rcInfos;
            policy.allocationResultToken = this.allocationResultToken;
            policy.hotelBookPassengerInputs = this.hotelBookPassengerInputs;
            policy.cityInput = this.cityInput;
            policy.corpPayInfo = this.corpPayInfo;
            policy.policyToken = this.policyToken;
            policy.costCenterStr = this.costCenterStr;
            policy.roomPayType = this.roomPayType;
            policy.strategyInfos = this.strategyInfos;
            policy.servicePayType = this.servicePayType;
            policy.strategyInfoMap = this.strategyInfoMap;
            policy.qconfigOfCertificateInitConfig = this.qconfigOfCertificateInitConfig;
            policy.costCenterInfo = this.costCenterInfo;
            policy.approvalTextInfoResponseType = approvalTextInfoResponseType;
            return policy;
        }
    }
}