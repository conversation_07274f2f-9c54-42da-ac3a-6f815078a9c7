package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import corp.user.service.corpUserInfoService.GetUserServedCardInfoRequestType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/5/16
 */
@Component
public class MapperOfGetUserServedCardInfoRequestType extends AbstractMapper<Tuple5<PolicyInput, IntegrationSoaRequestType,
        WrapperOfAccount.AccountInfo, CorpPayInfo, CityInput>, GetUserServedCardInfoRequestType> {
    @Override
    protected GetUserServedCardInfoRequestType convert(Tuple5<PolicyInput, IntegrationSoaRequestType, WrapperOfAccount.AccountInfo, CorpPayInfo, CityInput> para) {
        if (para == null) {
            return null;
        }
        PolicyInput policyInput = para.getT1();
        IntegrationSoaRequestType integrationSoaRequestType = para.getT2();
        WrapperOfAccount.AccountInfo accountInfo = para.getT3();
        CorpPayInfo corpPayInfo = para.getT4();
        CityInput cityInfo = para.getT5();
        // 无政策执行人
        if (policyInput == null || StringUtil.isBlank(policyInput.getPolicyUid())) {
            return null;
        }
        // 无登录卡信息
        if (integrationSoaRequestType == null || integrationSoaRequestType.getUserInfo() == null || StringUtil.isBlank(integrationSoaRequestType.getUserInfo().getUserId())) {
            return null;
        }
        if (cityInfo == null || accountInfo == null) {
            return null;
        }
        // 行程+政策执行人
        if (accountInfo.isPackageEnabled() && accountInfo.isPolicyModel()) {
            return null;
        }
        // 同行程
        if (accountInfo.bookPolicyPsgMustSameTripApprove(CityInfoUtil.oversea(cityInfo.getCityId()), corpPayInfo)) {
            return null;
        }
        // 非政策执行人
        if (!accountInfo.isPolicyModel()) {
            return null;
        }
        // 政策执行人是自己
        if (StringUtil.equalsIgnoreCase(integrationSoaRequestType.getUserInfo().getUserId(), policyInput.getPolicyUid())) {
            return null;
        }
        GetUserServedCardInfoRequestType requestType = new GetUserServedCardInfoRequestType();
        requestType.setUid(Optional.ofNullable(integrationSoaRequestType.getUserInfo()).map(UserInfo::getUserId).orElse(null));
        requestType.setCorpId(Optional.ofNullable(integrationSoaRequestType.getUserInfo()).map(UserInfo::getCorpId).orElse(null));
        return requestType;
    }

    @Override
    protected ParamCheckResult check(Tuple5<PolicyInput, IntegrationSoaRequestType, WrapperOfAccount.AccountInfo,
            CorpPayInfo, CityInput> integrationSoaRequestTypeAccountInfoTuple2) {
        return null;
    }
}
