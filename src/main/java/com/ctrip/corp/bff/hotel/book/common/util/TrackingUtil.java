package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.bff.framework.hotel.common.enums.TrackingBookModeEnum;
import com.ctrip.corp.bff.framework.hotel.common.enums.TrackingEnum;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelGeoInfoResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ReservationResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/12/17 18:47
 */
public class TrackingUtil {
    public static Map<String, String> buildBaseTrackingMap(HotelBookInput hotelBookInput) {
        Map<String, String> trackingMap = new HashMap<>();
        trackingMap.put(TrackingEnum.CHECK_IN.getCode(),
            Optional.ofNullable(hotelBookInput).map(HotelBookInput::getHotelDateRangeInfo)
                .map(HotelDateRangeInfo::getCheckIn).orElse(StringUtil.EMPTY));
        trackingMap.put(TrackingEnum.CHECK_OUT.getCode(),
            Optional.ofNullable(hotelBookInput).map(HotelBookInput::getHotelDateRangeInfo)
                .map(HotelDateRangeInfo::getCheckOut).orElse(StringUtil.EMPTY));
        return trackingMap;
    }
}
