package com.ctrip.corp.bff.hotel.book.common.wrapper;

import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.mice.basic.auth.contract.TmsCreateOrderVerifyResponseType;
import com.ctrip.corp.bff.payment.contract.PaymentOrderCreateResponseType;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType;
import com.ctrip.corp.hotelbooking.hotelws.entity.SubmitOrderResponseType;
import com.ctrip.corp.order.paymentcenter.bill.contract.QueryPaymentBillConfigResponseType;
import com.ctrip.microfinance.giftcardpay.ws.contract.RetrieveTicketsByOrderTypeResponseType;
import com.ctrip.soa._21234.CreateTripResponseType;
import com.ctrip.soa._21685.PayConfigResponseType;
import com.ctrip.soa._21685.TransactionPayUrlResponseType;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/18 10:57
 */
public class WrapperOfFinishResponse {
    private TmsCreateOrderVerifyResponseType tmsCreateOrderVerifyResponseType;
    private CreateOrderResponseType createOrderResponseType;
    private PaymentOrderCreateResponseType paymentOrderCreateResponseType;
    private CreateTripResponseType createTripResponseType;
    private OrderCreateRequestType orderCreateRequestType;
    private ResourceToken resourceToken;
    private OrderCreateToken orderCreateToken;
    private QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType;
    private WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo;
    private TransactionPayUrlResponseType transactionPayUrlResponseType;
    private PayConfigResponseType payConfigResponseType;

    private WrapperOfAccount.AccountInfo accountInfo;
    private Boolean offlineNewPay;
    private RetrieveTicketsByOrderTypeResponseType retrieveTicketsByOrderTypeResponseType;

    private Map<String, StrategyInfo> strategyInfoMap;

    public RetrieveTicketsByOrderTypeResponseType getRetrieveTicketsByOrderTypeResponseType() {
        return retrieveTicketsByOrderTypeResponseType;
    }

    public Boolean getOfflineNewPay() {
        return offlineNewPay;
    }

    public PayConfigResponseType getPayConfigResponseType() {
        return payConfigResponseType;
    }

    public WrapperOfAccount.AccountInfo getAccountInfo() {
        return accountInfo;
    }

    public TransactionPayUrlResponseType getTransactionPayUrlResponseType() {
        return transactionPayUrlResponseType;
    }

    public TmsCreateOrderVerifyResponseType getTmsCreateOrderVerifyResponseType() {
        return tmsCreateOrderVerifyResponseType;
    }

    public CreateOrderResponseType getCreateOrderResponseType() {
        return createOrderResponseType;
    }

    public PaymentOrderCreateResponseType getPaymentOrderCreateResponseType() {
        return paymentOrderCreateResponseType;
    }

    public CreateTripResponseType getCreateTripResponseType() {
        return createTripResponseType;
    }

    public OrderCreateRequestType getOrderCreateRequestType() {
        return orderCreateRequestType;
    }

    public ResourceToken getResourceToken() {
        return resourceToken;
    }

    public OrderCreateToken getOrderCreateToken() {
        return orderCreateToken;
    }

    public QueryPaymentBillConfigResponseType getQueryPaymentBillConfigResponseType() {
        return queryPaymentBillConfigResponseType;
    }

    public WrapperOfCheckAvail.CheckAvailContextInfo getCheckAvailContextInfo() {
        return checkAvailContextInfo;
    }

    public static WrapperOfFinishResponse builder() {
        return new WrapperOfFinishResponse();
    }

    public WrapperOfFinishResponse setTmsCreateOrderVerifyResponseType(
        TmsCreateOrderVerifyResponseType tmsCreateOrderVerifyResponseType) {
        this.tmsCreateOrderVerifyResponseType = tmsCreateOrderVerifyResponseType;
        return this;
    }

    public WrapperOfFinishResponse setCreateOrderResponseType(CreateOrderResponseType createOrderResponseType) {
        this.createOrderResponseType = createOrderResponseType;
        return this;
    }

    public WrapperOfFinishResponse setPaymentOrderCreateResponseType(
        PaymentOrderCreateResponseType paymentOrderCreateResponseType) {
        this.paymentOrderCreateResponseType = paymentOrderCreateResponseType;
        return this;
    }

    public WrapperOfFinishResponse setCreateTripResponseType(CreateTripResponseType createTripResponseType) {
        this.createTripResponseType = createTripResponseType;
        return this;
    }

    public WrapperOfFinishResponse setOrderCreateRequestType(OrderCreateRequestType orderCreateRequestType) {
        this.orderCreateRequestType = orderCreateRequestType;
        return this;
    }

    public WrapperOfFinishResponse setResourceToken(ResourceToken resourceToken) {
        this.resourceToken = resourceToken;
        return this;
    }

    public WrapperOfFinishResponse setOrderCreateToken(OrderCreateToken orderCreateToken) {
        this.orderCreateToken = orderCreateToken;
        return this;
    }

    public WrapperOfFinishResponse setQueryPaymentBillConfigResponseType(
        QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType) {
        this.queryPaymentBillConfigResponseType = queryPaymentBillConfigResponseType;
        return this;
    }

    public WrapperOfFinishResponse setCheckAvailContextInfo(
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo) {
        this.checkAvailContextInfo = checkAvailContextInfo;
        return this;
    }

    public WrapperOfFinishResponse setTransactionPayUrlResponseType(
        TransactionPayUrlResponseType transactionPayUrlResponseType) {
        this.transactionPayUrlResponseType = transactionPayUrlResponseType;
        return this;
    }

    public WrapperOfFinishResponse setPayConfigResponseType(PayConfigResponseType payConfigResponseType) {
        this.payConfigResponseType = payConfigResponseType;
        return this;
    }

    public WrapperOfFinishResponse setAccountInfo(WrapperOfAccount.AccountInfo accountInfo) {
        this.accountInfo = accountInfo;
        return this;
    }

    public WrapperOfFinishResponse setOfflineNewPay(Boolean offlineNewPay) {
        this.offlineNewPay = offlineNewPay;
        return this;
    }

    public WrapperOfFinishResponse setRetrieveTicketsByOrderTypeResponseType(
        RetrieveTicketsByOrderTypeResponseType retrieveTicketsByOrderTypeResponseType) {
        this.retrieveTicketsByOrderTypeResponseType = retrieveTicketsByOrderTypeResponseType;
        return this;
    }

    public WrapperOfFinishResponse setStrategyInfoMap(Map<String, StrategyInfo> strategyInfoMap) {
        this.strategyInfoMap = strategyInfoMap;
        return this;
    }

    public Map<String, StrategyInfo> getStrategyInfoMap() {
        return strategyInfoMap;
    }
}
