package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.bff.framework.hotel.common.builder.SearchApprovalRequest;
import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelGeoInfoResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.order.data.aggregation.query.contract.*;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/7/30
 */
@Component
public class MapperOfHotelSearchApprovalRequest extends AbstractMapper<Tuple4<BookingInitRequestType,
        QueryHotelOrderDataResponseType, ResourceToken,
        WrapperOfAccount.AccountInfo>, SearchApprovalRequest> {
    @Override
    protected SearchApprovalRequest convert(Tuple4<BookingInitRequestType, QueryHotelOrderDataResponseType, ResourceToken,
            WrapperOfAccount.AccountInfo> para) {
        if (para == null) {
            return null;
        }
        BookingInitRequestType bookingInitRequestType = para.getT1();
        QueryHotelOrderDataResponseType queryHotelOrderDataResponseType = para.getT2();
        ResourceToken resourceToken = para.getT3();
        WrapperOfAccount.AccountInfo accountInfo = para.getT4();
        ApprovalInput approvalInput = new ApprovalInput();
        String mainApprovalNo = getMainApprovalNo(queryHotelOrderDataResponseType);
        String subApprovalNo = getSubApprovalNo(queryHotelOrderDataResponseType);
        String mainApprovalNoRequest = null;
        String subApprovalNoRequest = null;
        // 非提前审批不需要查询单据---应对单点场景，前端传入的审批单号是关联行程号，但是酒店压根没开提前审批的场景
        if (accountInfo.isPreApprovalRequired(CityInfoUtil.oversea(
                Optional.ofNullable(resourceToken).map(ResourceToken::getHotelResourceToken)
                        .map(HotelResourceToken::getHotelGeoInfoResourceToken).map(HotelGeoInfoResourceToken::getCityId)
                        .orElse(null)), bookingInitRequestType.getCorpPayInfo())) {
            mainApprovalNoRequest =
                    Optional.ofNullable(bookingInitRequestType.getApprovalInput()).map(ApprovalInput::getMasterApprovalNo)
                            .orElse(null);
            subApprovalNoRequest =
                    Optional.ofNullable(bookingInitRequestType.getApprovalInput()).map(ApprovalInput::getSubApprovalNo)
                            .orElse(null);
        }
        if (StringUtils.isEmpty(subApprovalNo) && StringUtil.isBlank(subApprovalNoRequest)) {
            return null;
        }
        String useMainApprovalNo =
                StringUtil.isNotBlank(mainApprovalNoRequest) ? mainApprovalNoRequest : mainApprovalNo;
        String useSubApprovalNo = StringUtil.isNotBlank(subApprovalNoRequest) ? subApprovalNoRequest : subApprovalNo;
        approvalInput.setMasterApprovalNo(useMainApprovalNo);
        approvalInput.setSubApprovalNo(useSubApprovalNo);
        return SearchApprovalRequest.builder()
                .approvalInput(approvalInput)
                .cityInput(getCityInput(queryHotelOrderDataResponseType, resourceToken))
                .integrationSoaRequestType(bookingInitRequestType.getIntegrationSoaRequestType())
                .returnRegionControlCityInfo(true)
                .compatibleCitySplit(false)
                .build();
    }

    private CityInput getCityInput(QueryHotelOrderDataResponseType queryHotelOrderDataResponseType,
                                   ResourceToken resourceToken) {
        if (TemplateNumberUtil.isNotZeroAndNull(
                Optional.ofNullable(resourceToken).map(ResourceToken::getHotelResourceToken)
                        .map(HotelResourceToken::getHotelGeoInfoResourceToken).map(HotelGeoInfoResourceToken::getCityId)
                        .orElse(null))) {
            CityInput cityInput = new CityInput();
            cityInput.setCityId(resourceToken.getHotelResourceToken().getHotelGeoInfoResourceToken().getCityId());
            return cityInput;
        }
        Integer cityId = Optional.ofNullable(queryHotelOrderDataResponseType)
                .map(QueryHotelOrderDataResponseType::getHotelInfo).map(HotelInfoType::getHotelProduct)
                .map(HotelProductType::getHotel).map(HotelType::getCityId).orElse(-1);
        CityInput cityInput = new CityInput();
        cityInput.setCityId(cityId);
        return cityInput;
    }

    private String getSubApprovalNo(QueryHotelOrderDataResponseType queryHotelOrderDataResponseType) {
        return Optional.ofNullable(queryHotelOrderDataResponseType).map(QueryHotelOrderDataResponseType::getTravelInfo)
                .map(TravelInfoType::getApproval)
                .map(ApprovalType::getSubPreApprovalNo).orElse(null);
    }

    private String getMainApprovalNo(QueryHotelOrderDataResponseType queryHotelOrderDataResponseType) {
        return Optional.ofNullable(queryHotelOrderDataResponseType).map(QueryHotelOrderDataResponseType::getTravelInfo)
                .map(TravelInfoType::getApproval)
                .map(ApprovalType::getPrepareApprovalNo).orElse(null);
    }

    @Override
    protected ParamCheckResult check(Tuple4<BookingInitRequestType, QueryHotelOrderDataResponseType,
            ResourceToken, WrapperOfAccount.AccountInfo> para) {
        return null;
    }
}
