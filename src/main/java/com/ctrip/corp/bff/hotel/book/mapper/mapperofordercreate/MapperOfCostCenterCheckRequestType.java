package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount.AccountInfo;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.OrderResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple6;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail.BaseCheckAvailInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.bff.specific.contract.CostCenterCheckRequestType;
import com.ctrip.corp.bff.specific.contract.CostCenterPassenger;
import org.apache.kafka.common.config.ConfigDef.NonEmptyStringWithoutControlChars;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/29 21:41
 */
@Component public class MapperOfCostCenterCheckRequestType extends
    AbstractMapper<Tuple6<OrderCreateRequestType, ResourceToken, BaseCheckAvailInfo,
            QconfigOfCertificateInitConfig, AccountInfo, Map<String, StrategyInfo>>, CostCenterCheckRequestType> {
    public static final String PRODUCT_TYPE_CN_HOTEL = "CN_HOTEL";
    public static final String PRODUCT_TYPE_INTERNATIONAL_HOTEL = "INTERNATIONAL_HOTEL";
    public static final String BOOKING_TYPE_HOTEL_CHANGE = "HOTEL_CHANGE";
    public static final String BOOKING_TYPE_NORMAL = "NORMAL";
    public static final String STRATEGY_KEY_COST_CENTER_NEW = "COST_CENTER_NEW";
    public static final String STRATEGY_VALUE_NOT_REQUIRED_COST_CENTER = "T";

    @Override protected CostCenterCheckRequestType convert(
        Tuple6<OrderCreateRequestType, ResourceToken, WrapperOfCheckAvail.BaseCheckAvailInfo,
            QconfigOfCertificateInitConfig,
            AccountInfo, Map<String, StrategyInfo>> tuple) {
        OrderCreateRequestType orderCreateRequestType = tuple.getT1();
        ResourceToken resourceToken = tuple.getT2();
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = tuple.getT3();
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig = tuple.getT4();
        WrapperOfAccount.AccountInfo accountInfo = tuple.getT5();
        Map<String, StrategyInfo> strategyInfoMap = tuple.getT6();
        CostCenterCheckRequestType costCenterCheckRequestType = new CostCenterCheckRequestType();
        costCenterCheckRequestType.setIntegrationSoaRequestType(orderCreateRequestType.getIntegrationSoaRequestType());
        costCenterCheckRequestType.setTripInput(orderCreateRequestType.getTripInput());
        costCenterCheckRequestType.setSSOInput(orderCreateRequestType.getSsoInput());
        costCenterCheckRequestType.setPolicyInput(
            Optional.ofNullable(orderCreateRequestType.getHotelPolicyInput()).map(HotelPolicyInput::getPolicyInput)
                .orElse(null));
        costCenterCheckRequestType.setCorpPayInfo(orderCreateRequestType.getCorpPayInfo());
        costCenterCheckRequestType.setProductType(CityInfoUtil.oversea(
            Optional.ofNullable(orderCreateRequestType.getCityInput()).map(CityInput::getCityId).orElse(null)) ?
            PRODUCT_TYPE_INTERNATIONAL_HOTEL : PRODUCT_TYPE_CN_HOTEL);
        Long originOrderId =
            Optional.ofNullable(resourceToken.getOrderResourceToken()).map(OrderResourceToken::getOrderId).orElse(null);
        if (TemplateNumberUtil.getValue(originOrderId) > 0) {
            costCenterCheckRequestType.setOriginalOrderId(String.valueOf(originOrderId));
            costCenterCheckRequestType.setBookingType(BOOKING_TYPE_HOTEL_CHANGE);
        } else {
            costCenterCheckRequestType.setBookingType(BOOKING_TYPE_NORMAL);
        }
        costCenterCheckRequestType.setCostCenterInputs(orderCreateRequestType.getCostCenterInputs());
        costCenterCheckRequestType.setApprovalInput(orderCreateRequestType.getApprovalInput());
        costCenterCheckRequestType.setCostCenterPassengers(
            buildCostCenterPassengers(orderCreateRequestType, checkAvailInfo, qconfigOfCertificateInitConfig,
                strategyInfoMap));
        if (OrderCreateProcessorOfUtil.needCostCenterNew(resourceToken, orderCreateRequestType.getCorpPayInfo(),
            accountInfo, strategyInfoMap)) {
            costCenterCheckRequestType.setCostCenterInfo(orderCreateRequestType.getCostCenterInfoNew());
            costCenterCheckRequestType.setStrategyInfos(buildStrategyInfos());
        }
        return costCenterCheckRequestType;
    }

    @Override protected ParamCheckResult check(
        Tuple6<OrderCreateRequestType, ResourceToken, BaseCheckAvailInfo,
                    QconfigOfCertificateInitConfig,
            AccountInfo, Map<String, StrategyInfo>> tuple) {
        return null;
    }

    protected List<StrategyInfo> buildStrategyInfos() {
        List<StrategyInfo> strategyInfos = new ArrayList<>();
        StrategyInfo strategyInfoNewCostCenter = new StrategyInfo();
        strategyInfoNewCostCenter.setStrategyKey(STRATEGY_KEY_COST_CENTER_NEW);
        strategyInfoNewCostCenter.setStrategyValue(STRATEGY_VALUE_NOT_REQUIRED_COST_CENTER);
        strategyInfos.add(strategyInfoNewCostCenter);
        return strategyInfos;
    }

    public List<CostCenterPassenger> buildCostCenterPassengers(OrderCreateRequestType orderCreateRequestType,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo, QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        Map<String, StrategyInfo> strategyInfoMap) {
        return orderCreateRequestType.getHotelBookPassengerInputs().stream().filter(Objects::nonNull)
            .map(hotelBookPassengerInput -> {
                CostCenterPassenger costCenterPassenger = new CostCenterPassenger();
                costCenterPassenger.setUid(buildId(hotelBookPassengerInput));
                costCenterPassenger.setEmployee(Optional.ofNullable(hotelBookPassengerInput.getHotelPassengerInput())
                    .map(HotelPassengerInput::getEmployee).orElse(BooleanUtil.parseStr(false)));
                costCenterPassenger.setPassengerName(OrderCreateProcessorOfUtil.getUseName(hotelBookPassengerInput,
                    orderCreateRequestType.getCityInput().getCityId(), checkAvailInfo, qconfigOfCertificateInitConfig,
                    strategyInfoMap));
                costCenterPassenger.setPassengerKey(UUID.randomUUID().toString());
                costCenterPassenger.setApprovalInput(
                    Optional.ofNullable(hotelBookPassengerInput.getHotelPassengerInput())
                        .map(HotelPassengerInput::getApprovalInput).orElse(null));
                return costCenterPassenger;
            }).collect(Collectors.toList());
    }

    private String buildId(HotelBookPassengerInput hotelBookPassengerInput) {
        return StringUtil.isNotBlank(hotelBookPassengerInput.getHotelPassengerInput().getUid()) ?
            hotelBookPassengerInput.getHotelPassengerInput().getUid() :
            hotelBookPassengerInput.getHotelPassengerInput().getInfoId();
    }

}
