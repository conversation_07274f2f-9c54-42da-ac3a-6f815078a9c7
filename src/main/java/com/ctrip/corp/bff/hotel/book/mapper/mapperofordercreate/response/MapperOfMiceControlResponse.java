package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationResponse;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfOrderCreateUtil;
import com.ctrip.corp.bff.mice.basic.auth.contract.TmsCreateOrderVerifyResponseType;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2024/6/28 22:27
 */
@Component
public class MapperOfMiceControlResponse extends AbstractMapper<Tuple2<TmsCreateOrderVerifyResponseType, Map<String, StrategyInfo>>, TmsCreateOrderVerifyResponseType> {

    private static final int APPID = 100053122;

    @Override protected TmsCreateOrderVerifyResponseType convert(Tuple2<TmsCreateOrderVerifyResponseType, Map<String, StrategyInfo>> request) {
        return request.getT1();
    }

    @Override protected ParamCheckResult check(Tuple2<TmsCreateOrderVerifyResponseType, Map<String, StrategyInfo>> request) {
        TmsCreateOrderVerifyResponseType tmsCreateOrderVerifyResponseType = request.getT1();
        Map<String, StrategyInfo> strategyInfoMap = request.getT2();
        if (tmsCreateOrderVerifyResponseType == null) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.TMS_CREATE_ORDER_VERIFY_RESPONSE_IS_NULL, "");
        }
        // 活动详情地址不为空
        if (!StringUtil.isEmpty(tmsCreateOrderVerifyResponseType.getActivityDetailUrl())) {
            return null;
        }
        String errorCode = Optional.ofNullable(tmsCreateOrderVerifyResponseType.getIntegrationResponse()).map(IntegrationResponse::getErrorCode).orElse(null);
        if (!StringUtil.isEmpty(errorCode)) {
            String errorMessage = Optional.ofNullable(tmsCreateOrderVerifyResponseType.getIntegrationResponse()).map(IntegrationResponse::getErrorMessage).orElse(null);
            return new ParamCheckResult(false,
                OrderCreateErrorEnum.TMS_CREATE_ORDER_VERIFY_RESPONSE_IS_ERROR.getErrorCode(),
                errorCode,
                errorMessage,
                BFFSharkUtil.getSoaCodeSharkValue(APPID, "CorpBffMiceBasicAuthService", "tmsCreateOrderVerify",
                    errorCode));
        }
        if (StrategyOfOrderCreateUtil.miceOfflineBooking(strategyInfoMap)) {
            return null;
        }
        return new ParamCheckResult(false, OrderCreateErrorEnum.ACTIVITY_DETAIL_URL_IS_NULL, "");
    }
}
