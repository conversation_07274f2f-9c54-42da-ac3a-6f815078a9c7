package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount.AccountInfo;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail.CheckAvailContextInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.BookingSourceEnum;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.BookingTypeEnum;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.ProductTypeEnum;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.verifyfellowpassenger.VerifyFellowPassengerInfo;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.verifyfellowpassenger.VerifyFellowPassengerRequestType;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.verifyfellowpassenger.VerifyOrderInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ctrip.corp.corpsz.preapprovalws.common.contract.BookingSourceEnum.ONLINE;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 校验同行出行人请求
 * @Date 2024/8/9 14:08
 * @Version 1.0
 */
@Component
public class MapperOfVerifyFellowPassengerRequestType
        extends AbstractMapper<Tuple5<OrderCreateRequestType, AccountInfo, WrapperOfCheckAvail.CheckAvailContextInfo,
    QconfigOfCertificateInitConfig,
    Map<String, StrategyInfo>>
        , VerifyFellowPassengerRequestType> {

    @Override
    protected VerifyFellowPassengerRequestType convert(Tuple5<OrderCreateRequestType, WrapperOfAccount.AccountInfo,
        WrapperOfCheckAvail.CheckAvailContextInfo, QconfigOfCertificateInitConfig,
        Map<String, StrategyInfo>> tuple) {
        VerifyFellowPassengerRequestType result = new VerifyFellowPassengerRequestType();
        OrderCreateRequestType orderCreateRequestType = tuple.getT1();
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = tuple.getT3();
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig = tuple.getT4();
        Map<String, StrategyInfo> strategyInfoMap = tuple.getT5();

        UserInfo userInfo = Optional.ofNullable(orderCreateRequestType.getIntegrationSoaRequestType())
                .map(IntegrationSoaRequestType::getUserInfo).orElse(null);
        if (userInfo != null) {
            result.setCorpID(userInfo.getCorpId());
            result.setUID(userInfo.getUserId());
        }
        String approvalNumber = Optional.ofNullable(orderCreateRequestType.getApprovalInput())
                .map(ApprovalInput::getSubApprovalNo).orElse(null);
        result.setApprovalNumber(approvalNumber);
        String requestId = Optional.ofNullable(orderCreateRequestType.getIntegrationSoaRequestType())
                .map(IntegrationSoaRequestType::getRequestId).orElse(null);
        result.setRequestID(requestId);
        Integer cityId = Optional.ofNullable(orderCreateRequestType.getCityInput())
                .map(CityInput::getCityId).orElse(null);
        if (CityInfoUtil.oversea(cityId)) {
            result.setProductType(ProductTypeEnum.OVERSEA_HOTEL);
        } else {
            result.setProductType(ProductTypeEnum.DOMESTIC_HOTEL);
        }
        SourceFrom sourceFrom = Optional.ofNullable(orderCreateRequestType.getIntegrationSoaRequestType())
                .map(IntegrationSoaRequestType::getSourceFrom).orElse(null);
        result.setSourceFrom(getSourceFrom(sourceFrom));
        result.setBookingType(BookingTypeEnum.NORMAL);
        HotelPolicyInput hotelPolicyInput = orderCreateRequestType.getHotelPolicyInput();
        result.setPolicyInfo(getPolicyInfo(hotelPolicyInput));
        List<HotelBookPassengerInput> hotelPassengerInfos = orderCreateRequestType.getHotelBookPassengerInputs();
        result.setFellowPassengerList(
            getFellowPassengerList(hotelPassengerInfos, checkAvailInfo, qconfigOfCertificateInitConfig, strategyInfoMap));
        result.setOrderInfo(getOrderInfo(orderCreateRequestType));
        return result;
    }

    @Override
    protected ParamCheckResult check(Tuple5<OrderCreateRequestType, AccountInfo,
                CheckAvailContextInfo, QconfigOfCertificateInitConfig, Map<String, StrategyInfo>> tuple) {
        OrderCreateRequestType orderCreateRequestType = tuple.getT1();
        WrapperOfAccount.AccountInfo accountInfo = tuple.getT2();
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo = tuple.getT3();
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig = tuple.getT4();
        Map<String, StrategyInfo> strategyInfoMap = tuple.getT5();
        if (checkAvailInfo == null || accountInfo == null) {
            return null;
        }
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo()) || !accountInfo.isPolicyPsgSameTrip()
            || !accountInfo.isPolicyModel()) {
            return null;
        }
        if (!accountInfo.preApprovalSameTrip(CityInfoUtil.oversea(orderCreateRequestType.getCityInput().getCityId()),
            orderCreateRequestType.getCorpPayInfo())) {
            return null;
        }
        // 入住人校验同行程审批单据，入住人必须包含审批单号
        if (orderCreateRequestType.getHotelBookPassengerInputs().stream().anyMatch(p -> StringUtil.isBlank(
            Optional.ofNullable(p).map(HotelBookPassengerInput::getHotelPassengerInput)
                .map(HotelPassengerInput::getApprovalInput).map(ApprovalInput::getSubApprovalNo).orElse(null)))) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.PSG_MISS_SUBAPPROVALNO,
                OrderCreateErrorEnum.PSG_MISS_SUBAPPROVALNO.getErrorMessage());
        }
        String policyUid =
            Optional.ofNullable(orderCreateRequestType.getHotelPolicyInput()).map(HotelPolicyInput::getPolicyInput)
                .map(PolicyInput::getPolicyUid).orElse(null);
        if (StringUtil.isBlank(policyUid)) {
            return null;
        }
        String subApprovalNoPolicy = Optional.ofNullable(orderCreateRequestType.getHotelPolicyInput())
            .map(HotelPolicyInput::getApprovalInput)
            .map(ApprovalInput::getSubApprovalNo).orElse(null);

        HotelBookPassengerInput hotelBookPassengerInput = orderCreateRequestType.getHotelBookPassengerInputs().stream()
            .filter(p -> policyUid.equalsIgnoreCase(p.getHotelPassengerInput().getUid())
                && p.getHotelPassengerInput().getApprovalInput() != null && StringUtil.isNotBlank(
                p.getHotelPassengerInput().getApprovalInput().getSubApprovalNo())).collect(Collectors.toList()).stream()
            .findFirst().orElse(null);
        if (hotelBookPassengerInput == null) {
            return null;
        }
        String subApprovalNoPsg =
            hotelBookPassengerInput.getHotelPassengerInput().getApprovalInput().getSubApprovalNo();
        if (StringUtil.equalsIgnoreCase(subApprovalNoPsg, subApprovalNoPolicy)) {
            return null;
        }
        String friendlyMessage = StringUtil.indexedFormat(
            BFFSharkUtil.getSharkValue("OrderCreateErrorEnum.INVALID_TRAVEL_TOGETHER_PASSENGER"),
            OrderCreateProcessorOfUtil.getUseName(hotelBookPassengerInput,
                OrderCreateProcessorOfUtil.getCityId(orderCreateRequestType), checkAvailInfo,
                qconfigOfCertificateInitConfig, strategyInfoMap));
        return new ParamCheckResult(false,
            OrderCreateErrorEnum.INVALID_TRAVEL_TOGETHER_PASSENGER.getErrorCode(),
            "checkTravelTogether error", friendlyMessage, friendlyMessage);
    }

    private BookingSourceEnum getSourceFrom(SourceFrom sourceFrom) {
        if (sourceFrom == null) {
            return BookingSourceEnum.UNKNOWN;
        }
        switch (sourceFrom) {
            case Online:
                return ONLINE;
            case Offline:
                return BookingSourceEnum.OFFLINE;
            case Native:
            case H5:
            case CRN:
                return BookingSourceEnum.MOBILE;
            default:
                return BookingSourceEnum.UNKNOWN;
        }
    }

    private VerifyFellowPassengerInfo getPolicyInfo(HotelPolicyInput hotelPolicyInput) {
        String policyUid =
            Optional.ofNullable(hotelPolicyInput).map(HotelPolicyInput::getPolicyInput).map(PolicyInput::getPolicyUid)
                .orElse(null);
        if (StringUtil.isEmpty(policyUid)) {
            return null;
        }
        VerifyFellowPassengerInfo result = new VerifyFellowPassengerInfo();
        result.setUID(policyUid);
        String approvalNumber = Optional.ofNullable(hotelPolicyInput.getApprovalInput())
                .map(ApprovalInput::getSubApprovalNo).orElse(null);
        result.setApprovalNumber(approvalNumber);
        return result;
    }

    private List<VerifyFellowPassengerInfo> getFellowPassengerList(
        final List<HotelBookPassengerInput> hotelPassengerInfos, WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        Map<String, StrategyInfo> strategyInfoMap) {
        if (CollectionUtil.isEmpty(hotelPassengerInfos)) {
            return null;
        }
        List<VerifyFellowPassengerInfo> result = new ArrayList<>();
        for (HotelBookPassengerInput passengerInput : hotelPassengerInfos) {
            if (passengerInput == null) {
                continue;
            }
            VerifyFellowPassengerInfo verifyFellowPassengerInfo = new VerifyFellowPassengerInfo();
            HotelPassengerInput hotelPassengerInput = passengerInput.getHotelPassengerInput();
            if (hotelPassengerInput != null) {
                verifyFellowPassengerInfo.setNonEmployeeId(hotelPassengerInput.getInfoId());
                String uid = null;
                if (StringUtil.equalsIgnoreCase(hotelPassengerInput.getEmployee(), BooleanUtil.parseStr(Boolean.TRUE))) {
                    uid = hotelPassengerInput.getUid();
                }
                verifyFellowPassengerInfo.setUID(uid);
                String approvalNumber = Optional.ofNullable(hotelPassengerInput.getApprovalInput())
                    .map(ApprovalInput::getSubApprovalNo).orElse(null);
                verifyFellowPassengerInfo.setApprovalNumber(approvalNumber);
            }

            verifyFellowPassengerInfo.setName(passengerInput.getName());
            verifyFellowPassengerInfo.setNameEn(
                OrderCreateProcessorOfUtil.getEname(passengerInput, checkAvailInfo, qconfigOfCertificateInitConfig, strategyInfoMap));
            result.add(verifyFellowPassengerInfo);
        }
        return result;
    }

    private VerifyOrderInfo getOrderInfo(OrderCreateRequestType orderCreateRequestType) {
        VerifyOrderInfo verifyOrderInfo = new VerifyOrderInfo();
        Integer cityId = Optional.ofNullable(orderCreateRequestType.getCityInput())
                .map(CityInput::getCityId).orElse(null);
        verifyOrderInfo.setCityID(cityId);
        String endDate = Optional.ofNullable(orderCreateRequestType.getHotelBookInput())
                .map(HotelBookInput::getHotelDateRangeInfo)
                .map(HotelDateRangeInfo::getCheckOut).orElse(null);
        verifyOrderInfo.setEndDate(endDate);
        String startDate = Optional.ofNullable(orderCreateRequestType.getHotelBookInput())
                .map(HotelBookInput::getHotelDateRangeInfo)
                .map(HotelDateRangeInfo::getCheckIn).orElse(null);
        verifyOrderInfo.setStartDate(startDate);
        return verifyOrderInfo;
    }
}
