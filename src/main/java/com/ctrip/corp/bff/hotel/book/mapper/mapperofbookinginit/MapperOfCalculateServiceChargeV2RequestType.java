package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.agg.hotel.expense.contract.model.*;
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelItem;
import com.ctrip.corp.agg.hotel.roomavailable.entity.RommDailySalePromotionEntity;
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomItem;
import com.ctrip.corp.agg.hotel.roomavailable.entity.SalePromotionEntity;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.HotelDateRangeUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount.AccountInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.HostUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple8;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelBalanceTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.BookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.util.DateUtils;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail.CheckAvailInfo;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.hotel.book.query.entity.DetailBaseInfoEntity;
import com.ctrip.corp.hotel.book.query.entity.GetHotelDetailInfoResponseType;
import com.ctrip.corp.hotel.book.query.entity.HotelBrandEntity;
import com.ctrip.corp.hotel.book.query.entity.HotelDetailInfoEntity;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodResponseType;
import com.ctrip.corp.hotelbook.commonws.entity.PaymentMethodInfoType;
import com.ctrip.corp.order.data.aggregation.query.contract.HotelOrderType;
import com.ctrip.corp.order.data.aggregation.query.contract.OrderBasicInfoType;
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType;
import com.ctrip.corp.order.data.aggregation.query.contract.ServiceFeeConfigType;
import com.ctrip.model.CalculateServiceChargeV2RequestType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.QueryOrderSettingsResponseType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.ServiceFeeSettingType;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 费用计算服务费
 * @Date 2024/8/22 09:59
 * @Version 1.0
 */
@Component
public class MapperOfCalculateServiceChargeV2RequestType extends AbstractMapper<Tuple8<WrapperOfAccount.AccountInfo
    , QueryHotelOrderDataResponseType, QueryOrderSettingsResponseType
    , WrapperOfCheckAvail.CheckAvailInfo, BookingInitRequestType, ResourceToken
    , GetSupportedPaymentMethodResponseType, GetHotelDetailInfoResponseType>, CalculateServiceChargeV2RequestType> {
    private static final String PRE_CHARGE = "PreCharge";
    @Override
    protected CalculateServiceChargeV2RequestType convert(Tuple8<AccountInfo
                , QueryHotelOrderDataResponseType, QueryOrderSettingsResponseType
                , CheckAvailInfo, BookingInitRequestType, ResourceToken
            , GetSupportedPaymentMethodResponseType, GetHotelDetailInfoResponseType > param) {
        WrapperOfAccount.AccountInfo accountInfo = param.getT1();
        QueryHotelOrderDataResponseType queryHotelOrderDataResponseType = param.getT2();
        QueryOrderSettingsResponseType queryOrderSettingsResponseType = param.getT3();
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = param.getT4();
        BookingInitRequestType bookingInitRequestType = param.getT5();
        boolean isBlueSpacePreChargeService = StrategyOfBookingInitUtil.isBlueSpacePreChargeService(
                Optional.ofNullable(bookingInitRequestType.getStrategyInfos()).orElse(Collections.emptyList()));
        GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponseType = param.getT7();
        ResourceToken resourceToken = param.getT6();
        CalculateServiceChargeV2RequestType requestType = new CalculateServiceChargeV2RequestType();
        requestType.setServiceChargeToken(buildServiceChargeToken(queryOrderSettingsResponseType, bookingInitRequestType));
        requestType.setAmsFeeConfigVersion(buildOrderAmsFeeConfigVersion(queryHotelOrderDataResponseType, bookingInitRequestType));
        requestType.setAmsChargeVersion(buildAmsFeeConfigVersion(accountInfo, queryHotelOrderDataResponseType, bookingInitRequestType));
        requestType.setBaseInfo(getBaseInfoType(accountInfo, bookingInitRequestType));
        requestType.setOperationInfo(getOperationScenarioInfoType(bookingInitRequestType));
        requestType.setBookingInfo(buildBookingInfoType(bookingInitRequestType, getSupportedPaymentMethodResponseType));
        requestType.setHotelInfo(buildHotelInfoType(checkAvailInfo,param.getT8()));
        requestType.setRoomInfo(buildRoomInfoType(resourceToken));
        requestType.setOrderInfo(getOrderAmountInfoType(queryHotelOrderDataResponseType, bookingInitRequestType));
        requestType.setCalculateAmountInfo(getCalculateAmountInfoType(checkAvailInfo, isBlueSpacePreChargeService));
        requestType.setChargeMomentFilter(isBlueSpacePreChargeService ? PRE_CHARGE : null);
        return requestType;
    }

    protected String buildOrderAmsFeeConfigVersion(QueryHotelOrderDataResponseType queryHotelOrderDataResponseType, BookingInitRequestType bookingInitRequestType){
        if (StrategyOfBookingInitUtil.modify(bookingInitRequestType.getStrategyInfos()) ||
            StrategyOfBookingInitUtil.onlyApplyModify(bookingInitRequestType.getStrategyInfos())) {
            return Optional.ofNullable(queryHotelOrderDataResponseType).map(QueryHotelOrderDataResponseType::getHotelInfo)
                .map(com.ctrip.corp.order.data.aggregation.query.contract.HotelInfoType::getHotelOrder).map(HotelOrderType::getServiceFeeConfig)
                .map(ServiceFeeConfigType::getAmsFeeConfigVersion).orElse(null);
        }
        return null;
    }

    protected CalculateAmountContextInfoType getCalculateAmountInfoType(CheckAvailInfo checkAvailInfo, Boolean isBlueSpacePreChargeService) {
        RoomItem roomItem = Optional.ofNullable(checkAvailInfo.getRoomItem()).orElse(new RoomItem());
        CalculateAmountContextInfoType calculateAmountInfoType = new CalculateAmountContextInfoType();
        calculateAmountInfoType.setCalculationBaseAmount(BooleanUtil.isTrue(isBlueSpacePreChargeService) ? Optional.ofNullable(roomItem.getSalePromotionInfo())
                .map(SalePromotionEntity::getAfterPromotCustomAmount).orElse(roomItem.getCustomAmount()) : roomItem.getCustomAmount());
        calculateAmountInfoType.setCustomExchange(roomItem.getCustomExchange());
        calculateAmountInfoType.setOriginalExchange(roomItem.getOriginExchange());
        calculateAmountInfoType.setDailyRatePerRoom(BooleanUtil.isTrue(isBlueSpacePreChargeService) ? buildDailyRatePerRoom(checkAvailInfo) : null);
        return calculateAmountInfoType;
    }

    private List<DailyRatePerRoomType> buildDailyRatePerRoom(CheckAvailInfo checkAvailInfo) {
        if (checkAvailInfo == null || CollectionUtil.isEmpty(checkAvailInfo.getRoomDailyInfos())) {
            return null;
        }
        return checkAvailInfo.getRoomDailyInfos().stream().filter(Objects::nonNull).map(t -> {
            DailyRatePerRoomType dailyRatePerRoomType = new DailyRatePerRoomType();
            dailyRatePerRoomType.setEffectDate(t.getLocalEffectDate());
            dailyRatePerRoomType.setAmountPerRoom(Optional.ofNullable(t.getSalePromotionInfo())
                    .map(RommDailySalePromotionEntity::getAfterPromotCustomAmount).orElse(t.getCustomAmount()));
            return dailyRatePerRoomType;
        }).collect(Collectors.toList());
    }

    private OrderAmountInfoType getOrderAmountInfoType(QueryHotelOrderDataResponseType queryHotelOrderDataResponseType
        , BookingInitRequestType bookingInitRequestType) {
        if (queryHotelOrderDataResponseType == null) {
            return null;
        }
        if (!buildModifyServiceFeeOrder(bookingInitRequestType)) {
            return null;
        }
        OrderAmountInfoType orderAmountInfoType = new OrderAmountInfoType();
        orderAmountInfoType.setOrderId(Optional.ofNullable(queryHotelOrderDataResponseType.getOrderBasicInfo())
            .map(OrderBasicInfoType::getOrderId).orElse(null));
        orderAmountInfoType.setLastCancelTimeUTC(Optional.ofNullable(queryHotelOrderDataResponseType.getOrderGenericInfo())
            .map(com.ctrip.corp.order.data.aggregation.query.contract.OrderGenericInfoType::getCancelInfo)
            .map(com.ctrip.corp.order.data.aggregation.query.contract.CancelInfoType::getCancelDeadlineUTC).orElse(null));
        orderAmountInfoType.setLastModifyTimeUTC(Optional.ofNullable(queryHotelOrderDataResponseType.getOrderGenericInfo())
            .map(com.ctrip.corp.order.data.aggregation.query.contract.OrderGenericInfoType::getCancelInfo)
            .map(com.ctrip.corp.order.data.aggregation.query.contract.CancelInfoType::getCancelDeadlineUTC).orElse(null));
        orderAmountInfoType.setOrderAmsChargeVersion(Optional.ofNullable(queryHotelOrderDataResponseType.getHotelInfo())
            .map(com.ctrip.corp.order.data.aggregation.query.contract.HotelInfoType::getHotelOrder)
            .map(HotelOrderType::getServiceFeeConfig).map(ServiceFeeConfigType::getServiceFeeVersion).orElse(null));
        return orderAmountInfoType;
    }

    private static final String ACCOUNT_PAY = "ACCOUNT_PAY";

    private static final String INDIVIDUAL_PAY = "INDIVIDUAL_PAY";

    public static BookingInfoType buildBookingInfoType(BookingInitRequestType bookingInitRequestType,
        GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponseType) {
        boolean isBlueSpacePreChargeService = StrategyOfBookingInitUtil.isBlueSpacePreChargeService(
                Optional.ofNullable(bookingInitRequestType.getStrategyInfos()).orElse(Collections.emptyList()));
        BookingInfoType bookingInfoType = new BookingInfoType();
        bookingInfoType.setFeeType(CorpPayInfoUtil.isPrivate(bookingInitRequestType.getCorpPayInfo()) ? "P" : "C");
        long occupanycyDays = HotelDateRangeUtil.getDays(bookingInitRequestType.getHotelBookInput().getHotelDateRangeInfo());
        bookingInfoType.setOccupancyDays((int)occupanycyDays);
        bookingInfoType.setRoomQuantity(bookingInitRequestType.getHotelBookInput().getRoomQuantity());
        bookingInfoType.setPaymentMethodList(isBlueSpacePreChargeService ? null : Arrays.asList("AccountPay", "PersonalPay"));
        bookingInfoType.setRoomPaymentMethodList(isBlueSpacePreChargeService ? Arrays.asList(ACCOUNT_PAY, INDIVIDUAL_PAY)
                : Optional.ofNullable(getSupportedPaymentMethodResponseType)
            .map(GetSupportedPaymentMethodResponseType::getPaymentMethodList).orElse(Collections.emptyList()).stream()
            .filter(Objects::nonNull).map(PaymentMethodInfoType::getPaymentMethod).filter(Objects::nonNull).toList());
        return bookingInfoType;
    }

    protected OperationScenarioInfoType getOperationScenarioInfoType(BookingInitRequestType bookingInitRequestType) {
        OperationScenarioInfoType operationScenarioInfoType = new OperationScenarioInfoType();
        operationScenarioInfoType.setOperationScenario(getOperationScenario(bookingInitRequestType));
        String utcString = DateUtils.getUTCTimeStr();
        operationScenarioInfoType.setOperationTimeUTC(utcString);
        operationScenarioInfoType.setOperationChannel(buildOperationChannel(bookingInitRequestType));
        return operationScenarioInfoType;
    }

    private static String buildOperationChannel(BookingInitRequestType bookingInitRequestType) {
        switch (bookingInitRequestType.getIntegrationSoaRequestType().getSourceFrom()) {
            case Offline:
                return "OFFLINE";
            case Online:
                return "ONLINE";
            default:
                return "APP";
        }
    }

    private String getOperationScenario(BookingInitRequestType bookingInitRequestType) {
        if (buildModifyServiceFeeOrder(bookingInitRequestType)) {
            return "ModifyOrder";
        }
        return "BookingOrder";
    }

    public static RoomInfoType buildRoomInfoType(ResourceToken resourceToken) {
        RoomResourceToken roomResourceToken = Optional.ofNullable(resourceToken)
            .map(ResourceToken::getRoomResourceToken).orElse(new RoomResourceToken());
        RoomInfoType roomInfoType = new RoomInfoType();
        roomInfoType.setRoomType(roomResourceToken.getRoomType());
        roomInfoType.setBalanceType(mappingBalanceType(roomResourceToken.getBalanceType()));
        roomInfoType.setAmadeusRoom(roomResourceToken.getAmadeus());
        roomInfoType.setOriginCurrency(roomResourceToken.getRoomOriginCurrency());
        roomInfoType.setTmcPrice(roomResourceToken.getTmcPrice());
        return roomInfoType;
    }

    // 现转预入参需做转换
    public static String mappingBalanceType(String balanceType) {
        return HotelBalanceTypeEnum.USEFG.getDesc().equals(balanceType) ? "USE_FG" : balanceType;
    }

    public static com.ctrip.corp.agg.hotel.expense.contract.model.HotelInfoType buildHotelInfoType(CheckAvailInfo checkAvailInfo
            , GetHotelDetailInfoResponseType getHotelDetailInfoResponseType) {
        com.ctrip.corp.agg.hotel.expense.contract.model.HotelInfoType hotelInfoType = new com.ctrip.corp.agg.hotel.expense.contract.model.HotelInfoType();
        HotelItem hotelItem = checkAvailInfo.getHotelItem();
        hotelInfoType.setCountryId(hotelItem.getCountry());
        hotelInfoType.setProvinceId(hotelItem.getProvince());
        hotelInfoType.setCityId(hotelItem.getCity());
        // 计算服务费所用的品牌信息 ----服务费计算之前获取酒店信息以便agg根据酒店集团做服务费免除
        HotelBrandEntity hotelBrandInfo = Optional.ofNullable(getHotelDetailInfoResponseType)
                .map(GetHotelDetailInfoResponseType::getHotelDetailInfo)
                .map(HotelDetailInfoEntity::getHotelBaseInfo)
                .map(DetailBaseInfoEntity::getHotelBrandInfo)
                .orElse(null);
        if (Objects.nonNull(hotelBrandInfo)){
            if (Objects.nonNull(hotelBrandInfo.getGroupId()) && hotelBrandInfo.getGroupId() > 0){
                hotelInfoType.setHotelGroupId(hotelBrandInfo.getGroupId());
            }
            if (Objects.nonNull(hotelBrandInfo.getBrandId()) && hotelBrandInfo.getBrandId() > 0){
                hotelInfoType.setHotelBrandId(hotelBrandInfo.getBrandId());
            }
        }
        return hotelInfoType;
    }

    private BaseInfoType getBaseInfoType(WrapperOfAccount.AccountInfo accountInfo, BookingInitRequestType bookingInitRequestType) {
        IntegrationSoaRequestType integrationSoaRequestType = bookingInitRequestType.getIntegrationSoaRequestType();
        BaseInfoType baseInfoType = new BaseInfoType();
        baseInfoType.setTraceId(integrationSoaRequestType.getRequestId());
        baseInfoType.setUid(RequestHeaderUtil.getUserId(integrationSoaRequestType));
        String policyUid = Optional.ofNullable(bookingInitRequestType.getPolicyInput())
            .map(PolicyInput::getPolicyUid).orElse(integrationSoaRequestType.getUserInfo().getUserId());
        if (StringUtil.isEmpty(policyUid)) {
            policyUid = RequestHeaderUtil.getUserId(integrationSoaRequestType);
        }
        baseInfoType.setPolicyUid(policyUid);
        baseInfoType.setCorpId(RequestHeaderUtil.getCorpId(integrationSoaRequestType));
        // NOTE:蓝色空间接入时修复
        baseInfoType.setPOS(
                HostUtil.mapToAccountPos(Optional.ofNullable(bookingInitRequestType.getIntegrationSoaRequestType())
                        .map(IntegrationSoaRequestType::getUserInfo).map(UserInfo::getPos).orElse(null)));
        baseInfoType.setCustomCurrency(accountInfo.getCurrency());
        baseInfoType.setTraceId(bookingInitRequestType.getIntegrationSoaRequestType().getRequestId());
        return baseInfoType;
    }

    /**
     * 是否走修改服务费
     *
     * @param bookingInitRequestType
     * @return
     */
    private boolean buildModifyServiceFeeOrder(BookingInitRequestType bookingInitRequestType) {
        return BookingInitUtil.buildModifyServiceFeeOrder(bookingInitRequestType);
    }

    private String buildServiceChargeToken(QueryOrderSettingsResponseType queryOrderSettingsResponseType, BookingInitRequestType bookingInitRequestType) {
        if (buildModifyServiceFeeOrder(bookingInitRequestType)) {
            return Optional.ofNullable(queryOrderSettingsResponseType)
                .map(QueryOrderSettingsResponseType::getServiceFeeSetting)
                .map(ServiceFeeSettingType::getServiceChargeToken).orElse(null);
        }
        return null;
    }

    private String buildAmsFeeConfigVersion(WrapperOfAccount.AccountInfo accountInfo,
        QueryHotelOrderDataResponseType queryHotelOrderDataResponseType,
        BookingInitRequestType bookingInitRequestType) {

        return buildModifyServiceFeeOrder(bookingInitRequestType) ?
            Optional.ofNullable(queryHotelOrderDataResponseType)
                .map(QueryHotelOrderDataResponseType::getHotelInfo)
                .map(com.ctrip.corp.order.data.aggregation.query.contract.HotelInfoType::getHotelOrder)
                .map(HotelOrderType::getServiceFeeConfig).map(ServiceFeeConfigType::getServiceFeeVersion).orElse(null)
            : accountInfo.getServiceFeeVersion();
    }

    @Override
    protected ParamCheckResult check(Tuple8<WrapperOfAccount.AccountInfo
        , QueryHotelOrderDataResponseType, QueryOrderSettingsResponseType
        , WrapperOfCheckAvail.CheckAvailInfo, BookingInitRequestType, ResourceToken
        , GetSupportedPaymentMethodResponseType,GetHotelDetailInfoResponseType> param) {
        return null;
    }
}