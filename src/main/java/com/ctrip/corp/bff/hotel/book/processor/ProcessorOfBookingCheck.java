package com.ctrip.corp.bff.hotel.book.processor;

import com.ctrip.corp.agg.hotel.checkbookinglimition.entity.CheckBookingLimitionRequestType;
import com.ctrip.corp.agg.hotel.checkbookinglimition.entity.CheckBookingLimitionResponseType;
import com.ctrip.corp.bff.framework.hotel.common.builder.SearchApprovalRequest;
import com.ctrip.corp.bff.framework.hotel.common.mapper.MapperOfGeneralSearchAccountInfoRequest;
import com.ctrip.corp.bff.framework.hotel.common.mapper.MapperOfGetCorpUserInfoRequest;
import com.ctrip.corp.bff.framework.hotel.common.mapper.MapperOfGetSubAccountConfigRequest;
import com.ctrip.corp.bff.framework.hotel.common.mapper.MapperOfPolicyGeneralSearchAccountInfoRequest;
import com.ctrip.corp.bff.framework.hotel.common.mapper.MapperOfSearchApprovalRequest;
import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.WaitFutureUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfSearchApproval;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.handler.WaitFuture;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import com.ctrip.corp.bff.hotel.book.common.enums.exception.BookingCheckErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.util.BookingCheckUtil;
import com.ctrip.corp.bff.hotel.book.handler.corpaccountqueryservice.HandlerOfGeneralSearchAccountInfo;
import com.ctrip.corp.bff.hotel.book.handler.corpagghotelsalestrategyservice.HandlerOfCheckBookingLimition;
import com.ctrip.corp.bff.hotel.book.handler.corpconfigurationservice.HandlerOfGetSubAccountConfig;
import com.ctrip.corp.bff.hotel.book.handler.corpuserinfoservice4j.HandlerOfGetCorpUserInfo;
import com.ctrip.corp.bff.hotel.book.handler.preapprovalservice.HandlerOfSearchApproval;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookingcheck.MapperOfApprovalCheckInfo;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookingcheck.MapperOfCheckBookingLimitionRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookingcheck.MapperOfCheckBookingLimitionResponseType;
import com.ctrip.corp.bff.hotel.book.qconfig.QConfigOfCustomizedSharkConfig;
import com.ctrip.corp.bff.hotel.book.service.BookingCheckRequestType;
import com.ctrip.corp.bff.hotel.book.service.BookingCheckResponseType;
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigRequestType;
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.SearchApprovalRequestType;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.SearchApprovalResponseType;
import com.dianping.cat.utils.StringUtils;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoRequestType;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoRequestType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024-11-07
 **/
@Component
public class ProcessorOfBookingCheck extends AbstractProcessor<BookingCheckRequestType, BookingCheckResponseType> {
    private static final String DOMESTIC = "DOMESTIC";

    private static final String OVERSEA = "OVERSEA";

    private static final String SELF = "self";

    @Autowired
    private MapperOfSearchApprovalRequest mapperOfSearchApprovalRequest;

    @Autowired
    private HandlerOfSearchApproval handlerOfSearchApproval;

    @Autowired
    private MapperOfApprovalCheckInfo mapperOfApprovalCheckInfo;
    @Autowired
    private HandlerOfGeneralSearchAccountInfo handlerOfGeneralSearchAccountInfo;
    @Autowired
    private MapperOfGeneralSearchAccountInfoRequest mapperOfAccountInfoRequest;
    @Autowired
    private HandlerOfGetCorpUserInfo handlerOfGetCorpUserInfo;
    @Autowired
    private MapperOfGetCorpUserInfoRequest mapperOfGetCorpUserInfoRequest;
    @Autowired
    private MapperOfPolicyGeneralSearchAccountInfoRequest mapperOfPolicyAccountInfoRequest;
    @Autowired
    private HandlerOfGetSubAccountConfig handlerOfGetSubAccountConfig;
    @Autowired
    private MapperOfGetSubAccountConfigRequest mapperOfGetSubAccountConfigRequest;
    @Autowired
    private MapperOfCheckBookingLimitionRequestType mapperOfCheckBookingLimitionRequestType;
    @Autowired
    private HandlerOfCheckBookingLimition handlerOfCheckBookingLimition;
    @Autowired
    private MapperOfCheckBookingLimitionResponseType mapperOfCheckBookingLimitionResponseType;
    @Autowired
    private QConfigOfCustomizedSharkConfig qConfigOfCustomizedSharkConfig;

    @Override
    public BookingCheckResponseType execute(BookingCheckRequestType requestType) throws Exception {
        BookingCheckResponseType bookingCheckResponseType = new BookingCheckResponseType();
        // 查询账户信息
        WaitFuture<GeneralSearchAccountInfoRequestType, GeneralSearchAccountInfoResponseType>
            accountAccountInfoResponseTypeWaitFuture = handlerOfGeneralSearchAccountInfo.handleAsync(
            mapperOfAccountInfoRequest.map(Tuple1.of(requestType.getIntegrationSoaRequestType())));
        // 查询用户信息
        WaitFuture<GetCorpUserInfoRequestType, GetCorpUserInfoResponseType> getCorpUserInfoResponseTypeWaitFuture =
            handlerOfGetCorpUserInfo.handleAsync(
                mapperOfGetCorpUserInfoRequest.map(Tuple1.of(requestType.getIntegrationSoaRequestType())));
        // 查询政策执行人账户信息
        WaitFuture<GeneralSearchAccountInfoRequestType, GeneralSearchAccountInfoResponseType>
            policyAccountInfoResponseTypeWaitFuture = null;
        WaitFuture<GetCorpUserInfoRequestType, GetCorpUserInfoResponseType> policyCorpUserInfoResponseTypeWaitFuture =
            null;
        if (StringUtil.isNotBlank(
            Optional.ofNullable(requestType.getPolicyInfo()).map(PolicyInput::getPolicyUid).orElse(null))) {
            // 查询政策执行人账户信息
            policyAccountInfoResponseTypeWaitFuture = handlerOfGeneralSearchAccountInfo.handleAsync(
                mapperOfPolicyAccountInfoRequest.map(
                    Tuple2.of(requestType.getIntegrationSoaRequestType(), requestType.getPolicyInfo())));
        }
        // 查询子账户信息
        WaitFuture<GetSubAccountConfigRequestType, GetSubAccountConfigResponseType>
            getSubAccountConfigResponseTypeWaitFuture = handlerOfGetSubAccountConfig.handleAsync(
            mapperOfGetSubAccountConfigRequest.map(Tuple1.of(getCorpUserInfoResponseTypeWaitFuture.get())));


        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
            .accountInfo(accountAccountInfoResponseTypeWaitFuture.get())
            .policyAccountInfo(WaitFutureUtil.safeGetFuture(policyAccountInfoResponseTypeWaitFuture))
            .corpUserInfo(WaitFutureUtil.safeGetFuture(getCorpUserInfoResponseTypeWaitFuture))
            .subAccountConfig(WaitFutureUtil.safeGetFuture(getSubAccountConfigResponseTypeWaitFuture))
            .build();

        // 资源管控校验
        WaitFuture<CheckBookingLimitionRequestType, CheckBookingLimitionResponseType>
            checkBookingLimitionResponseTypeWaitFuture = null;
        if (BookingCheckUtil.buildNeedCheckBookingLimition(accountInfo, requestType)) {
            checkBookingLimitionResponseTypeWaitFuture = handlerOfCheckBookingLimition.handleAsync(
                mapperOfCheckBookingLimitionRequestType.map(Tuple1.of(requestType)));
            mapperOfCheckBookingLimitionResponseType.map(Tuple3.of(checkBookingLimitionResponseTypeWaitFuture.get(),
                qConfigOfCustomizedSharkConfig.getCustomizedSharkConfigs(), requestType));
        }

        SearchApprovalRequest searchApprovalRequest = buildSearchApprovalRequest(requestType);
        if (searchApprovalRequest != null && BookingCheckUtil.buildNeedCheckApproval(accountInfo, requestType)) {
            WaitFuture<SearchApprovalRequestType, SearchApprovalResponseType> searchApprovalResponseTypeWaitFuture =
                    handlerOfSearchApproval.handleAsync(mapperOfSearchApprovalRequest.map(Tuple1.of(searchApprovalRequest)));
            WrapperOfSearchApproval.ApprovalInfo approvalInfo = WrapperOfSearchApproval.builder()
                .searchApprovalResponseType(searchApprovalResponseTypeWaitFuture.getWithoutError()).build();
            bookingCheckResponseType.setApprovalCheckInfo(mapperOfApprovalCheckInfo.map(
                Tuple3.of(requestType, searchApprovalResponseTypeWaitFuture.getWithoutError(), approvalInfo)));
        }
        return bookingCheckResponseType;
    }

    @Override
    public Map<String, String> tracking(BookingCheckRequestType requestType,
                                        BookingCheckResponseType responseType) {
        return null;
    }

    private Boolean buildOverseaFlag(Integer cityId) {
        if (cityId == null) {
            return false;
        }
        // 是否海外会员酒店城市
        if (CityInfoUtil.hkOrMacao(cityId)) {
            // 港澳台当做国内
            return false;
        }
        return CityInfoUtil.oversea(cityId);
    }

    /**
     * 校验审批单国内海外与城市国内海外是否匹配
     * @param approvalBookingType 是否国内审批单： DOMESTIC 国内； OVERSEA 国外
     * @param isOverSeaCity 是否海外城市
     */
    private void checkApprovalMatchCity(String approvalBookingType, boolean isOverSeaCity) {
        if (approvalBookingType == null) {
            return;
        }
        if (!DOMESTIC.equals(approvalBookingType) && !OVERSEA.equals(approvalBookingType)) {
            return;
        }
        boolean isOverSeaApproval = OVERSEA.equals(approvalBookingType);
        if (isOverSeaApproval != isOverSeaCity) {
            if (isOverSeaApproval) {
                throw BusinessExceptionBuilder.createAlertException(BookingCheckErrorEnum.CHECK_APPROVAL_OVERSEA_CITY_ERROR, "");
            } else {
                throw BusinessExceptionBuilder.createAlertException(BookingCheckErrorEnum.CHECK_APPROVAL_DOMESTIC_CITY_ERROR, "");
            }
        }
    }

    private SearchApprovalRequest buildSearchApprovalRequest(BookingCheckRequestType bookingCheckRequestType) {
        if (bookingCheckRequestType.getApprovalInput() == null || StringUtils.isEmpty(bookingCheckRequestType.getApprovalInput().getSubApprovalNo())) {
            return null;
        }
        ApprovalInput approvalInput = new ApprovalInput();
        String mainApprovalNo = bookingCheckRequestType.getApprovalInput().getMasterApprovalNo();
        String subApprovalNo = bookingCheckRequestType.getApprovalInput().getSubApprovalNo();
        approvalInput.setMasterApprovalNo(mainApprovalNo);
        approvalInput.setSubApprovalNo(subApprovalNo);
        return SearchApprovalRequest.builder()
                .approvalInput(approvalInput)
                .cityInput(bookingCheckRequestType.getCityInput())
                .integrationSoaRequestType(bookingCheckRequestType.getIntegrationSoaRequestType())
                .returnRegionControlCityInfo(true)
                .compatibleCitySplit(false)
                .build();
    }
}
