package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelGeoInfoResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.OrderResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.utils.ResourceTokenUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.*;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.bff.specific.contract.ApprovalDefaultRequestType;
import com.ctrip.corp.bff.specific.contract.BatchApprovalDefaultRequestType;
import com.ctrip.corp.bff.specific.contract.ExtRelationItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/6/26 20:37
 */
@Component
public class MapperOfBatchApprovalDefaultRequest extends AbstractMapper<Tuple2<BookingInitRequestType, WrapperOfAccount.AccountInfo>, BatchApprovalDefaultRequestType> {
    /**
     * 中国酒店
     */
    private static final String CN_HOTEL = "CN_HOTEL";
    /**
     * 海外酒店
     */
    private static final String INTERNATIONAL_HOTEL = "INTERNATIONAL_HOTEL";
    /**
     * 普通预定
     */
    private static final String NORMAL = "NORMAL";
    /**
     * 酒店修改
     */
    private static final String HOTEL_CHANGE = "HOTEL_CHANGE";

    private static final String PUBLIC = "public";
    private static final String PRIVATE = "private";

    /**
     * mice类型
     */
    private static final String MICE_ACTIVITY_ID_TYPE = "MICE_ACTIVITY_ID";

    @Override
    protected BatchApprovalDefaultRequestType convert(Tuple2<BookingInitRequestType, WrapperOfAccount.AccountInfo> param) {
        BookingInitRequestType bookInitRequestType = param.getT1();
        WrapperOfAccount.AccountInfo accountInfoUtil = param.getT2();

        BatchApprovalDefaultRequestType approvalDefaultRequestType = new BatchApprovalDefaultRequestType();
        approvalDefaultRequestType.setIntegrationSoaRequestType(bookInitRequestType.getIntegrationSoaRequestType());
        approvalDefaultRequestType.setScene(convertScene(accountInfoUtil));
        if (StringUtils.isEmpty(bookInitRequestType.getOrderId())) {
            approvalDefaultRequestType.setOrderIds(Collections.singletonList(bookInitRequestType.getOrderId()));
        }
        String subApprovalNo = Optional.ofNullable(bookInitRequestType.getApprovalInput()).map(ApprovalInput::getSubApprovalNo).orElse("");
        if (StringUtils.isNotEmpty(subApprovalNo)) {
            approvalDefaultRequestType.setSubApprovalNos(Collections.singletonList(subApprovalNo));
        }
        ResourceToken resourceToken = ResourceTokenUtil.tryParseToken(
                Optional.ofNullable(bookInitRequestType.getResourceTokenInfo()).map(ResourceTokenInfo::getResourceToken).orElse(null));
        approvalDefaultRequestType.setProductTypes(Collections.singletonList(convertProductType(buildCityId(resourceToken))));
        String orderId = buildOrderId(bookInitRequestType, resourceToken);
        if (orderId != null) {
            approvalDefaultRequestType.setOrderIds(Collections.singletonList(orderId));
        }
        approvalDefaultRequestType.setBookingType(convertBookingType(bookInitRequestType.getStrategyInfos()));
        if (bookInitRequestType.getSsoInput() != null) {
            approvalDefaultRequestType.setSsoInput(new SSOInput(bookInitRequestType.getSsoInput().getSsoKey()));
        }
        approvalDefaultRequestType.setCorpPayInfo(convertCorpPayInfo(bookInitRequestType.getCorpPayInfo()));
        String miceActivityId = Optional.ofNullable(bookInitRequestType.getMiceInput()).map(MiceInput::getMiceActivityId).orElse(null);
        if (!StringUtil.isEmpty(miceActivityId)) {
            ExtRelationItem extRelationInfo = new ExtRelationItem();
            extRelationInfo.setRelationId(miceActivityId);
            extRelationInfo.setType(MICE_ACTIVITY_ID_TYPE);
            approvalDefaultRequestType.setExtRelationItems(Collections.singletonList(extRelationInfo));
        }
        return approvalDefaultRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple2<BookingInitRequestType, WrapperOfAccount.AccountInfo> param) {
        return null;
    }

    private String convertScene(WrapperOfAccount.AccountInfo accountInfo) {
        if (Boolean.TRUE.equals(accountInfo.isOaApprovalHead())) {
            return CommonConstant.PRE_POSITION;
        } else {
            return CommonConstant.POST_POSITION;
        }
    }

    private String convertProductType(Integer cityId) {
        if (cityId == null) {
            return CN_HOTEL;
        }
        if (CityInfoUtil.oversea(cityId)) {
            return INTERNATIONAL_HOTEL;
        } else {
            return CN_HOTEL;
        }
    }

    private String convertBookingType(List<StrategyInfo> strategyInfos) {
        if (StrategyOfBookingInitUtil.modify(strategyInfos)) {
            return HOTEL_CHANGE;
        } else {
            return NORMAL;
        }
    }

    private CorpPayInfo convertCorpPayInfo(CorpPayInfo corpPayInfo) {
        String corpPayType = CorpPayInfoUtil.isPublic(corpPayInfo) ? PUBLIC : PRIVATE;
        CorpPayInfo request = new CorpPayInfo();
        request.setCorpPayType(corpPayType);
        return request;
    }

    /**
     * 构建订单号，目前有部分场景通过token带入订单号，有部分场景通过入参直接带入，所以需要兼容
     * @param bookingInitRequestType
     * @param resourceToken
     * @return
     */
    public String buildOrderId(BookingInitRequestType bookingInitRequestType, ResourceToken resourceToken) {
        // 资源token解析
        Long orderId = Optional.ofNullable(resourceToken).map(ResourceToken::getOrderResourceToken)
                .map(OrderResourceToken::getOrderId).orElse(null);
        if (orderId != null) {
            return String.valueOf(orderId);
        }
        return bookingInitRequestType.getOrderId();
    }

    public Integer buildCityId(ResourceToken resourceToken) {
        return Optional.ofNullable(resourceToken).map(ResourceToken::getHotelResourceToken)
                .map(HotelResourceToken::getHotelGeoInfoResourceToken).map(HotelGeoInfoResourceToken::getCityId).orElse(null);
    }
}
