package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.bff.framework.hotel.common.builder.GetTravelPolicyRequest;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.entity.contract.ApprovalOutput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelGeoInfoResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityInfoUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.*;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.BookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.bff.hotel.book.contract.TravelPolicyInfo;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ctrip.corp.bff.framework.hotel.common.enums.GetHotelTravelPolicyScenarioEnum.BASE_INFO_SCENARIO;
import static com.ctrip.corp.foundation.common.constant.StringConstants.T;

/**
 * @Author: chenchuang
 * @Date: 2024/10/22 20:20
 * @Description:
 * @Param: groupId, registerUid
 */
@Component
public class MapperOfBuildTravelPolicyRequestType extends AbstractMapper<Tuple6<BookingInitRequestType, ResourceToken,
    ApprovalInput, HotelPayTypeEnum, WrapperOfAccount.AccountInfo, List<HotelBookPassengerInput>>, GetTravelPolicyRequest> {

    @Override
    protected GetTravelPolicyRequest convert(Tuple6<BookingInitRequestType, ResourceToken, ApprovalInput,
        HotelPayTypeEnum, WrapperOfAccount.AccountInfo, List<HotelBookPassengerInput>> param) {
        BookingInitRequestType bookingInitRequest = param.getT1();
        ResourceToken resourceToken = param.getT2();
        ApprovalInput approvalInput = param.getT3();
        HotelPayTypeEnum selectedRoomPayType = param.getT4();
        WrapperOfAccount.AccountInfo accountInfo = param.getT5();
        List<HotelBookPassengerInput> hotelBookPassengerInputs = param.getT6();
        HotelGeoInfoResourceToken hotelGeoInfoResourceToken = Optional.ofNullable(resourceToken.getHotelResourceToken())
            .map(HotelResourceToken::getHotelGeoInfoResourceToken)
            .orElse(new HotelGeoInfoResourceToken());
        HotelResourceToken hotelResourceToken = resourceToken.getHotelResourceToken();

        List<HotelPassengerInput> passengerInputs = Optional.ofNullable(hotelBookPassengerInputs)
            .orElse(new ArrayList<>())
            .stream()
            .map(HotelBookPassengerInput::getHotelPassengerInput)
            .filter(Objects::nonNull).collect(Collectors.toList());
        GetTravelPolicyRequest getTravelPolicyRequest = new GetTravelPolicyRequest.Builder()
            .policyInput(bookingInitRequest.getPolicyInput())
            .integrationSoaRequestType(bookingInitRequest.getIntegrationSoaRequestType())
            .approvalInput(buildApprovalInput(approvalInput, accountInfo, bookingInitRequest, hotelGeoInfoResourceToken))
            .bookingWithPersonalAccount(StrategyOfBookingInitUtil.bookingWithPersonalAccount(bookingInitRequest.getStrategyInfos()))
            .cityInput(buildCityInput(hotelGeoInfoResourceToken.getCityId()))
            .corpPayInfo(bookingInitRequest.getCorpPayInfo())
            .hotelDateRangeInfo(Optional.ofNullable(bookingInitRequest.getHotelBookInput())
                .map(HotelBookInput::getHotelDateRangeInfo).orElse(new HotelDateRangeInfo()))
            .hotelPassengerInputs(passengerInputs)
            // todo:场景是否还需要修改
            .getHotelTravelPolicyScenarioEnum(BASE_INFO_SCENARIO)
            .paymentType(HotelPayTypeEnum.getCheckTravelPolicyPayType(selectedRoomPayType))
            .locationId(buildLocationId(hotelGeoInfoResourceToken))
            .masterHotelId(Optional.ofNullable(hotelResourceToken).map(HotelResourceToken::getMasterHotelId).orElse(null))
            .useMealStandard(T.equals(Optional.ofNullable(bookingInitRequest.getTravelPolicyInfo())
                .map(TravelPolicyInfo::getSupportMealStandard).orElse(null)))
            .build();
        return getTravelPolicyRequest;
    }

    protected Integer buildLocationId(HotelGeoInfoResourceToken hotelGeoInfoResourceToken) {
        if (hotelGeoInfoResourceToken == null) {
            return null;
        }
        Integer locationId = hotelGeoInfoResourceToken.getLocationId();
        if (TemplateNumberUtil.isNotZeroAndNull(locationId)) {
            return locationId;
        }
        return null;
    }

    protected ApprovalInput buildApprovalInput(ApprovalInput approvalInput, WrapperOfAccount.AccountInfo accountInfo,
        BookingInitRequestType bookingInitRequestType, HotelGeoInfoResourceToken hotelGeoInfoResourceToken) {
        if (!StrategyOfBookingInitUtil.hotelCheckAvail(bookingInitRequestType.getStrategyInfos())) {
            return approvalInput;
        }
        // 对比降噪 逻辑与详情页可定传参一致 非单据模式，不需要审批单 提前审批前置场景,直接使用入参中的审批单
        if (accountInfo.isOaApprovalHead() && accountInfo.isPreApprovalRequired(
            CityInfoUtil.oversea(hotelGeoInfoResourceToken.getCityId()), bookingInitRequestType.getCorpPayInfo())) {
            return approvalInput;
        }
        // 非前置场景 首页，酒店列表页，房型列表页无需审批单
        return null;
    }


    public CityInput buildCityInput(Integer cityId) {
        CityInput cityInput = new CityInput();
        cityInput.setCityId(cityId);
        return cityInput;
    }


    @Override
    protected ParamCheckResult check(Tuple6<BookingInitRequestType, ResourceToken, ApprovalInput, HotelPayTypeEnum,
        WrapperOfAccount.AccountInfo, List<HotelBookPassengerInput>> bookingInitRequestTypeResourceTokenTuple2) {
        return null;
    }
}
