package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount.AccountInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelGeoInfoResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple6;
import com.ctrip.corp.bff.hotel.book.common.constant.InvoiceConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail.CheckAvailInfo;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import corp.settlement.service.invoice.corpinvoice.CorpOrderInvoiceDetailInfoQueryRequest;
import corp.settlement.service.invoice.corpinvoice.HotelBasicInfo;
import corp.settlement.service.invoice.corpinvoice.HotelFeeInfo;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/2/17 14:08
 */
@Component public class MapperOfCorpOrderInvoiceDetailInfoQueryRequest extends
    AbstractMapper<Tuple6<BookingInitRequestType, ResourceToken, AccountInfo, CheckAvailInfo, HotelPayTypeEnum, HotelPayTypeEnum>, CorpOrderInvoiceDetailInfoQueryRequest> {
    private static final String OVERSEA = "I";
    private static final String DOMESTIC = "N";
    private static final String PUBLIC = "Pub";
    private static final String OWN = "Own";
    private static final int HOTEL = 2;

    @Override protected CorpOrderInvoiceDetailInfoQueryRequest convert(
        Tuple6<BookingInitRequestType, ResourceToken, AccountInfo, CheckAvailInfo, HotelPayTypeEnum, HotelPayTypeEnum> tuple) {
        CorpOrderInvoiceDetailInfoQueryRequest corpOrderInvoiceDetailInfoQueryRequest =
            new CorpOrderInvoiceDetailInfoQueryRequest();
        BookingInitRequestType bookingInitRequestType = tuple.getT1();
        ResourceToken resourceToken = tuple.getT2();
        WrapperOfAccount.AccountInfo accountInfo = tuple.getT3();
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = tuple.getT4();
        HotelPayTypeEnum roomPayType = tuple.getT5();
        HotelPayTypeEnum servicePayType = tuple.getT6();
        HotelBasicInfo hotelBasicInfo = new HotelBasicInfo();
        hotelBasicInfo.setCorpId(bookingInitRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId());
        hotelBasicInfo.setBookUid(bookingInitRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId());
        hotelBasicInfo.setPolicyUid(buildPolicyUid(accountInfo, bookingInitRequestType));
        hotelBasicInfo.setAccountId(
            StringUtil.isNotBlank(accountInfo.getAccountId()) ? Long.parseLong(accountInfo.getAccountId()) : null);
        hotelBasicInfo.setHotelType(checkAvailInfo.getRoomType());
        hotelBasicInfo.setHotelClass(CityInfoUtil.oversea(
            Optional.ofNullable(resourceToken).map(ResourceToken::getHotelResourceToken)
                .map(HotelResourceToken::getHotelGeoInfoResourceToken).map(HotelGeoInfoResourceToken::getCityId)
                .orElse(null)) ? OVERSEA : DOMESTIC);
        hotelBasicInfo.setCorpPayType(CorpPayInfoUtil.isPublic(bookingInitRequestType.getCorpPayInfo()) ? PUBLIC : OWN);
        hotelBasicInfo.setHotelFeeInfoList(buildHotelFeeInfoList(roomPayType, servicePayType, bookingInitRequestType));
        hotelBasicInfo.setTransactionId(checkAvailInfo.getWsId());
        corpOrderInvoiceDetailInfoQueryRequest.setHotelBasicInfo(hotelBasicInfo);
        corpOrderInvoiceDetailInfoQueryRequest.setProductLine(HOTEL);
        return corpOrderInvoiceDetailInfoQueryRequest;
    }

    @Override protected ParamCheckResult check(
        Tuple6<BookingInitRequestType, ResourceToken, AccountInfo, CheckAvailInfo, HotelPayTypeEnum, HotelPayTypeEnum> tuple) {
        return null;
    }

    private String buildPolicyUid(WrapperOfAccount.AccountInfo accountInfo,
        BookingInitRequestType bookingInitRequestType) {
        if (accountInfo.isPolicyModel()) {
            if (StringUtil.isNotBlank(
                Optional.ofNullable(bookingInitRequestType.getPolicyInput()).map(PolicyInput::getPolicyUid)
                    .orElse(null))) {
                return Optional.ofNullable(bookingInitRequestType.getPolicyInput()).map(PolicyInput::getPolicyUid)
                    .orElse(null);
            }
            return bookingInitRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId();
        }
        return null;
    }

    private List<HotelFeeInfo> buildHotelFeeInfoList(HotelPayTypeEnum roomPayType, HotelPayTypeEnum servicePayType,
        BookingInitRequestType bookingInitRequestType) {
        List<HotelFeeInfo> hotelFeeInfoList = new ArrayList<>();
        HotelFeeInfo hotelFeeInfo = buildHotelFeeInfo(roomPayType);
        if (hotelFeeInfo != null) {
            hotelFeeInfoList.add(hotelFeeInfo);
        }
        HotelFeeInfo serviceFeeInfo = buildServiceFeeInfo(servicePayType);
        if (serviceFeeInfo != null) {
            hotelFeeInfoList.add(serviceFeeInfo);
        }
        HotelFeeInfo insuranceFeeInfo = buildInsuranceFeeInfo(roomPayType, bookingInitRequestType);
        if (insuranceFeeInfo != null) {
            hotelFeeInfoList.add(insuranceFeeInfo);
        }
        return hotelFeeInfoList;
    }

    private HotelFeeInfo buildInsuranceFeeInfo(HotelPayTypeEnum roomPayType,
        BookingInitRequestType bookingInitRequestType) {
        HotelFeeInfo serviceFeeInfo = new HotelFeeInfo();
        serviceFeeInfo.setFeeType(InvoiceConstant.INSURANCE_FEE);
        if (bookingInitRequestType.getHotelInsuranceInput() == null) {
            return null;
        }
        if (CollectionUtil.isEmpty(bookingInitRequestType.getHotelInsuranceInput().getHotelInsuranceDetailInputs())) {
            return null;
        }
        if (roomPayType.isCorpPay() || roomPayType.isMixPay()) {
            serviceFeeInfo.setTransactionFlag(InvoiceConstant.ACCNT);
            return serviceFeeInfo;
        }
        serviceFeeInfo.setTransactionFlag(InvoiceConstant.PERSONAL);
        return serviceFeeInfo;
    }

    private HotelFeeInfo buildServiceFeeInfo(HotelPayTypeEnum servicePayType) {
        HotelFeeInfo serviceFeeInfo = new HotelFeeInfo();
        serviceFeeInfo.setFeeType(InvoiceConstant.SERVICE_FEE);
        if (servicePayType == HotelPayTypeEnum.CORP_PAY) {
            serviceFeeInfo.setTransactionFlag(InvoiceConstant.ACCNT);
            return serviceFeeInfo;
        }
        if (servicePayType == HotelPayTypeEnum.SELF_PAY) {
            serviceFeeInfo.setTransactionFlag(InvoiceConstant.PERSONAL);
            return serviceFeeInfo;
        }
        return null;
    }

    private HotelFeeInfo buildHotelFeeInfo(HotelPayTypeEnum roomPayType) {
        if (Arrays.asList(HotelPayTypeEnum.GUARANTEE_SELF_PAY, HotelPayTypeEnum.GUARANTEE_CORP_PAY,
            HotelPayTypeEnum.CASH).contains(roomPayType)) {
            return null;
        }
        HotelFeeInfo roomFeeInfo = new HotelFeeInfo();
        roomFeeInfo.setFeeType(InvoiceConstant.HOTEL_FEE);
        if (roomPayType == HotelPayTypeEnum.PRBAL) {
            roomFeeInfo.setTransactionFlag(HotelPayTypeEnum.PRBAL.getCode());
            return roomFeeInfo;
        }
        if (roomPayType.isCorpPay()) {
            roomFeeInfo.setTransactionFlag(InvoiceConstant.ACCNT);
            return roomFeeInfo;
        }
        if (roomPayType.isMixPay()) {
            roomFeeInfo.setTransactionFlag(InvoiceConstant.MIX);
            return roomFeeInfo;
        }
        if (roomPayType.isSelfPay()) {
            roomFeeInfo.setTransactionFlag(InvoiceConstant.PERSONAL);
            return roomFeeInfo;
        }
        return null;
    }
}
