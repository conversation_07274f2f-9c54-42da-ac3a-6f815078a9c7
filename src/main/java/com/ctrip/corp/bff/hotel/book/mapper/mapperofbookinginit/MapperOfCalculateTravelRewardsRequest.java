package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.agg.hotel.expense.contract.model.BaseChargeAmount;
import com.ctrip.corp.agg.hotel.expense.contract.model.ChargeAmountInfoType;
import com.ctrip.corp.agg.hotel.expense.contract.model.ServiceChargePriceType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomItem;
import com.ctrip.corp.agg.hotel.salestrategy.entity.*;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfHotelTravelPolicy;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.CorpXProductInfoToken;
import com.ctrip.corp.bff.framework.template.common.utils.HostUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple8;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.BookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.util.CorpHotelBookCommonWSUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodResponseType;
import com.ctrip.model.CalculateServiceChargeV2ResponseType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 个人账户，奖励计算
 * @Date 2024/8/22 10:02
 * @Version 1.0
 */
@Component
public class MapperOfCalculateTravelRewardsRequest extends MapperOfBookingInitCommonInfo<Tuple8<BookingInitRequestType,
        WrapperOfCheckAvail.CheckAvailInfo,
        WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo,
        CalculateServiceChargeV2ResponseType,
        WrapperOfAccount.AccountInfo,
        GetSupportedPaymentMethodResponseType,
    HotelPayTypeEnum, List<HotelBookPassengerInput>>, CalculateTravelRewardsRequestType> {

    @Override
    protected CalculateTravelRewardsRequestType convert(Tuple8<BookingInitRequestType,
                WrapperOfCheckAvail.CheckAvailInfo,
                WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo,
                CalculateServiceChargeV2ResponseType,
                WrapperOfAccount.AccountInfo,
                GetSupportedPaymentMethodResponseType,
            HotelPayTypeEnum, List<HotelBookPassengerInput>> param) {

        CalculateTravelRewardsRequestType request = new CalculateTravelRewardsRequestType();
        BookingInitRequestType bookingInitRequest = param.getT1();
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = param.getT2();
        WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo hotelTravelPolicyInfo = param.getT3();
        CalculateServiceChargeV2ResponseType calculateServiceChargeV2Response = param.getT4();
        WrapperOfAccount.AccountInfo accountInfo = param.getT5();
        HotelPayTypeEnum selectedRoomPayType = param.getT7();
        GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponseType = param.getT6();
        List<HotelBookPassengerInput> hotelBookPassengerInputs = param.getT8();

        BigDecimal customExchange = getCustomExchange(checkAvailInfo);

        HotelPayTypeEnum servicePayTypeEnum = BookingInitUtil.getServicePayType(bookingInitRequest.getHotelPayTypeInput(),
            selectedRoomPayType
                , getSupportedPaymentMethodResponseType, null,
                accountInfo, calculateServiceChargeV2Response);
        HotelPayTypeEnum roomPayTypeEnum =
                BookingInitUtil.getRoomPayType(servicePayTypeEnum, getSupportedPaymentMethodResponseType, null,
                        accountInfo);

        request.setRequestBaseInfo(getRequestBaseInfoType(bookingInitRequest, accountInfo.getCurrency(), customExchange));
        request.setCheckAvailId(checkAvailInfo.getWsId());
        request.setCouponAmount(getCouponAmount(checkAvailInfo));
        request.setPolicyToken(Optional.ofNullable(hotelTravelPolicyInfo)
                .map(WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo::getPolicyToken).orElse(null));
        request.setAddPriceAmount(getAddPriceAmount(bookingInitRequest, checkAvailInfo, accountInfo.getCurrency()));
        request.setCorpXProductList(buildCorpXProductInfoType(bookingInitRequest));
        request.setServiceChargeAmount(buildServiceAmount(calculateServiceChargeV2Response, servicePayTypeEnum,
                roomPayTypeEnum, accountInfo.getCurrency()));
        request.setGuestInfoList(getGuestIngetfoList(bookingInitRequest, hotelBookPassengerInputs));
        return request;
    }

    /**
     * 服务费计算
     *
     * @param responseType
     * @param servicePayType
     * @param roomPayType
     * @param customCurrency
     * @return
     */
    public static PriceType buildServiceAmount(CalculateServiceChargeV2ResponseType responseType,
                                               HotelPayTypeEnum servicePayType, HotelPayTypeEnum roomPayType, String customCurrency) {
        ChargeAmountInfoType infoType = BookingInitUtil.getChargeAmountInfoType(responseType, servicePayType, roomPayType);
        ServiceChargePriceType serviceChargePriceType = Optional.ofNullable(infoType).map(ChargeAmountInfoType::getChargeAmountPack)
                .map(BaseChargeAmount::getChargeAmountCustomCurrency).orElse(null);
        if (serviceChargePriceType == null || serviceChargePriceType.getAmount() == null) {
            return null;
        }
        BigDecimal price = Optional.ofNullable(serviceChargePriceType).map(ServiceChargePriceType::getAmount).orElse(new BigDecimal(0));
        return new PriceType(price, customCurrency);
    }


    public static List<CorpXProductInfoType> buildCorpXProductInfoType(BookingInitRequestType bookingInitRequestType) {
        if (bookingInitRequestType.getHotelInsuranceInput() == null
                || CollectionUtil.isEmpty(bookingInitRequestType.getHotelInsuranceInput().getHotelInsuranceDetailInputs())) {
            return null;
        }
        List<CorpXProductInfoType> corpXProductInfoTypeList = new ArrayList<>();
        bookingInitRequestType.getHotelInsuranceInput().getHotelInsuranceDetailInputs().forEach(
                hotelInsuranceDetailInput -> {
                    if (CollectionUtil.isEmpty(hotelInsuranceDetailInput.getInsuranceHotelBookPassengerInputs())) {
                        return;
                    }
                    CorpXProductInfoToken insuranceToken =
                        TokenParseUtil.parseToken(hotelInsuranceDetailInput.getInsuranceToken(),
                            CorpXProductInfoToken.class);
                    CorpXProductInfoType corpXProductInfoType = new CorpXProductInfoType();
                    corpXProductInfoType.setPriceMark(
                        Optional.ofNullable(insuranceToken).map(CorpXProductInfoToken::getPriceMark).orElse(null));
                    corpXProductInfoType.setOwnerType("PERSON");
                    List<com.ctrip.corp.agg.hotel.salestrategy.entity.CorpXGuestInfoType> corpXGuestInfoTypes = new ArrayList<>();
                    hotelInsuranceDetailInput.getInsuranceHotelBookPassengerInputs().forEach(
                            insuranceHotelBookPassengerInput -> {
                                HotelPassengerInput hotelPassengerInput = insuranceHotelBookPassengerInput.getHotelPassengerInput();
                                CorpXGuestInfoType corpXGuestInfoType = new CorpXGuestInfoType();
                                corpXGuestInfoType.setUid(hotelPassengerInput.getUid());
                                corpXGuestInfoType.setEmployee("T".equalsIgnoreCase(hotelPassengerInput.getEmployee()));
                                corpXGuestInfoType.setRoomIndex(hotelPassengerInput.getRoomIndex());
                                corpXGuestInfoTypes.add(corpXGuestInfoType);
                            }
                    );
                    corpXProductInfoType.setGuestList(corpXGuestInfoTypes);
                    corpXProductInfoTypeList.add(corpXProductInfoType);
                }
        );
        return corpXProductInfoTypeList;
    }

    private RequestBaseInfoType getRequestBaseInfoType(BookingInitRequestType request, String customCurrency,
                                                       BigDecimal customExchange) {

        IntegrationSoaRequestType soaRequestType = request.getIntegrationSoaRequestType();
        String policyUid = getPolicyUid(request.getPolicyInput(), request.getIntegrationSoaRequestType().getUserInfo());
        UserInfo userInfo = soaRequestType.getUserInfo();

        RequestBaseInfoType requestBaseInfoType = new RequestBaseInfoType();
        requestBaseInfoType.setUid(userInfo.getUserId());
        requestBaseInfoType.setCorpPayType(getTravelCorpPayType(request.getCorpPayInfo()));
        requestBaseInfoType.setBookingChannel(CorpHotelBookCommonWSUtil.getChannel(soaRequestType.getSourceFrom()));
        requestBaseInfoType.setCorpId(userInfo.getCorpId());
        requestBaseInfoType.setPolicyUid(policyUid);
        requestBaseInfoType.setCustomCurrency(customCurrency);
        requestBaseInfoType.setCustomExchange(customExchange);
        requestBaseInfoType.setLocale(soaRequestType.getLanguage());
        requestBaseInfoType.setPos(HostUtil.mapToAccountPos(userInfo.getPos()));
        requestBaseInfoType.setTraceId(soaRequestType.getRequestId());
        return requestBaseInfoType;
    }

    private String getTravelCorpPayType(CorpPayInfo corpPayInfo) {
        if (CorpPayInfoUtil.isPublic(corpPayInfo)) {
            return PUBLIC;
        } else {
            return PRIVATE;
        }
    }

    @Override
    protected ParamCheckResult check(Tuple8<BookingInitRequestType,
            WrapperOfCheckAvail.CheckAvailInfo,
            WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo,
            CalculateServiceChargeV2ResponseType,
            WrapperOfAccount.AccountInfo,
            GetSupportedPaymentMethodResponseType,
        HotelPayTypeEnum, List<HotelBookPassengerInput>> param) {
        return null;
    }


    private List<GuestInfoType> getGuestIngetfoList(BookingInitRequestType request, List<HotelBookPassengerInput> hotelBookPassengerInputs) {
        if (CollectionUtils.isEmpty(hotelBookPassengerInputs)) {
            return null;
        }
        return hotelBookPassengerInputs.stream().filter(Objects::nonNull)
                .filter(z -> z.getHotelPassengerInput() != null)
                .map(t -> {
            GuestInfoType guestInfoType = new GuestInfoType();
                    guestInfoType.setUid(StringUtil.isNotBlank(t.getHotelPassengerInput().getUid())
                            ? t.getHotelPassengerInput().getUid()
                            : t.getHotelPassengerInput().getInfoId());
            guestInfoType.setEmployee("T".equals(t.getHotelPassengerInput().getEmployee()));
            return guestInfoType;
        }).collect(Collectors.toList());
    }

    private BigDecimal getCustomExchange(WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo) {
        RoomItem roomItem = checkAvailInfo.getRoomItem();
        if (roomItem == null) {
            return null;
        }
        return roomItem.getCustomExchange();
    }
}
