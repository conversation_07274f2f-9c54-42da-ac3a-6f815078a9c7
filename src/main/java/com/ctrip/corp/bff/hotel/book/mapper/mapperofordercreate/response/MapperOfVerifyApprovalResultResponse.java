package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.ApprovalBillChecklistType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.BillAndDockingMergedResultType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.BillDetailInfoType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckItemType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckResultExtType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.HotelTripConflictType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.VerifyApprovalBillResultType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.VerifyDockingResultType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.VerifyPassengerDetailType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.VerifyPassengerResultType;
import com.ctrip.corp.agg.hotel.tmc.entity.FieldInfoType;
import com.ctrip.corp.agg.hotel.tmc.entity.FieldValueDetailType;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig;
import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.TimeUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfCityBaseInfo;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfCityBaseInfo.CityBaseInfo;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum;
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil;
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.shark.DateDisplayUtil;
import com.ctrip.corp.bff.framework.template.common.shark.currencytemplate.CurrencyDisplayUtil;
import com.ctrip.corp.bff.framework.template.common.shark.entity.CurrencyDisplayInfo;
import com.ctrip.corp.bff.framework.template.common.shark.entity.CurrencyProductLineEnum;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.CalendarUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.common.utils.date.DateUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.AmountInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.*;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.CustomConfigKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.SharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.SoaErrorSharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.ControlElementEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.RoomTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.CurrencyUtil;
import com.ctrip.corp.bff.hotel.book.common.util.MathUtils;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfVerifyApproval;
import com.ctrip.corp.bff.hotel.book.contract.ApprovalExtend;
import com.ctrip.corp.bff.hotel.book.contract.ApprovalInfo;
import com.ctrip.corp.bff.hotel.book.contract.SimpleApprovalOutput;
import com.ctrip.corp.bff.hotel.book.contract.BillExtendInfo;
import com.ctrip.corp.bff.hotel.book.contract.CheckItem;
import com.ctrip.corp.bff.hotel.book.contract.ElementDetail;
import com.ctrip.corp.bff.hotel.book.contract.ElementInfo;
import com.ctrip.corp.bff.hotel.book.contract.HotelTripConflictDetail;
import com.ctrip.corp.bff.hotel.book.contract.HotelTripConflictInfo;
import com.ctrip.corp.bff.hotel.book.contract.InputExtend;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType;
import com.ctrip.corp.bff.hotel.book.contract.VerifyApprovalInfo;
import com.ctrip.corp.bff.hotel.book.contract.VerifyApprovalPassengerInfo;
import com.ctrip.corp.bff.hotel.book.contract.VerifyDockingInfo;
import com.ctrip.corp.bff.hotel.book.contract.VerifyPassengerInfo;
import com.ctrip.corp.bff.hotel.book.contract.VerifyResult;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.ProductTypeEnum;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.ApprovalBaseInfo;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.ApprovalRankInfo;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.CompatibleCity;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.CountryInfo;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.HotelApprovalInfo;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.HotelDetail;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.HotelExtInfo;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.HotelProductInfo;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.LocationInfo;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.PassengerInfo;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.SearchApprovalResponseType;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.SearchApprovalResult;
import com.ctrip.corp.foundation.translation.currency.util.HotelCurrencyDisplayUtil;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType;
import com.ctrip.ibu.platform.shark.sdk.api.L10n;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 提前审批管控结果
 * @Date 2024/7/13 12:13
 * @Version 1.0
 */
@Component public class MapperOfVerifyApprovalResultResponse
    extends AbstractMapper<Tuple1<WrapperOfVerifyApproval>, Tuple2<Boolean, OrderCreateResponseType>> {
    public static final String CONTROL_BY_PERSON = "P";
    public static final String FAIL = "FAIL";
    public static final String UNNECESSARY = "UNNECESSARY";
    public static final String PASS = "PASS";
    public static final String EMERGENCY = "EMERGENCY";
    public static final String TRUE = "T";

    /**
     * 出行人字符串转List分割符
     */
    public static final String ORDER_CHECK_TRAVEL_SPLIT_MARK = ",";
    /**
     * 展示连接符
     */
    public static final String ORDER_CHECK_TRAVEL_JOIN_MARK = "、";
    /**
     * 星级字符串转List分割符
     */
    public static final String ORDER_CHECK_STAR_SPLIT_MARK = "-";

    public static final String APPROVA_BYORDER = "APPROVA_BYORDER";
    public static final String APPROVAL_BYPSG = "APPROVAL_BYPSG";
    public static final String DOCKING = "DOCKING";

    // 行程冲突code：ORDER_ID：订单号 GUEST_NAME：入住人 CHECK_DATE：入离日期 HOTEL_NAME：入住酒店 CITY_NAME：入住城市
    public static final String ORDER_ID = "ORDER_ID";
    public static final String GUEST_NAME = "GUEST_NAME";
    public static final String CHECK_DATE = "CHECK_DATE";
    public static final String HOTEL_NAME = "HOTEL_NAME";
    public static final String CITY_NAME = "CITY_NAME";

    private static final int STAR_ARRAY_LENGTH = 2;

    private final List<String> invalidDateList = Lists.newArrayList("2000-01-01", "2099-12-31", "0001-01-01");
    // 表示数据的区间关系
    private static final String BETWEEN = "BETWEEN";
    public static final String CHECK_RESULT_ROW_TYPE_ROOM_NIGHT_PRICE_TIP =
        "trip.biz.hotel.booking.biz.text.approval.check.result.row.type_room_night_price_tip";
    // 价格相关的类型，需要和币种拼在一起展示
    private static final List<ControlElementEnum> PRICE_LIST =
        Lists.newArrayList(ControlElementEnum.PRICE, ControlElementEnum.BUDGET_AMOUNT,
            ControlElementEnum.ROOM_NIGHT_PRICE, ControlElementEnum.AVERAGE_PRICE);


    @Override protected Tuple2<Boolean, OrderCreateResponseType> convert(Tuple1<WrapperOfVerifyApproval> tuple) {
        Tuple2<Boolean, OrderCreateResponseType> tupleResult = Tuple2.of(false, null);
        WrapperOfVerifyApproval wrapperOfVerifyApproval = tuple.getT1();
        CheckTravelPolicyResponseType checkTravelPolicyResponseType =
            wrapperOfVerifyApproval.getCheckTravelPolicyResponseType();
        WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo = wrapperOfVerifyApproval.getCityBaseInfo();
        WrapperOfAccount.AccountInfo accountInfo = wrapperOfVerifyApproval.getAccountInfo();
        OrderCreateToken orderCreateToken = wrapperOfVerifyApproval.getOrderCreateToken();
        OrderCreateRequestType orderCreateRequestType = wrapperOfVerifyApproval.getOrderCreateRequestType();
        SearchApprovalResponseType searchApprovalResponseType = wrapperOfVerifyApproval.getSearchApprovalResponseType();
        Map<String, StrategyInfo> strategyInfoMap = wrapperOfVerifyApproval.getStrategyInfoMap();
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo =
            wrapperOfVerifyApproval.getCheckAvailContextInfo();
        Map<String, HotelApprovalInfo> hotelApprovalInfoMap =
            buildHotelApprovalInfoMap(orderCreateRequestType, searchApprovalResponseType);
        OrderCreateResponseType orderCreateResponseType = new OrderCreateResponseType();
        VerifyApprovalInfo verifyApprovalInfo;
        if (orderCreateToken.containsContinueType(ContinueTypeConst.CONTROL_BOOKING)) {
            return tupleResult;
        }
        if (CONTROL_BY_PERSON.equalsIgnoreCase(checkTravelPolicyResponseType.getApprovalBillControlDimension())) {
            // 按人管控
            verifyApprovalInfo =
                buildVerifyApprovalInfoByPassenger(checkTravelPolicyResponseType, orderCreateRequestType,
                    hotelApprovalInfoMap, cityBaseInfo, accountInfo, checkAvailContextInfo);
        } else if (checkTravelPolicyResponseType.getVerifyDockingResult() != null) {
            // 下单管控/下单管控+按单管控
            verifyApprovalInfo = buildVerifyApprovalInfoByDocking(checkTravelPolicyResponseType, orderCreateRequestType,
                hotelApprovalInfoMap, cityBaseInfo, accountInfo, checkAvailContextInfo);
        } else {
            // 按单管控
            verifyApprovalInfo = buildVerifyApprovalInfoByOrder(checkTravelPolicyResponseType, orderCreateRequestType,
                hotelApprovalInfoMap, cityBaseInfo, accountInfo, checkAvailContextInfo, strategyInfoMap);
        }
        if (verifyApprovalInfo == null) {
            return tupleResult;
        }
        orderCreateResponseType.setVerifyApprovalInfo(verifyApprovalInfo);
        orderCreateToken.addContinueTypes(ContinueTypeConst.CONTROL_BOOKING);
        orderCreateResponseType.setOrderCreateToken(
            TokenParseUtil.generateToken(orderCreateToken, OrderCreateToken.class));
        return Tuple2.of(true, orderCreateResponseType);
    }

    @Override protected ParamCheckResult check(Tuple1<WrapperOfVerifyApproval> tuple) {
        WrapperOfVerifyApproval wrapperOfVerifyApproval = tuple.getT1();
        CheckTravelPolicyResponseType checkTravelPolicyResponseType =
            wrapperOfVerifyApproval.getCheckTravelPolicyResponseType();
        OrderCreateRequestType orderCreateRequestType = wrapperOfVerifyApproval.getOrderCreateRequestType();
        SearchApprovalResponseType searchApprovalResponseType = wrapperOfVerifyApproval.getSearchApprovalResponseType();
        int logErrorCode =
            Optional.ofNullable(checkTravelPolicyResponseType).map(CheckTravelPolicyResponseType::getResponseCode)
                .orElse(OrderCreateErrorEnum.CHECK_TRAVEL_POLICY_RESPONSE_IS_ERROR.getErrorCode());
        if (logErrorCode != CommonConstant.SUCCESS_20000) {
            String friendlyMessage = SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessage(
                SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_AGG_HOTEL_TMC_SERVICE,
                SoaErrorSharkKeyConstant.ACTION_NAME_CHECK_TRAVEL_POLICY, String.valueOf(logErrorCode));
            return new ParamCheckResult(false,
                OrderCreateErrorEnum.CHECK_TRAVEL_POLICY_RESPONSE_IS_ERROR.getErrorCode(), String.valueOf(logErrorCode),
                Optional.ofNullable(checkTravelPolicyResponseType).map(CheckTravelPolicyResponseType::getResponseDesc)
                    .orElse(null), friendlyMessage);
        }
        Map<String, HotelApprovalInfo> hotelApprovalInfoMap =
            buildHotelApprovalInfoMap(orderCreateRequestType, searchApprovalResponseType);
        // 按人管控接口出参校验
        if (CONTROL_BY_PERSON.equalsIgnoreCase(checkTravelPolicyResponseType.getApprovalBillControlDimension())) {
            if (checkTravelPolicyResponseType.getVerifyPassengerResult() == null) {
                return buildParamCheckResultErrorByPassenger(checkTravelPolicyResponseType);
            }
            if (CollectionUtil.isEmpty(
                checkTravelPolicyResponseType.getVerifyPassengerResult().getVerifyPassengerDetailList())) {
                return buildParamCheckResultErrorByPassenger(checkTravelPolicyResponseType);
            }
            // 管控失败的出行人 但缺失管控失败元素信息
            if (checkTravelPolicyResponseType.getVerifyPassengerResult().getVerifyPassengerDetailList().stream()
                .anyMatch(verifyPassengerDetailType -> !checkVerifyPassengerDetailType(verifyPassengerDetailType,
                    hotelApprovalInfoMap))) {
                return buildParamCheckResultErrorByPassenger(checkTravelPolicyResponseType);
            }
        }
        // 按单管控+下单管控/纯下单管控 接口出参校验
        if (!CONTROL_BY_PERSON.equalsIgnoreCase(checkTravelPolicyResponseType.getApprovalBillControlDimension())
            && checkTravelPolicyResponseType.getVerifyDockingResult() != null) {
            if (checkTravelPolicyResponseType.getCheckResultExt() == null
                || checkTravelPolicyResponseType.getCheckResultExt().getBillAndDockingMergedResult() == null) {
                return new ParamCheckResult(false, OrderCreateErrorEnum.CHECK_TRAVEL_POLICY_RESPONSE_IS_ERROR);
            }
            // 提前审批按单管控需要校验审批单详情是否存在、管控不通过接口返回的不通过元素列表不为空
            if (checkTravelPolicyResponseType.getVerifyApprovalBillResult() != null) {
                ParamCheckResult paramCheckResult =
                    buildParamCheckResultApprovalBill(checkTravelPolicyResponseType, hotelApprovalInfoMap,
                        orderCreateRequestType);
                if (paramCheckResult != null) {
                    return paramCheckResult;
                }
            }
            if (checkTravelPolicyResponseType.getVerifyDockingResult() != null) {
                ParamCheckResult paramCheckResult = buildParamCheckResultDocking(checkTravelPolicyResponseType);
                if (paramCheckResult != null) {
                    return paramCheckResult;
                }
            }
        }
        // 按单管控接口出参校验
        if (!CONTROL_BY_PERSON.equalsIgnoreCase(checkTravelPolicyResponseType.getApprovalBillControlDimension())
            && checkTravelPolicyResponseType.getVerifyDockingResult() == null) {
            // 无需管控
            if (null == checkTravelPolicyResponseType.getVerifyApprovalBillResult()) {
                return null;
            }
            // 管控通过
            if (BooleanUtils.isTrue(checkTravelPolicyResponseType.getVerifyApprovalBillResult().isInControl())) {
                return null;
            }
            String logErrorCodeBillResult =
                String.valueOf(checkTravelPolicyResponseType.getVerifyApprovalBillResult().getVerifyCode());
            String friendlyMessageBillResult = SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessage(
                SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_AGG_HOTEL_TMC_SERVICE,
                SoaErrorSharkKeyConstant.ACTION_NAME_CHECK_TRAVEL_POLICY,
                String.valueOf(checkTravelPolicyResponseType.getVerifyApprovalBillResult().getVerifyCode()));
            if (StringUtil.isBlank(friendlyMessageBillResult)) {
                friendlyMessageBillResult =
                    checkTravelPolicyResponseType.getVerifyApprovalBillResult().getVerifyResultDescription();
            }
            // 管控不通过+单据查询失败
            if (CollectionUtil.isEmpty(hotelApprovalInfoMap) && StringUtil.isNotBlank(
                Optional.ofNullable(orderCreateRequestType.getApprovalInput()).map(ApprovalInput::getSubApprovalNo)
                    .orElse(null))) {
                return new ParamCheckResult(false,
                    OrderCreateErrorEnum.SEARCH_APPROVAL_RESPONSE_IS_ERROR.getErrorCode(), logErrorCodeBillResult,
                    checkTravelPolicyResponseType.getVerifyApprovalBillResult().getVerifyResultDescription(),
                    friendlyMessageBillResult);
            }
            // 管控不通过+有管控元素信息
            if (CollectionUtil.isNotEmpty(checkTravelPolicyResponseType.getVerifyApprovalBillResult().getChecklist())) {
                return null;
            }
            if (CollectionUtil.isEmpty(hotelApprovalInfoMap)) {
                return new ParamCheckResult(false,
                    OrderCreateErrorEnum.SEARCH_APPROVAL_RESPONSE_IS_ERROR.getErrorCode(), logErrorCodeBillResult,
                    checkTravelPolicyResponseType.getVerifyApprovalBillResult().getVerifyResultDescription(),
                    friendlyMessageBillResult);
            }
            // 按单管控不通过 缺失单据信息
            HotelApprovalInfo hotelApprovalInfo = hotelApprovalInfoMap.get(
                Optional.ofNullable(orderCreateRequestType.getApprovalInput()).map(ApprovalInput::getSubApprovalNo)
                    .orElse(null));
            if (hotelApprovalInfo == null) {
                return new ParamCheckResult(false,
                    OrderCreateErrorEnum.SEARCH_APPROVAL_RESPONSE_IS_ERROR.getErrorCode(), logErrorCodeBillResult,
                    checkTravelPolicyResponseType.getVerifyApprovalBillResult().getVerifyResultDescription(),
                    friendlyMessageBillResult);
            }
            // 管控不通过+无管控不通过的元素信息 报错拦截
            return new ParamCheckResult(false,
                OrderCreateErrorEnum.CHECK_TRAVEL_POLICY_RESPONSE_IS_ERROR.getErrorCode(), logErrorCodeBillResult,
                checkTravelPolicyResponseType.getVerifyApprovalBillResult().getVerifyResultDescription(),
                friendlyMessageBillResult);
        }
        return null;
    }

    protected ParamCheckResult buildParamCheckResultDocking(
        CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
        // 管控通过
        if (BooleanUtils.isTrue(checkTravelPolicyResponseType.getVerifyDockingResult().isInControl())) {
            return null;
        }
        if (CollectionUtil.isNotEmpty(checkTravelPolicyResponseType.getVerifyDockingResult().getHotelTripConflictList())
            || CollectionUtil.isNotEmpty(checkTravelPolicyResponseType.getVerifyDockingResult().getCheckItemList())) {
            return null;
        }
        String friendlyMessageBillResult = SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessage(
            SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_AGG_HOTEL_TMC_SERVICE,
            SoaErrorSharkKeyConstant.ACTION_NAME_CHECK_TRAVEL_POLICY,
            String.valueOf(checkTravelPolicyResponseType.getVerifyDockingResult().getVerifyErrorCode()));
        if (StringUtil.isBlank(friendlyMessageBillResult)) {
            friendlyMessageBillResult =
                checkTravelPolicyResponseType.getVerifyDockingResult().getVerifyResultDescription();
        }
        String logErrorCodeBillResult =
            String.valueOf(checkTravelPolicyResponseType.getVerifyDockingResult().getVerifyErrorCode());
        return new ParamCheckResult(false,
            OrderCreateErrorEnum.CHECK_TRAVEL_POLICY_RESPONSE_IS_ERROR.getErrorCode(), logErrorCodeBillResult,
            checkTravelPolicyResponseType.getVerifyDockingResult().getVerifyResultDescription(),
            friendlyMessageBillResult);
    }

    private ParamCheckResult buildParamCheckResultApprovalBill(
        CheckTravelPolicyResponseType checkTravelPolicyResponseType,
        Map<String, HotelApprovalInfo> hotelApprovalInfoMap, OrderCreateRequestType orderCreateRequestType) {
        // 管控通过
        if (BooleanUtils.isTrue(checkTravelPolicyResponseType.getVerifyApprovalBillResult().isInControl())) {
            return null;
        }
        Integer errorCode = OrderCreateErrorEnum.SEARCH_APPROVAL_RESPONSE_IS_ERROR.getErrorCode();
        Integer logErrorCode = checkTravelPolicyResponseType.getVerifyApprovalBillResult().getVerifyCode();
        String friendlyMessageBillResult = SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessage(
            SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_AGG_HOTEL_TMC_SERVICE,
            SoaErrorSharkKeyConstant.ACTION_NAME_CHECK_TRAVEL_POLICY,
            String.valueOf(checkTravelPolicyResponseType.getVerifyApprovalBillResult().getVerifyCode()));
        if (StringUtil.isBlank(friendlyMessageBillResult)) {
            friendlyMessageBillResult =
                checkTravelPolicyResponseType.getVerifyApprovalBillResult().getVerifyResultDescription();
        }
        if (CollectionUtil.isEmpty(hotelApprovalInfoMap) || orderCreateRequestType.getApprovalInput() == null
            || hotelApprovalInfoMap.get(orderCreateRequestType.getApprovalInput().getSubApprovalNo()) == null) {
            return new ParamCheckResult(false, errorCode, String.valueOf(logErrorCode),
                checkTravelPolicyResponseType.getVerifyApprovalBillResult().getVerifyResultDescription(),
                friendlyMessageBillResult);
        }
        // 管控不通过+有管控元素信息
        if (CollectionUtil.isNotEmpty(checkTravelPolicyResponseType.getVerifyApprovalBillResult().getChecklist())) {
            return null;
        }
        // 管控不通过+无管控不通过的元素信息 报错拦截
        return new ParamCheckResult(false, errorCode, String.valueOf(logErrorCode),
            checkTravelPolicyResponseType.getVerifyApprovalBillResult().getVerifyResultDescription(),
            friendlyMessageBillResult);
    }

    private VerifyApprovalInfo buildVerifyApprovalInfoByDocking(
        CheckTravelPolicyResponseType checkTravelPolicyResponseType, OrderCreateRequestType orderCreateRequestType,
        Map<String, HotelApprovalInfo> hotelApprovalInfoMap, WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo,
        WrapperOfAccount.AccountInfo accountInfo, WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo) {
        // 管控不通过-管控浮层
        if (!isFail(checkTravelPolicyResponseType)) {
            return null;
        }
        VerifyApprovalInfo verifyApprovalInfo = new VerifyApprovalInfo();
        verifyApprovalInfo.setVerifyDockingInfo(buildVerifyDockingInfo(checkTravelPolicyResponseType));
        if (needBookingApproval(checkTravelPolicyResponseType)) {
            HotelApprovalInfo hotelApprovalInfo =
                hotelApprovalInfoMap.get(orderCreateRequestType.getApprovalInput().getSubApprovalNo());
            verifyApprovalInfo.setVerifyResults(
                buildVerifyResults(checkTravelPolicyResponseType.getVerifyApprovalBillResult().getChecklist(),
                    hotelApprovalInfo, cityBaseInfo, orderCreateRequestType, accountInfo));
            verifyApprovalInfo.setApprovalInfos(buildApprovalInfos(hotelApprovalInfo, cityBaseInfo,
                checkTravelPolicyResponseType.getVerifyApprovalBillResult().getBillDetailInfo(), orderCreateRequestType,
                checkTravelPolicyResponseType.getVerifyApprovalBillResult().getChecklist(), checkAvailContextInfo));
            verifyApprovalInfo.setBillExtendInfo(
                buildBillExtendInfo(hotelApprovalInfo, accountInfo, orderCreateRequestType));
        }
        verifyApprovalInfo.setVerifyDimension(checkTravelPolicyResponseType.getApprovalBillControlDimension());
        verifyApprovalInfo.setFailRule(buildFailRule(checkTravelPolicyResponseType));
        verifyApprovalInfo.setControlModels(buildControlModels(checkTravelPolicyResponseType));
        return verifyApprovalInfo;
    }


    public static final String FORBID_BOOKING = "F";
    public static final String CONTINUE_BOOKING = "T";

    public static final String NC_BOOKING = "NC";

    protected String buildFailRule(CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
        String controlStrategy =
            checkTravelPolicyResponseType.getCheckResultExt().getBillAndDockingMergedResult().getControlStrategy();
        if (StringUtil.equalsIgnoreCase(controlStrategy, FORBID_BOOKING) || StringUtil.equalsIgnoreCase(controlStrategy,
            NC_BOOKING)) {
            return "FORBID_BOOKING";
        }
        if (StringUtil.equalsIgnoreCase(controlStrategy, CONTINUE_BOOKING)) {
            return "ALLOW_BOOKING";
        }
        return null;
    }

    protected VerifyDockingInfo buildVerifyDockingInfo(CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
        VerifyDockingResultType verifyDockingResult = checkTravelPolicyResponseType.getVerifyDockingResult();
        if (verifyDockingResult == null) {
            return null;
        }
        if (!needBookingDocking(checkTravelPolicyResponseType)) {
            return null;

        }
        VerifyDockingInfo verifyDocking = new VerifyDockingInfo();
        if (CollectionUtil.isNotEmpty(verifyDockingResult.getHotelTripConflictList())) {
            HotelTripConflictInfo hotelTripConflictInfo = new HotelTripConflictInfo();
            hotelTripConflictInfo.setHotelTripConflictDetails(
                verifyDockingResult.getHotelTripConflictList().stream().filter(Objects::nonNull)
                    .map(hotelTripConflictType -> {
                        HotelTripConflictDetail hotelTripConflictDetail = new HotelTripConflictDetail();
                        hotelTripConflictDetail.setConflictItems(buildConflictItems(hotelTripConflictType));
                        return hotelTripConflictDetail;
                    }).collect(Collectors.toList()));
            verifyDocking.setHotelTripConflictInfo(hotelTripConflictInfo);
        }
        if (CollectionUtil.isNotEmpty(verifyDockingResult.getCheckItemList())) {
            verifyDocking.setCheckItems(
                verifyDockingResult.getCheckItemList().stream().filter(Objects::nonNull).map(checkItemType -> {
                    CheckItem checkItem = new CheckItem();
                    checkItem.setCheckDesc(checkItemType.getCheckDescription());
                    checkItem.setCheckCode(checkItemType.getCheckCode());
                    return checkItem;
                }).collect(Collectors.toList()));
        }
        return verifyDocking;
    }

    /**
     * 行程冲突code：ORDER_ID：订单号 GUEST_NAME：入住人 CHECK_DATE：入离日期 HOTEL_NAME：入住酒店 CITY_NAME：入住城市
     *
     * @param hotelTripConflictType
     * @return
     */
    protected List<CheckItem> buildConflictItems(HotelTripConflictType hotelTripConflictType) {
        if (hotelTripConflictType == null) {
            return null;
        }
        List<CheckItem> checkItems = new ArrayList<>();
        CheckItem checkItemOrderId = new CheckItem();
        checkItemOrderId.setCheckCode(ORDER_ID);
        checkItemOrderId.setCheckDesc(hotelTripConflictType.getOrderId());
        checkItems.add(checkItemOrderId);

        CheckItem checkItemPsg = new CheckItem();
        checkItemPsg.setCheckCode(GUEST_NAME);
        checkItemPsg.setCheckDesc(hotelTripConflictType.getGuestName());
        checkItems.add(checkItemPsg);

        CheckItem checkItemCheckDate = new CheckItem();
        checkItemCheckDate.setCheckCode(CHECK_DATE);
        checkItemCheckDate.setCheckDesc(StringUtil.indexedFormat("{0}-{1}", hotelTripConflictType.getCheckInDate(),
            hotelTripConflictType.getCheckOutDate()));
        checkItems.add(checkItemCheckDate);

        CheckItem checkItemHotel = new CheckItem();
        checkItemHotel.setCheckCode(HOTEL_NAME);
        checkItemHotel.setCheckDesc(hotelTripConflictType.getHotelName());
        checkItems.add(checkItemHotel);

        CheckItem checkItemCity = new CheckItem();
        checkItemCity.setCheckCode(CITY_NAME);
        checkItemCity.setCheckDesc(hotelTripConflictType.getCheckinCityName());
        checkItems.add(checkItemCity);
        return checkItems;
    }

    /**
     * 下单管控/下单管控+按单审批管控-综合后管控不通过
     *
     * @return
     */
    protected boolean isFail(CheckTravelPolicyResponseType responseType) {
        return StringUtil.equalsIgnoreCase(FAIL,
            Optional.ofNullable(responseType.getCheckResultExt()).map(CheckResultExtType::getBillAndDockingMergedResult)
                .map(BillAndDockingMergedResultType::getVerifyResult).orElse(UNNECESSARY));
    }

    private ParamCheckResult buildParamCheckResult(CheckTravelPolicyResponseType checkTravelPolicyResponseType,
        OrderCreateErrorEnum orderCreateErrorEnum) {
        return new ParamCheckResult(false, orderCreateErrorEnum.getErrorCode(),
            Optional.ofNullable(checkTravelPolicyResponseType.getVerifyApprovalBillResult())
                .map(VerifyApprovalBillResultType::getVerifyCode).orElse(null) != null ?
                String.valueOf(checkTravelPolicyResponseType.getVerifyApprovalBillResult().getVerifyCode()) : null,
            Optional.ofNullable(checkTravelPolicyResponseType.getVerifyApprovalBillResult())
                .map(VerifyApprovalBillResultType::getVerifyResultDescription).orElse(null),
            BFFSharkUtil.getSoaCodeSharkValue(CommonConstant.APPID, "CorpAggHotelTMCService", "checkTravelPolicy",
                String.valueOf(Optional.ofNullable(checkTravelPolicyResponseType.getVerifyApprovalBillResult())
                    .map(VerifyApprovalBillResultType::getVerifyCode).orElse(0))));
    }

    protected Map<String, HotelApprovalInfo> buildHotelApprovalInfoMap(OrderCreateRequestType orderCreateRequestType,
        SearchApprovalResponseType searchApprovalResponseType) {
        List<String> approvalNumberList = buildApprovalNumberList(
            orderCreateRequestType.getHotelBookPassengerInputs().stream()
                .map(HotelBookPassengerInput::getHotelPassengerInput).map(HotelPassengerInput::getApprovalInput)
                .collect(Collectors.toList()), orderCreateRequestType.getApprovalInput());
        if (CollectionUtil.isEmpty(approvalNumberList)) {
            return null;
        }
        List<HotelApprovalInfo> hotelApprovalInfoList =
            Optional.ofNullable(searchApprovalResponseType).map(SearchApprovalResponseType::getSearchApprovalResult)
                .map(SearchApprovalResult::getHotelApprovalInfoList).orElse(Collections.emptyList()).stream().filter(
                    h -> Optional.ofNullable(h).map(HotelApprovalInfo::getApprovalBaseInfo)
                        .map(ApprovalBaseInfo::getApprovalNumber).map(StringUtil::isNotBlank).orElse(false))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(hotelApprovalInfoList)) {
            return null;
        }
        return hotelApprovalInfoList.stream().collect(
            Collectors.toMap(hotelApprovalInfo -> hotelApprovalInfo.getApprovalBaseInfo().getApprovalNumber(),
                hotelApprovalInfo -> hotelApprovalInfo));
    }

    protected List<String> buildApprovalNumberList(List<ApprovalInput> approvalInputList, ApprovalInput approvalInput) {
        List<String> approvalNumberList = Lists.newArrayList();
        if (approvalInput != null) {
            approvalNumberList.add(approvalInput.getSubApprovalNo());
        }
        if (CollectionUtil.isNotEmpty(approvalInputList)) {
            approvalNumberList.addAll(
                approvalInputList.stream().filter(Objects::nonNull).map(ApprovalInput::getSubApprovalNo)
                    .collect(Collectors.toList()));
        }
        return approvalNumberList;
    }

    private ParamCheckResult buildParamCheckResultErrorByPassenger(
        CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
        String friendlyMessageBillResult = SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessage(
            SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_AGG_HOTEL_TMC_SERVICE,
            SoaErrorSharkKeyConstant.ACTION_NAME_CHECK_TRAVEL_POLICY, String.valueOf(
                Optional.ofNullable(checkTravelPolicyResponseType.getVerifyPassengerResult())
                    .map(VerifyPassengerResultType::getVerifyErrorCode)
                    .orElse(OrderCreateErrorEnum.CHECK_TRAVEL_POLICY_RESPONSE_IS_ERROR.getErrorCode())));
        if (StringUtil.isBlank(friendlyMessageBillResult)) {
            friendlyMessageBillResult = Optional.ofNullable(checkTravelPolicyResponseType.getVerifyPassengerResult())
                .map(VerifyPassengerResultType::getVerifyResultDescription).orElse(null);
        }
        String logErrorCodeBillResult = String.valueOf(
            Optional.ofNullable(checkTravelPolicyResponseType.getVerifyPassengerResult())
                .map(VerifyPassengerResultType::getVerifyErrorCode)
                .orElse(OrderCreateErrorEnum.CHECK_TRAVEL_POLICY_RESPONSE_IS_ERROR.getErrorCode()));
        return new ParamCheckResult(false, OrderCreateErrorEnum.CHECK_TRAVEL_POLICY_RESPONSE_IS_ERROR.getErrorCode(),
            logErrorCodeBillResult, Optional.ofNullable(checkTravelPolicyResponseType.getVerifyPassengerResult())
            .map(VerifyPassengerResultType::getVerifyResultDescription).orElse(null), friendlyMessageBillResult);
    }

    private boolean checkVerifyPassengerDetailType(VerifyPassengerDetailType verifyPassengerDetailType,
        Map<String, HotelApprovalInfo> hotelApprovalInfoMap) {
        if (verifyPassengerDetailType == null || (StringUtil.isBlank(verifyPassengerDetailType.getInfoId())
            && StringUtil.isBlank(verifyPassengerDetailType.getUid()))) {
            return false;
        }
        if (!StringUtil.equalsIgnoreCase(FAIL,
            Optional.ofNullable(verifyPassengerDetailType.getVerifyResult()).orElse(UNNECESSARY))) {
            return true;
        }
        if (CollectionUtil.isEmpty(verifyPassengerDetailType.getCheckList())) {
            return false;
        }
        if (StringUtil.isNotBlank(verifyPassengerDetailType.getPreApprovalNumber())) {
            return !CollectionUtil.isEmpty(hotelApprovalInfoMap)
                && hotelApprovalInfoMap.get(verifyPassengerDetailType.getPreApprovalNumber()) != null;
        }
        return true;
    }

    private VerifyApprovalInfo buildVerifyApprovalInfoByPassenger(
        CheckTravelPolicyResponseType checkTravelPolicyResponseType, OrderCreateRequestType orderCreateRequestType,
        Map<String, HotelApprovalInfo> hotelApprovalInfoMap, WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo,
        WrapperOfAccount.AccountInfo accountInfo, WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo) {
        if (checkTravelPolicyResponseType.getVerifyPassengerResult() == null) {
            return null;
        }
        if (UNNECESSARY.equalsIgnoreCase(checkTravelPolicyResponseType.getVerifyPassengerResult().getVerifyResult())
            || PASS.equalsIgnoreCase(checkTravelPolicyResponseType.getVerifyPassengerResult().getVerifyResult())
            || EMERGENCY.equalsIgnoreCase(checkTravelPolicyResponseType.getVerifyPassengerResult().getVerifyResult())) {
            return null;
        }
        VerifyApprovalInfo verifyApprovalInfo = new VerifyApprovalInfo();
        verifyApprovalInfo.setVerifyDimension(checkTravelPolicyResponseType.getApprovalBillControlDimension());
        verifyApprovalInfo.setFailRule(checkTravelPolicyResponseType.getVerifyPassengerResult().getOverControlRule());
        List<VerifyApprovalPassengerInfo> verifyApprovalPassengerInfos = new ArrayList<>();
        checkTravelPolicyResponseType.getVerifyPassengerResult().getVerifyPassengerDetailList()
            .forEach(verifyPassengerDetailType -> {
                if (FAIL.equalsIgnoreCase(verifyPassengerDetailType.getVerifyResult())) {
                    HotelApprovalInfo hotelApprovalInfo =
                        hotelApprovalInfoMap.get(verifyPassengerDetailType.getPreApprovalNumber());
                    VerifyApprovalPassengerInfo verifyApprovalPassengerInfo = new VerifyApprovalPassengerInfo();
                    verifyApprovalPassengerInfo.setApprovalInfos(buildApprovalInfos(hotelApprovalInfo, cityBaseInfo,
                        verifyPassengerDetailType.getBillDetailInfo(), orderCreateRequestType,
                        verifyPassengerDetailType.getCheckList(), checkAvailContextInfo));
                    verifyApprovalPassengerInfo.setVerifyResults(
                        buildVerifyResults(verifyPassengerDetailType.getCheckList(), hotelApprovalInfo, cityBaseInfo,
                            orderCreateRequestType, accountInfo));
                    verifyApprovalPassengerInfo.setVerifyPassengerInfo(
                        buildVerifyPassengerInfo(verifyPassengerDetailType));
                    verifyApprovalPassengerInfos.add(verifyApprovalPassengerInfo);
                }
            });
        verifyApprovalInfo.setVerifyApprovalPassengerInfos(verifyApprovalPassengerInfos);
        verifyApprovalInfo.setControlModels(Arrays.asList(APPROVAL_BYPSG));
        return verifyApprovalInfo;
    }

    protected VerifyPassengerInfo buildVerifyPassengerInfo(VerifyPassengerDetailType verifyPassengerDetailType) {
        VerifyPassengerInfo verifyPassengerInfo = new VerifyPassengerInfo();
        SimpleApprovalOutput approvalOutput = new SimpleApprovalOutput();
        approvalOutput.setSubApprovalNo(verifyPassengerDetailType.getPreApprovalNumber());
        verifyPassengerInfo.setSimpleApprovalOutput(approvalOutput);
        verifyPassengerInfo.setUseName(verifyPassengerDetailType.getName());
        verifyPassengerInfo.setApprovalOutput(approvalOutput);
        return verifyPassengerInfo;
    }

    private VerifyApprovalInfo buildVerifyApprovalInfoByOrder(
        CheckTravelPolicyResponseType checkTravelPolicyResponseType, OrderCreateRequestType orderCreateRequestType,
        Map<String, HotelApprovalInfo> hotelApprovalInfoMap, WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo,
        WrapperOfAccount.AccountInfo accountInfo, WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo,
        Map<String, StrategyInfo> strategyInfoMap) {
        if (checkTravelPolicyResponseType.getVerifyApprovalBillResult() == null) {
            return null;
        }
        if (BooleanUtils.isTrue(checkTravelPolicyResponseType.getVerifyApprovalBillResult().isInControl())) {
            return null;
        }
        if (UNNECESSARY.equalsIgnoreCase(checkTravelPolicyResponseType.getVerifyApprovalBillResult().getVerifyResult())
            || PASS.equalsIgnoreCase(checkTravelPolicyResponseType.getVerifyApprovalBillResult().getVerifyResult())
            || EMERGENCY.equalsIgnoreCase(
            checkTravelPolicyResponseType.getVerifyApprovalBillResult().getVerifyResult())) {
            return null;
        }
        if (TRUE.equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getApprovalInput()).map(ApprovalInput::getEmergency)
                .orElse(null))) {
            return null;
        }
        HotelApprovalInfo hotelApprovalInfo =
            hotelApprovalInfoMap.get(orderCreateRequestType.getApprovalInput().getSubApprovalNo());
        VerifyApprovalInfo verifyApprovalInfo = new VerifyApprovalInfo();
        verifyApprovalInfo.setVerifyDimension(checkTravelPolicyResponseType.getApprovalBillControlDimension());
        verifyApprovalInfo.setFailRule(
            checkTravelPolicyResponseType.getVerifyApprovalBillResult().getOverControlRule());
        if (StrategyOfBookingInitUtil.verifyApprovalResultUseAggDetail(strategyInfoMap)) {
            verifyApprovalInfo.setVerifyResults(buildVerifyResultsOfApprovalBillChecklistType(
                checkTravelPolicyResponseType.getVerifyApprovalBillResult().getChecklist(), orderCreateRequestType));
        } else {
            verifyApprovalInfo.setVerifyResults(
                buildVerifyResults(checkTravelPolicyResponseType.getVerifyApprovalBillResult().getChecklist(),
                    hotelApprovalInfo, cityBaseInfo, orderCreateRequestType, accountInfo));
        }
        verifyApprovalInfo.setApprovalInfos(buildApprovalInfos(hotelApprovalInfo, cityBaseInfo,
            checkTravelPolicyResponseType.getVerifyApprovalBillResult().getBillDetailInfo(), orderCreateRequestType,
            checkTravelPolicyResponseType.getVerifyApprovalBillResult().getChecklist(), checkAvailContextInfo));
        verifyApprovalInfo.setBillExtendInfo(
            buildBillExtendInfo(hotelApprovalInfo, accountInfo, orderCreateRequestType));
        verifyApprovalInfo.setControlModels(Arrays.asList(APPROVA_BYORDER));
        return verifyApprovalInfo;
    }

    protected List<VerifyResult> buildVerifyResultsOfApprovalBillChecklistType(
        List<ApprovalBillChecklistType> approvalBillChecklistTypes, OrderCreateRequestType orderCreateRequestType) {
        Map<String, ApprovalBillChecklistType> checklistMap = approvalBillChecklistTypes.stream()
            .collect(Collectors.toMap(ApprovalBillChecklistType::getFieldName, o2 -> o2, (o3, o4) -> o3));
        List<VerifyResult> verifyResults = new ArrayList<>();
        // 按顺序展示，list的顺序不能变动，产品定义的展示顺序
        verifyResults.add(
            getBudgePirceApprovalControlDetail(checklistMap, orderCreateRequestType.getIntegrationSoaRequestType()));
        verifyResults.add(getApprovalControlDetail(checklistMap, ControlElementEnum.VALIDITY_COUNT,
            orderCreateRequestType.getIntegrationSoaRequestType()));
        verifyResults.add(getApprovalControlDetail(checklistMap, ControlElementEnum.TRAVELER,
            orderCreateRequestType.getIntegrationSoaRequestType()));
        verifyResults.add(getPirceApprovalControlDetail(ControlElementEnum.PRICE, checklistMap,
            orderCreateRequestType.getIntegrationSoaRequestType()));
        verifyResults.add(getApprovalControlDetail(checklistMap, ControlElementEnum.CHECK_IN,
            orderCreateRequestType.getIntegrationSoaRequestType()));
        verifyResults.add(getApprovalControlDetail(checklistMap, ControlElementEnum.CHECK_OUT,
            orderCreateRequestType.getIntegrationSoaRequestType()));
        verifyResults.add(getApprovalControlDetail(checklistMap, ControlElementEnum.TO_CITIES,
            orderCreateRequestType.getIntegrationSoaRequestType()));
        verifyResults.add(getApprovalControlDetail(checklistMap, ControlElementEnum.STAR,
            orderCreateRequestType.getIntegrationSoaRequestType()));
        verifyResults.add(getApprovalControlDetail(checklistMap, ControlElementEnum.ROOM_COUNT,
            orderCreateRequestType.getIntegrationSoaRequestType()));
        verifyResults.add(getPirceApprovalControlDetail(ControlElementEnum.ROOM_NIGHT_PRICE, checklistMap,
            orderCreateRequestType.getIntegrationSoaRequestType()));
        verifyResults.add(getPirceApprovalControlDetail(ControlElementEnum.AVERAGE_PRICE, checklistMap,
            orderCreateRequestType.getIntegrationSoaRequestType()));
        return verifyResults.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    protected VerifyResult getPirceApprovalControlDetail(ControlElementEnum type,
        Map<String, ApprovalBillChecklistType> checklistMap, IntegrationSoaRequestType integrationSoaRequestType) {
        if (CollectionUtil.isEmpty(checklistMap) || Objects.isNull(type)) {
            return null;
        }
        ApprovalBillChecklistType priceApprovalBillCheck = checklistMap.get(type.getFieldName());
        ApprovalBillChecklistType currencyApprovalBillCheck =
            checklistMap.get(ControlElementEnum.CURRENCY.getFieldName());
        if (Objects.isNull(priceApprovalBillCheck) || Objects.isNull(currencyApprovalBillCheck)) {
            return null;
        }

        FieldValueDetailType priceFieldValue = priceApprovalBillCheck.getFieldValueDetail();

        String currencyFieldValue = Optional.ofNullable(currencyApprovalBillCheck.getFieldValueDetail())
            .map(FieldValueDetailType::getFieldValueList).map(item -> item.get(0)).map(FieldInfoType::getValue)
            .orElse(null);

        FieldValueDetailType priceInputValue = priceApprovalBillCheck.getInputValueDetail();
        String currencyInputValue = Optional.ofNullable(currencyApprovalBillCheck.getInputValueDetail())
            .map(FieldValueDetailType::getFieldValueList).map(item -> item.get(0)).map(FieldInfoType::getValue)
            .orElse(null);
        VerifyResult verifyResult = new VerifyResult();
        ElementInfo elementInfo = new ElementInfo();
        elementInfo.setType(type.name());
        elementInfo.setDescription(type.getDescription());
        verifyResult.setElementInfo(elementInfo);
        if (type == ControlElementEnum.ROOM_NIGHT_PRICE) {
            verifyResult.setInputValue(BFFSharkUtil.getSharkValue(CHECK_RESULT_ROW_TYPE_ROOM_NIGHT_PRICE_TIP));
        } else {
            verifyResult.setInputValue(
                convertFieldValueDetail(priceInputValue, type, currencyInputValue, integrationSoaRequestType));
        }
        verifyResult.setApprovalValue(
            convertFieldValueDetail(priceFieldValue, type, currencyFieldValue, integrationSoaRequestType));
        verifyResult.setMatched(BooleanUtil.parseStr(
            BooleanUtil.isTrue(priceApprovalBillCheck.isInControl()) && BooleanUtil.isTrue(
                currencyApprovalBillCheck.isInControl())));
        return verifyResult;
    }

    protected VerifyResult getApprovalControlDetail(Map<String, ApprovalBillChecklistType> checklistMap,
        ControlElementEnum type, IntegrationSoaRequestType integrationSoaRequestType) {
        if (CollectionUtil.isEmpty(checklistMap) || Objects.isNull(type)) {
            return null;
        }
        ApprovalBillChecklistType approvalBillChecklistType = checklistMap.get(type.getFieldName());
        if (Objects.isNull(approvalBillChecklistType)) {
            return null;
        }
        VerifyResult verifyResult = new VerifyResult();
        ControlElementEnum controlElementEnum = ControlElementEnum.getControlElementEnum(type.getFieldName());
        if (controlElementEnum == null || controlElementEnum == ControlElementEnum.NONE) {
            return null;
        }
        ElementInfo elementInfo = new ElementInfo();
        elementInfo.setType(controlElementEnum.name());
        elementInfo.setDescription(controlElementEnum.getDescription());
        verifyResult.setElementInfo(elementInfo);
        verifyResult.setApprovalValue(
            convertFieldValueDetail(approvalBillChecklistType.getFieldValueDetail(), type, null,
                integrationSoaRequestType));
        verifyResult.setInputValue(convertFieldValueDetail(approvalBillChecklistType.getInputValueDetail(), type, null,
            integrationSoaRequestType));
        verifyResult.setMatched(BooleanUtil.parseStr(approvalBillChecklistType.isInControl()));
        return verifyResult;
    }

    protected VerifyResult getBudgePirceApprovalControlDetail(Map<String, ApprovalBillChecklistType> checklistMap,
        IntegrationSoaRequestType integrationSoaRequestType) {
        if (CollectionUtil.isEmpty(checklistMap)) {
            return null;
        }
        ApprovalBillChecklistType priceApprovalBillCheck =
            checklistMap.get(ControlElementEnum.BUDGET_AMOUNT.getFieldName());
        ApprovalBillChecklistType currencyApprovalBillCheck =
            checklistMap.get(ControlElementEnum.BUDGET_CURRENCY.getFieldName());
        if (Objects.isNull(priceApprovalBillCheck) || Objects.isNull(currencyApprovalBillCheck)) {
            return null;
        }

        FieldValueDetailType priceFieldValue = priceApprovalBillCheck.getFieldValueDetail();
        String currencyFieldValue = Optional.ofNullable(currencyApprovalBillCheck.getFieldValueDetail())
            .map(FieldValueDetailType::getFieldValueList).map(item -> item.get(0)).map(FieldInfoType::getValue)
            .orElse(null);

        FieldValueDetailType priceInputValue = priceApprovalBillCheck.getInputValueDetail();
        String currencyInputValue = Optional.ofNullable(currencyApprovalBillCheck.getInputValueDetail())
            .map(FieldValueDetailType::getFieldValueList).map(item -> item.get(0)).map(FieldInfoType::getValue)
            .orElse(null);
        VerifyResult verifyResult = new VerifyResult();
        ControlElementEnum controlElementEnum = ControlElementEnum.BUDGET_AMOUNT;
        ElementInfo elementInfo = new ElementInfo();
        elementInfo.setType(controlElementEnum.name());
        elementInfo.setDescription(controlElementEnum.getDescription());
        verifyResult.setElementInfo(elementInfo);
        verifyResult.setApprovalValue(
            convertFieldValueDetail(priceFieldValue, ControlElementEnum.BUDGET_AMOUNT, currencyFieldValue,
                integrationSoaRequestType));
        verifyResult.setInputValue(
            convertFieldValueDetail(priceInputValue, ControlElementEnum.BUDGET_AMOUNT, currencyInputValue,
                integrationSoaRequestType));
        verifyResult.setMatched(BooleanUtil.parseStr(
            BooleanUtil.isTrue(priceApprovalBillCheck.isInControl()) && BooleanUtil.isTrue(
                currencyApprovalBillCheck.isInControl())));
        return verifyResult;
    }

    protected String convertFieldValueDetail(FieldValueDetailType fieldValueDetailType, ControlElementEnum controlEnum,
        String currency, IntegrationSoaRequestType integrationSoaRequestType) {
        if (Objects.isNull(fieldValueDetailType)) {
            return null;
        }
        String relation = fieldValueDetailType.getRelation();
        boolean isBetween = StringUtil.equalsIgnoreCase(BETWEEN, relation);
        List<FieldInfoType> fieldValueList = fieldValueDetailType.getFieldValueList();
        if (CollectionUtil.isEmpty(fieldValueList)) {
            return null;
        }
        // 金额和星级，需要兼容>=某个值
        if (fieldValueList.size() == 2 && (controlEnum == ControlElementEnum.PRICE
            || controlEnum == ControlElementEnum.STAR)) {
            // 最大值返回0 则是不限
            FieldInfoType fieldInfoType = fieldValueList.get(1);
            FieldInfoType minFieldInfoType = fieldValueList.get(0);
            if (Objects.nonNull(fieldInfoType) && Objects.nonNull(minFieldInfoType) && StringUtil.equalsIgnoreCase(
                fieldInfoType.getValue(), "0")) {
                if (controlEnum == ControlElementEnum.PRICE) {
                    return ">=" + formatAmount(new BigDecimal(minFieldInfoType.getValue()), currency);
                }
                return ">=" + minFieldInfoType.getValue();
            }
        }
        StringBuilder result = new StringBuilder();
        for (FieldInfoType fieldInfoType : fieldValueList) {
            if (Objects.isNull(fieldInfoType) || StringUtil.isBlank(fieldInfoType.getValue())) {
                continue;
            }
            String s = fieldInfoType.getValue();
            if (controlEnum == ControlElementEnum.CHECK_IN || controlEnum == ControlElementEnum.CHECK_OUT) {
                s = L10n.dateTimeFormatter(
                    Optional.ofNullable(integrationSoaRequestType).map(IntegrationSoaRequestType::getLanguage)
                        .orElse(null)).ymdFullString(DateUtil.parseDate(s, DateUtil.YYYY_MM_DD));
            }
            if (PRICE_LIST.contains(controlEnum)) {
                s = formatAmount(StringUtil.isNotBlank(s) ? new BigDecimal(s) : BigDecimal.ZERO, currency);
            }
            if (StringUtil.isNotBlank(result)) {
                if (isBetween) {
                    result.append(" - ").append(s);
                } else {
                    result.append(",").append(s);
                }
                continue;
            }
            result.append(s);
        }
        return result.toString();
    }

    /**
     * APPROVA_BYORDER审批按单管控/APPROVAL_BYPSG审批按人管控/DOCKING下单管控
     *
     * @param responseType
     * @return
     */
    protected List<String> buildControlModels(CheckTravelPolicyResponseType responseType) {
        List<String> controlModels = new ArrayList<>();
        if (needBookingDocking(responseType)) {
            controlModels.add(DOCKING);
        }
        if (needBookingApproval(responseType)) {
            controlModels.add(APPROVA_BYORDER);
        }
        return controlModels;
    }

    /**
     * 需要下单管控模块
     * 管控不通过且需要管控
     *
     * @return
     */
    protected boolean needBookingDocking(CheckTravelPolicyResponseType responseType) {
        if (isUnnecessary(responseType) || isPass(responseType)) {
            return false;
        }
        if (responseType.getVerifyDockingResult() == null) {
            return false;
        }
        if (BooleanUtils.isTrue(responseType.getVerifyDockingResult().isInControl())) {
            return false;

        }
        return true;
    }

    /**
     * 需要审批管控模块
     * 管控不通过且需要管控
     *
     * @return
     */
    protected boolean needBookingApproval(CheckTravelPolicyResponseType responseType) {
        if (isUnnecessary(responseType) || isPass(responseType)) {
            return false;
        }
        return StringUtil.equalsIgnoreCase(FAIL, Optional.ofNullable(responseType.getVerifyApprovalBillResult())
            .map(VerifyApprovalBillResultType::getVerifyResult).orElse(null));
    }

    /**
     * 下单管控/下单管控+按单审批管控-综合后综合后无需管控
     *
     * @return
     */
    protected boolean isUnnecessary(CheckTravelPolicyResponseType responseType) {
        return StringUtil.equalsIgnoreCase(UNNECESSARY,
            Optional.ofNullable(responseType.getCheckResultExt()).map(CheckResultExtType::getBillAndDockingMergedResult)
                .map(BillAndDockingMergedResultType::getVerifyResult).orElse(UNNECESSARY));
    }

    /**
     * 下单管控/下单管控+按单审批管控-综合后综合后紧急预订
     *
     * @return
     */
    protected boolean isEmergency(CheckTravelPolicyResponseType responseType) {
        return StringUtil.equalsIgnoreCase(EMERGENCY,
            Optional.ofNullable(responseType.getCheckResultExt()).map(CheckResultExtType::getBillAndDockingMergedResult)
                .map(BillAndDockingMergedResultType::getVerifyResult).orElse(UNNECESSARY));
    }

    /**
     * 下单管控/下单管控+按单审批管控-综合后管控通过
     *
     * @return
     */
    protected boolean isPass(CheckTravelPolicyResponseType responseType) {
        return StringUtil.equalsIgnoreCase(PASS,
            Optional.ofNullable(responseType.getCheckResultExt()).map(CheckResultExtType::getBillAndDockingMergedResult)
                .map(BillAndDockingMergedResultType::getVerifyResult).orElse(UNNECESSARY));
    }

    protected BillExtendInfo buildBillExtendInfo(HotelApprovalInfo hotelApprovalInfo,
        WrapperOfAccount.AccountInfo accountInfo, OrderCreateRequestType orderCreateRequestType) {
        BillExtendInfo billExtendInfo = new BillExtendInfo();
        billExtendInfo.setCanChangeApprovalNo(BooleanUtil.parseStr(
            accountInfo.isPreApprovalRequired(CityInfoUtil.oversea(orderCreateRequestType.getCityInput().getCityId()),
                orderCreateRequestType.getCorpPayInfo()) && !accountInfo.isOaApprovalHead()));
        if (hotelApprovalInfo.getHotelProductInfo() == null || hotelApprovalInfo.getApprovalBaseInfo() == null) {
            return billExtendInfo;
        }
        billExtendInfo.setRankId(hotelApprovalInfo.getApprovalRankInfo().getRankID());
        billExtendInfo.setRankName(hotelApprovalInfo.getApprovalRankInfo().getRankName());
        if (MathUtils.isGreaterThanZero(hotelApprovalInfo.getHotelProductInfo().getBudgetAmount())) {
            AmountInfo amountInfo = new AmountInfo();
            amountInfo.setAmount(hotelApprovalInfo.getHotelProductInfo().getBudgetAmount().toString());
            amountInfo.setCurrency(hotelApprovalInfo.getHotelProductInfo().getCurrency());
            billExtendInfo.setBudgetAmount(amountInfo);
        }
        SimpleApprovalOutput approvalOutput = new SimpleApprovalOutput();
        approvalOutput.setSubApprovalNo(hotelApprovalInfo.getApprovalBaseInfo().getApprovalNumber());
        billExtendInfo.setSimpleApprovalOutput(approvalOutput);
        billExtendInfo.setApprovalOutput(approvalOutput);
        return billExtendInfo;
    }

    protected List<ApprovalInfo> buildApprovalInfos(HotelApprovalInfo hotelApprovalInfo,
        WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo, BillDetailInfoType billDetailInfoType,
        OrderCreateRequestType orderCreateRequestType, List<ApprovalBillChecklistType> checkList,
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo) {
        List<ApprovalInfo> approvalInfos = new ArrayList<>();
        if (isControlBudget(billDetailInfoType) && MathUtils.isGreaterThanZero(billDetailInfoType.getBudgetAmount())) {
            approvalInfos.add(buildApprovalInfoBase(String.valueOf(billDetailInfoType.getBudgetAmount()),
                ControlElementEnum.BUDGET_LEFT));
        }
        approvalInfos.add(buildApprovalInfoBase(
            billDomesticHotel(hotelApprovalInfo) ? BFFSharkUtil.getSharkValue(SharkKeyConstant.CONTROL_VALUE_DHOTEL) :
                BFFSharkUtil.getSharkValue(SharkKeyConstant.CONTROL_VALUE_IHOTEL), ControlElementEnum.ORDER_TYPE));
        approvalInfos.add(buildApprovalInfoBase(billRankName(hotelApprovalInfo), ControlElementEnum.RANK));
        approvalInfos.add(
            buildApprovalInfoBase(billPassenger(hotelApprovalInfo, checkList), ControlElementEnum.TRAVELER));
        approvalInfos.add(buildApprovalInfoCity(hotelApprovalInfo, cityBaseInfo, ControlElementEnum.LOCATION));
        approvalInfos.add(buildApprovalInfoBase(
            billCheckIn(hotelApprovalInfo, checkList, orderCreateRequestType.getIntegrationSoaRequestType()),
            ControlElementEnum.CHECK_IN));
        approvalInfos.add(buildApprovalInfoBase(
            billCheckOut(hotelApprovalInfo, checkList, orderCreateRequestType.getIntegrationSoaRequestType()),
            ControlElementEnum.CHECK_OUT));
        approvalInfos.add(buildApprovalInfoBase(billPrice(hotelApprovalInfo, checkList), ControlElementEnum.PRICE));
        String averagePrice = getCheckFieldValue(ControlElementEnum.AVERAGE_PRICE, checkList);
        if (StringUtil.isNotBlank(averagePrice) && MathUtils.isGreaterThanZero(new BigDecimal(averagePrice))) {
            approvalInfos.add(
                buildApprovalInfoBase(String.valueOf(new BigDecimal(averagePrice)), ControlElementEnum.AVERAGE_PRICE));
        }
        approvalInfos.add(
            buildApprovalInfoBase(billCurrency(hotelApprovalInfo, checkList), ControlElementEnum.CURRENCY));
        if ("zh-CN".equalsIgnoreCase(orderCreateRequestType.getIntegrationSoaRequestType().getLanguage())) {
            approvalInfos.add(
                buildApprovalInfoBase(getBillMaxStar(hotelApprovalInfo, checkList), ControlElementEnum.MAX_STAR));
            approvalInfos.add(
                buildApprovalInfoBase(getBillMinStar(hotelApprovalInfo, checkList), ControlElementEnum.MIN_STAR));
        } else {
            approvalInfos.add(buildApprovalInfoBase(billStar(hotelApprovalInfo, checkList), ControlElementEnum.STAR));
        }
        // 定制化需求 浙商银行的协议酒店的差标屏蔽 其他公司都不屏蔽
        if (!(QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.SHIELD_TRAVEL_STANDARD,
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId())
            && checkAvailContextInfo.getRoomTypeEnum() == RoomTypeEnum.C)) {
            approvalInfos.add(
                buildApprovalInfoBase(billTravelStandard(hotelApprovalInfo), ControlElementEnum.TRAVEL_STANDARD));
        }
        approvalInfos.add(
            buildApprovalInfoBase(roomCount(hotelApprovalInfo, checkList), ControlElementEnum.ROOM_COUNT));
        String roomNightPrice = getRoomNightPrice(hotelApprovalInfo, checkList);
        if (StringUtil.isNotBlank(roomNightPrice)) {
            approvalInfos.add(buildApprovalInfoBase(roomNightPrice, ControlElementEnum.ROOM_NIGHT_PRICE));
        }
        approvalInfos.add(buildApprovalInfoHotelName(hotelApprovalInfo, cityBaseInfo));
        approvalInfos.add(buildApprovalInfoBase(validityCount(checkList), ControlElementEnum.VALIDITY_COUNT));
        return approvalInfos;
    }

    public String billTravelStandard(HotelApprovalInfo hotelApprovalInfo) {
        BigDecimal averagePrice = Optional.ofNullable(hotelApprovalInfo).map(HotelApprovalInfo::getHotelProductInfo)
            .map(HotelProductInfo::getAveragePrice).orElse(null);
        if (averagePrice != null && MathUtils.isGreaterThanZero(averagePrice)) {
            return String.valueOf(
                new BigDecimal(new DecimalFormat("#.##").format(averagePrice.setScale(2, BigDecimal.ROUND_HALF_UP))));
        }
        return billValueAny();
    }

    protected String validityCount(List<ApprovalBillChecklistType> approvalBillChecklistTypes) {
        return getCheckFieldValue(ControlElementEnum.VALIDITY_COUNT, approvalBillChecklistTypes);
    }

    public boolean isControlBudget(BillDetailInfoType billDetailInfoType) {
        if (billDetailInfoType == null) {
            return false;
        }
        if (BooleanUtils.isTrue(billDetailInfoType.isControlBudget()) && MathUtils.isGreaterThan(
            billDetailInfoType.getBudgetAmount(), BigDecimal.ZERO)) {
            return true;
        }
        return false;
    }

    /**
     * 管控酒店id 单据配置的酒店名称等信息
     *
     * @return
     */
    protected ApprovalInfo buildApprovalInfoHotelName(HotelApprovalInfo hotelApprovalInfo,
        WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo) {
        ApprovalInfo approvalInfo = new ApprovalInfo();
        ElementInfo elementInfo = new ElementInfo();
        elementInfo.setType(ControlElementEnum.HOTELID.name());
        elementInfo.setDescription(ControlElementEnum.HOTELID.getDescription());
        approvalInfo.setElementInfo(elementInfo);
        approvalInfo.setApprovalValue(billCurrentCityHotelName(hotelApprovalInfo, cityBaseInfo));
        ApprovalExtend approvalExtend = new ApprovalExtend();
        approvalExtend.setElementDetails(buildHotelNameElementDetails(hotelApprovalInfo));
        approvalInfo.setApprovalExtend(approvalExtend);
        return approvalInfo;
    }

    public String getRoomNightPrice(HotelApprovalInfo hotelApprovalInfo,
        List<ApprovalBillChecklistType> approvalBillChecklistTypes) {
        String aggRoomNightPrice = getCheckFieldValue(ControlElementEnum.ROOM_NIGHT_PRICE, approvalBillChecklistTypes);
        if (StringUtil.isNotBlank(aggRoomNightPrice)) {
            return aggRoomNightPrice;
        }
        if (MathUtils.isLessOrEqualsZero(hotelApprovalInfo.getHotelProductInfo().getRoomNightPrice())) {
            return null;
        }
        return String.valueOf(hotelApprovalInfo.getHotelProductInfo().getRoomNightPrice());
    }

    protected String roomCount(HotelApprovalInfo hotelApprovalInfo,
        List<ApprovalBillChecklistType> approvalBillChecklistTypes) {
        String roomCount = getCheckFieldValue(ControlElementEnum.ROOM_COUNT, approvalBillChecklistTypes);
        if (StringUtil.isNotBlank(roomCount)) {
            return roomCount;
        }
        Integer room = hotelApprovalInfo.getHotelProductInfo().getRoomCount();
        if (TemplateNumberUtil.getValue(room) <= 0) {
            return StringUtil.EMPTY;
        }
        return String.valueOf(room);
    }

    protected String billStar(HotelApprovalInfo hotelApprovalInfo,
        List<ApprovalBillChecklistType> approvalBillChecklistTypes) {
        return getStarValue(getMinStar(hotelApprovalInfo, approvalBillChecklistTypes),
            getMaxStar(hotelApprovalInfo, approvalBillChecklistTypes));
    }

    private String getStarValue(int min, int max) {
        // 不限
        if (min <= 0 && max <= 0) {
            return billValueAny();
        }
        // 不限-{0}
        if (min <= 0) {
            return StringUtil.indexedFormat(BFFSharkUtil.getSharkValue(SharkKeyConstant.CONTROL_VALUE_BILL_FROM_ANY),
                max);
        }
        // {0}-不限
        if (max <= 0) {
            return StringUtil.indexedFormat(BFFSharkUtil.getSharkValue(SharkKeyConstant.CONTROL_VALUE_BILL_TO_ANY),
                min);
        }
        // {0}-{1}
        return StringUtil.indexedFormat(BFFSharkUtil.getSharkValue(SharkKeyConstant.CONTROL_VALUE_BILL_NOT_ANY), min,
            max);
    }

    protected String getBillMaxStar(HotelApprovalInfo hotelApprovalInfo,
        List<ApprovalBillChecklistType> approvalBillChecklistTypes) {
        return getMaxStar(hotelApprovalInfo, approvalBillChecklistTypes) <= 0 ? billValueAny() :
            String.valueOf(getMaxStar(hotelApprovalInfo, approvalBillChecklistTypes));
    }

    protected String getBillMinStar(HotelApprovalInfo hotelApprovalInfo,
        List<ApprovalBillChecklistType> approvalBillChecklistTypes) {
        return getMinStar(hotelApprovalInfo, approvalBillChecklistTypes) <= 0 ? billValueAny() :
            String.valueOf(getMinStar(hotelApprovalInfo, approvalBillChecklistTypes));
    }

    protected int getMinStar(HotelApprovalInfo hotelApprovalInfo,
        List<ApprovalBillChecklistType> approvalBillChecklistTypes) {
        String aggControlStar = getCheckFieldValue(ControlElementEnum.STAR, approvalBillChecklistTypes);
        if (StringUtil.isNotBlank(aggControlStar)) {
            String[] fieldArr = aggControlStar.split(ORDER_CHECK_STAR_SPLIT_MARK);
            if (fieldArr.length == STAR_ARRAY_LENGTH) {
                return TemplateNumberUtil.getValue(Integer.valueOf(fieldArr[NumberUtils.INTEGER_ZERO].trim()));
            }
        }
        return TemplateNumberUtil.getValue(hotelApprovalInfo.getHotelProductInfo().getMinStarRating());
    }

    protected int getMaxStar(HotelApprovalInfo hotelApprovalInfo,
        List<ApprovalBillChecklistType> approvalBillChecklistTypes) {
        String aggControlStar = getCheckFieldValue(ControlElementEnum.STAR, approvalBillChecklistTypes);
        if (StringUtil.isNotBlank(aggControlStar)) {
            String[] fieldArr = aggControlStar.split(ORDER_CHECK_STAR_SPLIT_MARK);
            if (fieldArr.length == STAR_ARRAY_LENGTH) {
                return TemplateNumberUtil.getValue(Integer.valueOf(fieldArr[NumberUtils.INTEGER_ONE].trim()));
            }
        }
        return TemplateNumberUtil.getValue(hotelApprovalInfo.getHotelProductInfo().getMaxStarRating());
    }

    public String billCurrency(HotelApprovalInfo hotelApprovalInfo,
        List<ApprovalBillChecklistType> approvalBillChecklistTypes) {
        String currecny = getCheckFieldValue(ControlElementEnum.CURRENCY, approvalBillChecklistTypes);
        if (StringUtil.isNotBlank(currecny)) {
            return currecny;
        }
        return hotelApprovalInfo.getHotelProductInfo().getCurrency();
    }

    protected String billPrice(HotelApprovalInfo hotelApprovalInfo,
        List<ApprovalBillChecklistType> approvalBillChecklistTypes) {
        String price = getCheckFieldValue(ControlElementEnum.PRICE, approvalBillChecklistTypes);
        if (StringUtil.isNotBlank(price)) {
            return price;
        }
        return getPriceValue(hotelApprovalInfo.getHotelProductInfo().getMinPrice(),
            hotelApprovalInfo.getHotelProductInfo().getMaxPrice());
    }

    protected String getPriceValue(BigDecimal from, BigDecimal to) {
        // 不限
        if (MathUtils.isLessOrEqualsZero(from) && MathUtils.isLessOrEqualsZero(to)) {
            return billValueAny();
        }
        // 0-{0}
        if (MathUtils.isLessOrEqualsZero(from)) {
            return StringUtil.indexedFormat(BFFSharkUtil.getSharkValue(SharkKeyConstant.CONTROL_VALUE_BILL_FROM_ZERO),
                to);
        }
        // {0}-不限
        if (MathUtils.isLessOrEqualsZero(to)) {
            return StringUtil.indexedFormat(BFFSharkUtil.getSharkValue(SharkKeyConstant.CONTROL_VALUE_BILL_TO_ANY),
                from);
        }
        // {0}-{1}
        return StringUtil.indexedFormat(BFFSharkUtil.getSharkValue(SharkKeyConstant.CONTROL_VALUE_BILL_NOT_ANY), from,
            to);
    }

    protected String billCheckOut(HotelApprovalInfo hotelApprovalInfo,
        List<ApprovalBillChecklistType> approvalBillChecklistTypes,
        IntegrationSoaRequestType integrationSoaRequestType) {
        String checkOut = getCheckFieldValue(ControlElementEnum.CHECK_OUT, approvalBillChecklistTypes);
        if (StringUtil.isNotBlank(checkOut)) {
            return checkOut;
        }
        return getDateValue(hotelApprovalInfo.getHotelProductInfo().getCheckOutBeginDate(),
            hotelApprovalInfo.getHotelProductInfo().getCheckOutEndDate(), integrationSoaRequestType);
    }

    protected String billCheckIn(HotelApprovalInfo hotelApprovalInfo,
        List<ApprovalBillChecklistType> approvalBillChecklistTypes,
        IntegrationSoaRequestType integrationSoaRequestType) {
        String checkIn = getCheckFieldValue(ControlElementEnum.CHECK_IN, approvalBillChecklistTypes);
        if (StringUtil.isNotBlank(checkIn)) {
            return checkIn;
        }
        return getDateValue(hotelApprovalInfo.getHotelProductInfo().getCheckInBeginDate(),
            hotelApprovalInfo.getHotelProductInfo().getCheckInEndDate(), integrationSoaRequestType);
    }

    /**
     * @param fromTime
     * @param toTime
     * @return
     */
    protected String getDateValue(String fromTime, String toTime, IntegrationSoaRequestType integrationSoaRequestType) {
        // 不限
        if (!isDateEmptyForApproval(fromTime) && !isDateEmptyForApproval(toTime)) {
            return billValueAny();
        }
        // 不限-{0}
        if (!isDateEmptyForApproval(fromTime)) {
            return StringUtil.indexedFormat(BFFSharkUtil.getSharkValue(SharkKeyConstant.CONTROL_VALUE_BILL_FROM_ANY),
                getDateEmptyForApproval(toTime, integrationSoaRequestType));
        }
        // {0}-不限
        if (!isDateEmptyForApproval(toTime)) {
            return StringUtil.indexedFormat(BFFSharkUtil.getSharkValue(SharkKeyConstant.CONTROL_VALUE_BILL_TO_ANY),
                getDateEmptyForApproval(fromTime, integrationSoaRequestType));
        }
        // {0}-{1}
        return StringUtil.indexedFormat(BFFSharkUtil.getSharkValue(SharkKeyConstant.CONTROL_VALUE_BILL_NOT_ANY),
            getDateEmptyForApproval(fromTime, integrationSoaRequestType),
            getDateEmptyForApproval(toTime, integrationSoaRequestType));
    }

    protected String getDateEmptyForApproval(String time, IntegrationSoaRequestType integrationSoaRequestType) {
        if (!isDateEmptyForApproval(time)) {
            return billValueAny();
        }
        return DateDisplayUtil.ymdShortString(DateUtil.parseDate(time, DateUtil.YYYY_MM_DD),
            integrationSoaRequestType.getLanguage());
    }

    protected boolean isDateEmptyForApproval(String time) {
        if (StringUtil.isBlank(time) || invalidDateList.stream().anyMatch(date -> date.contains(time))) {
            return false;
        }
        try {
            CalendarUtil.parseCalendar(time, DateUtil.YYYY_MM_DD);
            return true;
        } catch (ParseException e) {
            LogUtil.loggingClogOnly(LogLevelEnum.Error, MapperOfVerifyApprovalResultResponse.class,
                "isDateEmptyForApproval is error", time, null);
            return false;
        }
    }

    protected ApprovalInfo buildApprovalInfoCity(HotelApprovalInfo hotelApprovalInfo,
        WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo, ControlElementEnum controlElementEnum) {
        ApprovalInfo approvalInfo = new ApprovalInfo();
        ElementInfo elementInfo = new ElementInfo();
        elementInfo.setDescription(ControlElementEnum.TO_CITIES.getDescription());
        elementInfo.setType(ControlElementEnum.TO_CITIES.name());
        approvalInfo.setElementInfo(elementInfo);
        approvalInfo.setApprovalValue(
            billOriginCity(hotelApprovalInfo, cityBaseInfo, ControlElementEnum.LOCATION.equals(controlElementEnum)));
        ApprovalExtend approvalExtend = new ApprovalExtend();
        approvalExtend.setElementDetails(
            buildElementDetails(hotelApprovalInfo, ControlElementEnum.LOCATION.equals(controlElementEnum)));
        approvalInfo.setApprovalExtend(approvalExtend);
        return approvalInfo;
    }

    protected String billRankName(HotelApprovalInfo hotelApprovalInfo) {
        return Optional.ofNullable(hotelApprovalInfo.getApprovalRankInfo()).map(ApprovalRankInfo::getRankName)
            .orElse(null);
    }

    private boolean billDomesticHotel(HotelApprovalInfo hotelApprovalInfo) {
        return hotelApprovalInfo.getHotelProductInfo().getProductType() == ProductTypeEnum.DOMESTIC_HOTEL;
    }

    protected ApprovalInfo buildApprovalInfoBase(String value, ControlElementEnum elementEnum) {
        ApprovalInfo approvalInfo = new ApprovalInfo();
        ElementInfo elementInfo = new ElementInfo();
        elementInfo.setType(elementEnum.name());
        elementInfo.setDescription(elementEnum.getDescription());
        approvalInfo.setElementInfo(elementInfo);
        approvalInfo.setApprovalValue(value);
        return approvalInfo;
    }

    protected List<VerifyResult> buildVerifyResults(List<ApprovalBillChecklistType> approvalBillChecklistTypes,
        HotelApprovalInfo hotelApprovalInfo, WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo,
        OrderCreateRequestType orderCreateRequestType, WrapperOfAccount.AccountInfo accountInfo) {
        List<VerifyResult> verifyResults = new ArrayList<>();
        // 是否有按区管控
        boolean hasLocationControl = approvalBillChecklistTypes.stream()
            .anyMatch(check -> ControlElementEnum.LOCATION.getFieldName().equalsIgnoreCase(check.getFieldName()));
        approvalBillChecklistTypes.forEach(approvalBillChecklistType -> {
            VerifyResult verifyResult = new VerifyResult();
            ControlElementEnum controlElementEnum =
                ControlElementEnum.getControlElementEnum(approvalBillChecklistType.getFieldName());
            if (controlElementEnum == ControlElementEnum.NONE) {
                // 出现未接入过的管控元素类型，记录error
                LogUtil.loggingClogOnly(LogLevelEnum.Error, MapperOfVerifyApprovalResultResponse.class,
                    "fieldName is error", approvalBillChecklistType.getFieldName(), null);
                return;
            }
            if (hasLocationControl && ControlElementEnum.TO_CITIES.equals(controlElementEnum)) {
                return;
            }
            ElementInfo elementInfo = new ElementInfo();
            elementInfo.setType(controlElementEnum.name());
            elementInfo.setDescription(controlElementEnum.getDescription());
            verifyResult.setElementInfo(elementInfo);
            verifyResult.setApprovalValue(
                buildApprovalValue(approvalBillChecklistType, hotelApprovalInfo, cityBaseInfo));
            verifyResult.setInputValue(buildInputValue(approvalBillChecklistType, cityBaseInfo));
            verifyResult.setMatched(BooleanUtil.parseStr(BooleanUtils.isTrue(approvalBillChecklistType.isInControl())));
            // 每日房价扩展信息
            verifyResult.setInputExtend(
                buildInputExtend(approvalBillChecklistType, orderCreateRequestType.getIntegrationSoaRequestType(),
                    accountInfo));
            // 城市、区 审批单元素扩展信息
            verifyResult.setApprovalExtend(buildApprovalExtend(approvalBillChecklistType, hotelApprovalInfo));
            verifyResults.add(verifyResult);
        });
        return verifyResults;
    }

    protected ApprovalExtend buildApprovalExtend(ApprovalBillChecklistType approvalBillChecklistType,
        HotelApprovalInfo hotelApprovalInfo) {
        ControlElementEnum controlElementEnum =
            ControlElementEnum.getControlElementEnum(approvalBillChecklistType.getFieldName());
        if (controlElementEnum == ControlElementEnum.TO_CITIES || controlElementEnum == ControlElementEnum.LOCATION) {
            ApprovalExtend approvalExtend = new ApprovalExtend();
            approvalExtend.setElementDetails(buildElementDetails(hotelApprovalInfo, true));
            return approvalExtend;
        }
        if (controlElementEnum == ControlElementEnum.HOTELID) {
            ApprovalExtend approvalExtend = new ApprovalExtend();
            List<ElementDetail> elementDetails = new ArrayList<>();
            getHotelExtInfos(hotelApprovalInfo).stream()
                .filter(x -> x != null && CollectionUtil.isNotEmpty(x.getHotelDetailList())).forEach(x -> {
                    ElementDetail valueInfo = new ElementDetail();
                    valueInfo.setElementKey(x.getCityName());
                    valueInfo.setElementValue(String.join(ORDER_CHECK_TRAVEL_JOIN_MARK,
                        x.getHotelDetailList().stream().map(HotelDetail::getHotelName).collect(Collectors.toList())));
                    elementDetails.add(valueInfo);
                });
            approvalExtend.setElementDetails(elementDetails);
            return approvalExtend;
        }
        return null;
    }

    protected List<ElementDetail> buildHotelNameElementDetails(HotelApprovalInfo hotelApprovalInfo) {
        if (hotelApprovalInfo == null || hotelApprovalInfo.getHotelProductInfo() == null || CollectionUtil.isEmpty(
            getHotelExtInfos(hotelApprovalInfo))) {
            return null;
        }
        List<ElementDetail> valueInfos = new ArrayList<>();
        getHotelExtInfos(hotelApprovalInfo).stream()
            .filter(x -> x != null && CollectionUtil.isNotEmpty(x.getHotelDetailList())).forEach(x -> {
                ElementDetail valueInfo = new ElementDetail();
                valueInfo.setElementKey(x.getCityName());
                valueInfo.setElementValue(String.join(ORDER_CHECK_TRAVEL_JOIN_MARK,
                    x.getHotelDetailList().stream().map(HotelDetail::getHotelName).collect(Collectors.toList())));
                valueInfos.add(valueInfo);
            });
        return valueInfos;
    }

    protected List<ElementDetail> buildElementDetails(HotelApprovalInfo hotelApprovalInfo, Boolean isNeedShowLocation) {
        if (!hasCompatibleCitys(hotelApprovalInfo)) {
            return null;
        }
        Map<String, List<LocationInfo>> locationInfoMap =
            getCheckInLocationInfos(hotelApprovalInfo).stream().collect(Collectors.groupingBy(LocationInfo::getCityId));
        List<ElementDetail> valueInfos = new ArrayList<>();
        getCheckInCityInfos(hotelApprovalInfo).stream()
            .filter(x -> x != null && CollectionUtil.isNotEmpty(x.getCompatibleCityList())).forEach(x -> {
                ElementDetail valueInfo = new ElementDetail();
                valueInfo.setElementKey(
                    buildCityLocationName(x.getCityName(), locationInfoMap.get(String.valueOf(x.getCityId())),
                        isNeedShowLocation));
                valueInfo.setElementValue(String.join(ORDER_CHECK_TRAVEL_JOIN_MARK,
                    x.getCompatibleCityList().stream().map(CompatibleCity::getCityName).collect(Collectors.toList())));
                valueInfos.add(valueInfo);
            });
        if (!hasCityNoCompatibleCitys(hotelApprovalInfo)) {
            return valueInfos;
        }
        ElementDetail valueInfo = new ElementDetail();
        valueInfo.setElementKey(String.join(ORDER_CHECK_TRAVEL_JOIN_MARK,
            getCheckInCityInfos(hotelApprovalInfo).stream()
                .filter(x -> x != null && CollectionUtil.isEmpty(x.getCompatibleCityList())).map(
                    x -> buildCityLocationName(x.getCityName(), locationInfoMap.get(String.valueOf(x.getCityId())),
                        isNeedShowLocation)).collect(Collectors.toList())));
        valueInfos.add(valueInfo);
        return valueInfos;
    }

    private boolean hasCityNoCompatibleCitys(HotelApprovalInfo hotelApprovalInfo) {
        return Optional.ofNullable(hotelApprovalInfo.getHotelProductInfo())
            .map(HotelProductInfo::getCheckInCityInfoList).orElse(Collections.emptyList()).stream()
            .anyMatch(x -> x != null && CollectionUtil.isEmpty(x.getCompatibleCityList()));
    }

    protected List<com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.CityInfo> getCheckInCityInfos(
        HotelApprovalInfo hotelApprovalInfo) {
        return Optional.ofNullable(hotelApprovalInfo.getHotelProductInfo())
            .map(HotelProductInfo::getCheckInCityInfoList).orElse(new ArrayList<>());
    }

    protected boolean hasCompatibleCitys(HotelApprovalInfo hotelApprovalInfo) {
        return Optional.ofNullable(hotelApprovalInfo.getHotelProductInfo())
            .map(HotelProductInfo::getCheckInCityInfoList).orElse(Collections.emptyList()).stream()
            .anyMatch(x -> x != null && CollectionUtil.isNotEmpty(x.getCompatibleCityList()));
    }

    protected String buildApprovalValue(ApprovalBillChecklistType approvalBillChecklistType,
        HotelApprovalInfo hotelApprovalInfo, WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo) {
        ControlElementEnum controlElementEnum =
            ControlElementEnum.getControlElementEnum(approvalBillChecklistType.getFieldName());
        if (controlElementEnum == ControlElementEnum.TO_CITIES || controlElementEnum == ControlElementEnum.LOCATION) {
            return billOriginCity(hotelApprovalInfo, cityBaseInfo, false);
        }
        if (controlElementEnum == ControlElementEnum.HOTELID) {
            return billCurrentCityHotelName(hotelApprovalInfo, cityBaseInfo);
        }
        if (controlElementEnum == ControlElementEnum.TRAVELER) {
            String aggBillPassenger = approvalBillChecklistType.getFieldValue();
            if (StringUtil.isNotBlank(aggBillPassenger)) {
                return aggBillPassenger.replace(ORDER_CHECK_TRAVEL_SPLIT_MARK, ORDER_CHECK_TRAVEL_JOIN_MARK);
            }
            if (hotelApprovalInfo == null || hotelApprovalInfo.getHotelProductInfo() == null) {
                return approvalBillChecklistType.getFieldValue();
            }
            return buildPassengerName(hotelApprovalInfo.getHotelProductInfo().getPassengerInfoList());
        }
        return approvalBillChecklistType.getFieldValue();
    }

    protected String billPassenger(HotelApprovalInfo hotelApprovalInfo,
        List<ApprovalBillChecklistType> approvalBillChecklistTypes) {
        String aggBillPassenger = getCheckFieldValue(ControlElementEnum.TRAVELER, approvalBillChecklistTypes);
        if (StringUtil.isNotBlank(aggBillPassenger)) {
            return aggBillPassenger.replace(ORDER_CHECK_TRAVEL_SPLIT_MARK, ORDER_CHECK_TRAVEL_JOIN_MARK);
        }
        return buildPassengerName(hotelApprovalInfo.getHotelProductInfo().getPassengerInfoList());
    }

    protected String getCheckFieldValue(ControlElementEnum controlElementEnum,
        List<ApprovalBillChecklistType> approvalBillChecklistTypes) {
        return Optional.ofNullable(approvalBillChecklistTypes).orElse(Collections.emptyList()).stream().filter(
                x -> controlElementEnum == ControlElementEnum.getControlElementEnum(x.getFieldName())
                    && StringUtil.isNotBlank(x.getFieldValue())).map(ApprovalBillChecklistType::getFieldValue).findFirst()
            .orElse("");
    }

    protected String billOriginCity(HotelApprovalInfo hotelApprovalInfo,
        WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo, Boolean isNeedShowLocation) {
        CountryInfo countryInfo = getCheckInCountryInfo(hotelApprovalInfo, cityBaseInfo);
        if (countryInfo != null) {
            return StringUtil.indexedFormat(BFFSharkUtil.getSharkValue(SharkKeyConstant.CONTROL_TITLE_COUNTRY),
                countryInfo.getCountryName());
        }
        if (CollectionUtil.isEmpty(hotelApprovalInfo.getHotelProductInfo().getCheckInCityInfoList())) {
            return null;
        }
        List<com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.CityInfo> cityInfos =
            hotelApprovalInfo.getHotelProductInfo().getCheckInCityInfoList().stream().filter(
                cityInfo -> cityInfo != null && cityInfo.getCityId() != null
                    && TemplateNumberUtil.parseInt(cityInfo.getCityId()) > 0).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(cityInfos)) {
            return null;
        }
        Map<String, List<LocationInfo>> locationInfoMap =
            getCheckInLocationInfos(hotelApprovalInfo).stream().collect(Collectors.groupingBy(LocationInfo::getCityId));
        List<String> citys = cityInfos.stream().map(
            cityInfo -> buildCityLocationName(cityInfo.getCityName(), locationInfoMap.get(cityInfo.getCityId()),
                isNeedShowLocation)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(citys)) {
            return null;
        }
        if (CollectionUtil.isEmpty(citys.stream().filter(
            c -> StringUtil.isNotBlank(c) && !"undefined".equalsIgnoreCase(c) && !"null".equalsIgnoreCase(c)
                && !"InvalidCity".equalsIgnoreCase(c)).collect(Collectors.toList()))) {
            return null;
        }
        return String.join(ORDER_CHECK_TRAVEL_JOIN_MARK, citys.stream().filter(
            c -> StringUtil.isNotBlank(c) && !"undefined".equalsIgnoreCase(c) && !"null".equalsIgnoreCase(c)
                && !"InvalidCity".equalsIgnoreCase(c)).collect(Collectors.toList()));
    }

    protected List<LocationInfo> getCheckInLocationInfos(HotelApprovalInfo hotelApprovalInfo) {
        return Optional.ofNullable(hotelApprovalInfo.getHotelProductInfo())
            .map(HotelProductInfo::getCheckInLocationInfoList).orElse(new ArrayList<>());
    }

    protected String buildCityLocationName(String cityName, List<LocationInfo> locationInfos,
        Boolean isNeedShowLocation) {
        if (isNeedShowLocation && !CollectionUtil.isEmpty(locationInfos)) {
            String locationNames = locationInfos.stream().map(LocationInfo::getName)
                .collect(Collectors.joining(ORDER_CHECK_TRAVEL_JOIN_MARK));
            if (StringUtils.isNotEmpty(locationNames)) {
                cityName += Optional.ofNullable(BFFSharkUtil.getSharkValue(SharkKeyConstant.BRACKETS))
                    .map(shark -> StringUtil.indexedFormat(shark, locationNames)).orElse("");
            }
        }
        return cityName;
    }

    public CountryInfo getCheckInCountryInfo(HotelApprovalInfo hotelApprovalInfo,
        WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo) {
        return Optional.ofNullable(hotelApprovalInfo.getHotelProductInfo().getCheckInCountryInfoList())
            .orElse(Collections.emptyList()).stream().filter(
                x -> bookCountryId(cityBaseInfo) > 0 && bookCountryId(cityBaseInfo) == TemplateNumberUtil.parseInt(
                    x.getCountryId())).findFirst().orElse(null);
    }

    protected int bookCountryId(WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo) {
        return Optional.ofNullable(cityBaseInfo).map(CityBaseInfo::getCountryInfo)
            .map(com.ctrip.corp.bff.framework.template.entity.contract.integration.CountryInfo::getCountryId).orElse(0);
    }

    protected String billCurrentCityHotelName(HotelApprovalInfo hotelApprovalInfo,
        WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo) {
        HotelExtInfo hotelExtInfo = getHotelExtInfo(hotelApprovalInfo, cityBaseInfo);
        if (hotelExtInfo == null) {
            return null;
        }
        if (CollectionUtil.isEmpty(hotelExtInfo.getHotelDetailList())) {
            return null;
        }
        return String.join(ORDER_CHECK_TRAVEL_JOIN_MARK,
            hotelExtInfo.getHotelDetailList().stream().map(HotelDetail::getHotelName).collect(Collectors.toList()));
    }

    private HotelExtInfo getHotelExtInfo(HotelApprovalInfo hotelApprovalInfo,
        WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo) {
        return getHotelExtInfos(hotelApprovalInfo).stream().filter(
                x -> bookCityId(cityBaseInfo) > 0 && bookCityId(cityBaseInfo) == TemplateNumberUtil.parseInt(x.getCityID()))
            .findFirst().orElse(null);
    }

    public List<HotelExtInfo> getHotelExtInfos(HotelApprovalInfo hotelApprovalInfo) {
        return Optional.ofNullable(hotelApprovalInfo.getHotelProductInfo().getHotelExtInfoList())
            .orElse(Collections.emptyList());
    }

    protected int bookCityId(WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo) {
        return Optional.ofNullable(cityBaseInfo).map(CityBaseInfo::getCityInfo).map(CityInfo::getCityId).orElse(0);
    }

    protected String buildPassengerName(List<PassengerInfo> passengerInfos) {
        if (CollectionUtil.isEmpty(passengerInfos)) {
            return billValueAny();
        }
        List<String> names = Lists.newArrayList();
        passengerInfos.forEach(passengerInfo -> {
            if (null == passengerInfo) {
                return;
            }
            if (!StringUtil.isBlank(passengerInfo.getPassengerName())) {
                if (!StringUtil.isBlank((passengerInfo.getPassengerNameEn()))) {
                    names.add(StringUtil.indexedFormat("{0}({1})", passengerInfo.getPassengerName(),
                        passengerInfo.getPassengerNameEn()));
                    return;
                }
                names.add(StringUtil.indexedFormat("{0}", passengerInfo.getPassengerName()));
                return;
            }
            if (!StringUtil.isBlank(passengerInfo.getPassengerNameEn())) {
                names.add(passengerInfo.getPassengerNameEn());
            }
        });
        return StringUtils.joinWith(ORDER_CHECK_TRAVEL_JOIN_MARK, names.toArray());
    }

    protected String billValueAny() {
        return BFFSharkUtil.getSharkValue(SharkKeyConstant.CONTROL_VALUE_BILL_ANY);
    }

    protected String buildInputValue(ApprovalBillChecklistType approvalBillChecklistType,
        WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo) {
        ControlElementEnum controlElementEnum =
            ControlElementEnum.getControlElementEnum(approvalBillChecklistType.getFieldName());
        if (controlElementEnum == ControlElementEnum.LOCATION) {
            if (cityBaseInfo == null || cityBaseInfo.getCityInfo() == null) {
                return null;
            }
            if (!StringUtils.isEmpty(approvalBillChecklistType.getInputValue())) {
                return cityBaseInfo.getCityInfo().getLocaleCityName() + Optional.ofNullable(
                        BFFSharkUtil.getSharkValue(SharkKeyConstant.BRACKETS)).filter(StringUtils::isNotEmpty)
                    .map(shark -> StringUtil.indexedFormat(shark, approvalBillChecklistType.getInputValue()))
                    .orElse("");
            } else {
                cityBaseInfo.getCityInfo().getLocaleCityName();
            }
        }
        return approvalBillChecklistType.getInputValue();
    }

    protected InputExtend buildInputExtend(ApprovalBillChecklistType approvalBillChecklistType,
        IntegrationSoaRequestType integrationSoaRequestType, WrapperOfAccount.AccountInfo accountInfo) {
        ControlElementEnum controlElementEnum =
            ControlElementEnum.getControlElementEnum(approvalBillChecklistType.getFieldName());
        if (controlElementEnum == ControlElementEnum.ROOM_NIGHT_PRICE) {
            InputExtend inputExtend;
            try {
                List<RoomNightPriceBO> roomNightPriceBOS = JsonUtil.fromJson(approvalBillChecklistType.getInputValue(),
                    new TypeToken<List<RoomNightPriceBO>>() {
                    }.getType());

                if (CollectionUtil.isEmpty(roomNightPriceBOS)) {
                    return null;
                }
                inputExtend = new InputExtend();
                List<ElementDetail> bookVauleInfos = new ArrayList<>();
                inputExtend.setElementDetails(bookVauleInfos);
                roomNightPriceBOS.forEach(r -> {
                    ElementDetail elementDetail = new ElementDetail();
                    elementDetail.setElementKey(
                        DateDisplayUtil.ymdShortString(r.getRoomDate(), integrationSoaRequestType.getLanguage()));
                    elementDetail.setElementValue(
                        formatAmount(r.getPrice(), accountInfo.getCurrency()));
                    bookVauleInfos.add(elementDetail);
                });
            } catch (Exception e) {
                // 接口返回了入住时间段，但不符合规范，记录error
                LogUtil.loggingClogOnly(LogLevelEnum.Error, MapperOfVerifyApprovalResultResponse.class,
                    "roomDate is error", approvalBillChecklistType.getFieldName(), null);
                return null;
            }
            return inputExtend;
        }
        return null;
    }
    protected String formatAmount(BigDecimal amount, String currency) {
        if (com.ctrip.framework.ucs.common.util.StringUtils.isBlank(currency) || amount == null) {
            return null;
        }
        CurrencyDisplayInfo currencyDisplayInfo = new CurrencyDisplayInfo();
        currencyDisplayInfo.setNumber(amount);
        currencyDisplayInfo.setCurrency(currency);
        currencyDisplayInfo.setProductLine(CurrencyProductLineEnum.HOTEL);
        return CurrencyDisplayUtil.currencyString(currencyDisplayInfo);
    }

    public static class RoomNightPriceBO {
        /**
         * 价格
         */
        public BigDecimal price;
        /**
         * 入住日期
         */
        public Date roomDate;

        public BigDecimal getPrice() {
            return price;
        }

        public void setPrice(BigDecimal price) {
            this.price = price;
        }

        public Date getRoomDate() {
            return roomDate;
        }

        public void setRoomDate(Date roomDate) {
            this.roomDate = roomDate;
        }
    }
}
