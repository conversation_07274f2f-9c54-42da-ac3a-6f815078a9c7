package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelGeoInfoResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken;
import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityInfoUtil;

import java.util.Optional;

/**
 * @Author: chenchuang
 * @Date: 2024/9/2 13:25
 * @Description: 从解析后的token中获取所需要的信息
 */
public class TokenInfoGetUtil {

    public static String getRoomTypeFromResourceToken(ResourceToken resourceToken) {
        String roomType = Optional.ofNullable(resourceToken).map(ResourceToken::getRoomResourceToken)
                .map(RoomResourceToken::getRoomType).orElse(null);
        return roomType;
    }

    public static Boolean isOverSeaFromResourceToken(ResourceToken resourceToken) {
        return CityInfoUtil.oversea(Optional.ofNullable(resourceToken)
                .map(ResourceToken::getHotelResourceToken)
                .map(HotelResourceToken::getHotelGeoInfoResourceToken)
                .map(HotelGeoInfoResourceToken::getCityId)
                .orElse(null));
    }

}
