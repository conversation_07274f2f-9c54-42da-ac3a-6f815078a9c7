package com.ctrip.corp.bff.hotel.book.mapper.mapperofhotelpassengercheck;

import com.ctrip.corp.agg.hotel.roomavailable.entity.*;
import com.ctrip.corp.bff.framework.hotel.common.constant.BooleanConstant;
import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityInfoUtil;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.CalendarUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.common.utils.date.DateUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.*;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.passenger.PassengerCertificateTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.passenger.PassengerCheckErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.util.AllowCountryCodeUtil;
import com.ctrip.corp.bff.hotel.book.common.util.MathUtils;
import com.ctrip.corp.bff.hotel.book.common.util.PassengerInfoConfigUtil;
import com.ctrip.corp.bff.hotel.book.contract.HotelPassengerCheckRequestType;
import com.ctrip.corp.bff.hotel.book.contract.HotelPassengerCheckResponseType;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfPassengerFormConfig;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.entityofpassengerform.CertificateRuleListEntity;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.entityofpassengerform.CertificatesBasedOnCountryEntity;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.entityofpassengerform.PassengerFormConfigEntity;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.entityofpassengerform.RuleInfoEntity;
import org.apache.zookeeper.Op;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author: z.c. wang
 * @Description 酒店出行人校验
 * @Date: 2025/3/14 14:58
 * @Version 1.0
 */
@Component
public class MapperOfHotelPassengerCheckResponse extends
        AbstractMapper<Tuple2<HotelPassengerCheckRequestType, QueryCheckAvailContextResponseType>, HotelPassengerCheckResponseType> {

    public static final String EN_NAME_PATTERN = "^[a-zA-Z/\\s]+$";
    private static final String NAME_TYPE_LOCAL = "LOCAL";
    private static final String NAME_TYPE_EN = "EN";
    @Autowired
    private QconfigOfPassengerFormConfig qconfigOfPassengerFormConfig;

    @Override
    protected HotelPassengerCheckResponseType convert(Tuple2<HotelPassengerCheckRequestType, QueryCheckAvailContextResponseType> tuple) {
        HotelPassengerCheckRequestType requestType = tuple.getT1();

        // 出行人信息集合
        List<PassengerInfo> passengerInfoList = Optional.ofNullable(requestType)
                .map(HotelPassengerCheckRequestType::getPassengerCheckInput)
                .map(PassengerCheckInput::getPassengerInfos)
                .orElse(null);
        // 可订反查结果
        QueryCheckAvailContextResponseType checkAvailContextResponse = tuple.getT2();

        // 出行人整体校验
        List<ErrorInfo> totalErrorInfos = new ArrayList<>();
        // 出行人为空校验
        addIfNotNull(totalErrorInfos, checkPassengerNull(passengerInfoList));
        // 出行人人数校验
        addIfNotNull(totalErrorInfos, checkPassengerNumber(passengerInfoList, checkAvailContextResponse));

        PassengerCheckOutput passengerCheckOutput = new PassengerCheckOutput();
        if (CollectionUtil.isNotEmpty(totalErrorInfos)) {
            passengerCheckOutput.setCheckPassFlag(BooleanConstant.STR_F);
            passengerCheckOutput.setErrorInfos(totalErrorInfos);
        } else {
            passengerCheckOutput.setCheckPassFlag(BooleanConstant.STR_T);
        }

        // 出行人按人校验
        List<PassengerInfoCheckOutput> passengersCheckOutputs = new ArrayList<>();
        for (PassengerInfo passengerInfo : Optional.ofNullable(passengerInfoList).orElse(Collections.emptyList())) {
            if (passengerInfo == null) {
                continue;
            }
            List<ErrorInfo> passengerErrorInfos = new ArrayList<>();
            // 姓名校验
            addIfNotNull(passengerErrorInfos, checkPassengerName(passengerInfo, checkAvailContextResponse));
            // 证件校验
            addIfNotNull(passengerErrorInfos, checkPassengerCertificate(passengerInfo, checkAvailContextResponse));
            // 国籍校验
            addIfNotNull(passengerErrorInfos, checkPassengerNationality(passengerInfo, checkAvailContextResponse));
            // 员工校验
            addIfNotNull(passengerErrorInfos, checkPassengerEmployee(passengerInfo, checkAvailContextResponse));
            // 必填校验
            addIfNotNull(passengerErrorInfos, checkPassengerMustInfo(passengerInfo));
            // 性别校验
            addIfNotNull(passengerErrorInfos, checkPassengerGender(passengerInfo, checkAvailContextResponse));
            PassengerInfoCheckOutput passengerInfoCheckOutput = new PassengerInfoCheckOutput();
            if (CollectionUtil.isNotEmpty(passengerErrorInfos)) {
                passengerInfoCheckOutput.setPassengerKey(passengerInfo.getPassengerKey());
                passengerInfoCheckOutput.setCheckPassFlag(BooleanConstant.STR_F);
                passengerInfoCheckOutput.setErrorInfos(passengerErrorInfos);
            } else {
                passengerInfoCheckOutput.setPassengerKey(passengerInfo.getPassengerKey());
                passengerInfoCheckOutput.setCheckPassFlag(BooleanConstant.STR_T);
            }

            // 证件校验提示
            passengerInfoCheckOutput.setCertificateCheckOutputs(buildCertificateCheckOutputs(
                    passengerInfo.getCertificateInfos(), checkAvailContextResponse));
            passengersCheckOutputs.add(passengerInfoCheckOutput);
        }
        passengerCheckOutput.setPassengersCheckOutputs(passengersCheckOutputs);

        HotelPassengerCheckResponseType hotelPassengerCheckResponseType = new HotelPassengerCheckResponseType();
        hotelPassengerCheckResponseType.setPassengerCheckOutput(passengerCheckOutput);

        return hotelPassengerCheckResponseType;
    }


    /**
     * 有证件预订校验默认证件
     *
     * @param certificateInfos
     * @param queryCheckAvailContextResponseType
     * @return
     */
    private List<CertificateCheckOutput> buildCertificateCheckOutputs(List<CertificateInfo> certificateInfos,
                                                                      QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
        boolean needCertificate = Optional.ofNullable(queryCheckAvailContextResponseType).map(QueryCheckAvailContextResponseType::getBookingRules)
                .map(QueryBookingRulesType::getCertificateInfo).map(QueryCertificateType::isNeedCertificate).orElse(false);
        if (!needCertificate || CollectionUtil.isEmpty(certificateInfos)) {
            return null;
        }
        List<CertificateCheckOutput> certificateCheckOutputs = new ArrayList<>();
        List<PassengerCertificateTypeEnum> supportCertificateType = Optional.ofNullable(queryCheckAvailContextResponseType).map(QueryCheckAvailContextResponseType::getBookingRules)
                .map(QueryBookingRulesType::getCertificateInfo).map(QueryCertificateType::getSupportCertificateType).orElse(Collections.emptyList())
                .stream().map(PassengerCertificateTypeEnum::findByAggCertificateType).collect(Collectors.toList());
        for (CertificateInfo certificateInfo : certificateInfos) {
            List<ErrorInfo> errorInfos = new ArrayList<>();
            CertificateCheckOutput certificateCheckOutput = new CertificateCheckOutput();
            // 证件校验
            PassengerCertificateTypeEnum byCommonCertificateType = PassengerCertificateTypeEnum.findByCommonCertificateType(
                    certificateInfo.getCertificateType());
            if (CollectionUtil.isNotEmpty(supportCertificateType) && !supportCertificateType.contains(byCommonCertificateType)) {
                certificateCheckOutput.setAvailableFlag("F");
                errorInfos.add(buildErrorInfo(PassengerCheckErrorEnum.CERTIFICATE_NOT_CONTAIN));
            }
            // 证件号码校验
            ErrorInfo checkCardNumber = checkCardNumber(certificateInfo);
            if (checkCardNumber != null) {
                errorInfos.add(checkCardNumber);
            }
            // 姓名校验
            ErrorInfo checkCardName = checkCardName(certificateInfo);
            if (checkCardName != null) {
                errorInfos.add(checkCardName);
            }
            // 证件国籍匹配
            ErrorInfo checkCardNationality = checkCardNationality(certificateInfo);
            if (checkCardNationality != null) {
                errorInfos.add(checkCardNationality);
            }
            // 有效期校验
            ErrorInfo checkCardTimeLimit = checkCardTimeLimit(certificateInfo, queryCheckAvailContextResponseType);
            if (checkCardTimeLimit != null) {
                errorInfos.add(checkCardTimeLimit);
            }
            if (CollectionUtil.isNotEmpty(errorInfos)) {
                certificateCheckOutput.setCertificateType(certificateInfo.getCertificateType());
                certificateCheckOutput.setTransferCertificateNo(certificateInfo.getTransferCertificateNo());
                certificateCheckOutput.setDefaultCertificateFlag(certificateInfo.getDefaultCertificateFlag());
                certificateCheckOutput.setErrorInfos(errorInfos);
                certificateCheckOutputs.add(certificateCheckOutput);
            }
        }

        return CollectionUtil.isEmpty(certificateCheckOutputs) ? null : certificateCheckOutputs;

    }

    /**
     * 校验证件国籍
     *
     * @param certificateInfo
     * @return
     */
    private ErrorInfo checkCardNationality(CertificateInfo certificateInfo) {
        if (certificateInfo == null) {
            return null;
        }
        String nationalityCode = Optional.ofNullable(certificateInfo.getNationalityInfo())
                .map(NationalityInfo::getNationalityCode).orElse(null);
        String certificateType = Optional.ofNullable(certificateInfo.getCertificateType()).orElse(null);
        if (StringUtil.isBlank(nationalityCode) || StringUtil.isBlank(certificateType)) {
            return null;
        }
        CertificatesBasedOnCountryEntity certificatesBasedOnCountryEntity = Optional.ofNullable(qconfigOfPassengerFormConfig)
                .map(QconfigOfPassengerFormConfig::getPassengerFormConfigEntity)
                .map(PassengerFormConfigEntity::getCertificateListBasedOnCountry).orElse(Collections.emptyList())
                .stream()
                .filter(x -> StringUtil.equalsIgnoreCase(nationalityCode, x.getCountryCode())).findFirst().orElse(null);
        if (certificatesBasedOnCountryEntity == null || CollectionUtil.isEmpty(certificatesBasedOnCountryEntity.getCertificateList())) {
            certificatesBasedOnCountryEntity = Optional.ofNullable(qconfigOfPassengerFormConfig)
                    .map(QconfigOfPassengerFormConfig::getPassengerFormConfigEntity)
                    .map(PassengerFormConfigEntity::getCertificateListBasedOnCountry).orElse(Collections.emptyList())
                    .stream()
                    .filter(x -> StringUtil.equalsIgnoreCase("default", x.getCountryCode())).findFirst().orElse(null);
        }
        if (certificatesBasedOnCountryEntity == null || CollectionUtil.isEmpty(certificatesBasedOnCountryEntity.getCertificateList())) {
            return null;
        }
        if (certificatesBasedOnCountryEntity.getCertificateList().stream().noneMatch(x -> StringUtil.equalsIgnoreCase(x.getCertificateType(), certificateType))) {
            return buildErrorInfo(PassengerCheckErrorEnum.CERTIFICATE_TYPE_NOT_SUPPORT);
        }
        return null;
    }



    /**
     *
     *
     * @param passengerInfo
     * @param queryCheckAvailContextResponseType
     * @return
     */
    private ErrorInfo checkPassengerCertificate(PassengerInfo passengerInfo,
                                                QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
        boolean needCertificate = Optional.ofNullable(queryCheckAvailContextResponseType).map(QueryCheckAvailContextResponseType::getBookingRules)
                .map(QueryBookingRulesType::getCertificateInfo).map(QueryCertificateType::isNeedCertificate).orElse(false);
        // 资源需要证件但证件为空
        CertificateInfo defaultCertificateInfo = getDefaultCertificateInfo(passengerInfo.getCertificateInfos(), queryCheckAvailContextResponseType);
        if (needCertificate && defaultCertificateInfo == null) {
            // 资源限制英文名
            List<String> guestsNameLanguages = Optional.ofNullable(queryCheckAvailContextResponseType)
                    .map(QueryCheckAvailContextResponseType::getBookingRules)
                    .map(QueryBookingRulesType::getBillingGuestInfo).map(QueryBillingGuestInfoType::getGuestsNameLanguages)
                    .orElse(Collections.emptyList());
            if (guestsNameLanguages.contains("en")) {
                return buildErrorInfo(PassengerCheckErrorEnum.CERTIFICATE_WITH_EN_NAME_NOT_CONTAIN);
            }
            return buildErrorInfo(PassengerCheckErrorEnum.CERTIFICATE_NOT_CONTAIN);
        }
        List<PassengerCertificateTypeEnum> supportCertificateType = Optional.ofNullable(queryCheckAvailContextResponseType).map(QueryCheckAvailContextResponseType::getBookingRules)
                .map(QueryBookingRulesType::getCertificateInfo).map(QueryCertificateType::getSupportCertificateType).orElse(Collections.emptyList())
                .stream().map(PassengerCertificateTypeEnum::findByAggCertificateType).collect(Collectors.toList());
        // 资源需要证件
        if (CollectionUtil.isNotEmpty(supportCertificateType)) {
            // 人下不存在资源所需证件
            List<PassengerCertificateTypeEnum> certificateInfos = Optional.ofNullable(passengerInfo.getCertificateInfos()).orElse(Collections.emptyList())
                    .stream().map(PassengerCertificateTypeEnum::findByCommonCertificateType).filter(Objects::nonNull).collect(Collectors.toList());
            if (certificateInfos.stream().noneMatch(supportCertificateType::contains)) {
                return buildErrorInfo(PassengerCheckErrorEnum.CERTIFICATE_NOT_CONTAIN);
            }
            // 人下存在资源所需证件但未选中
            PassengerCertificateTypeEnum defaultPassengerCertificateType = PassengerCertificateTypeEnum.findByCommonCertificateType(defaultCertificateInfo);
            if (!supportCertificateType.contains(defaultPassengerCertificateType)) {
                ErrorInfo errorInfo = new ErrorInfo();
                errorInfo.setErrorCode(PassengerCheckErrorEnum.CERTIFICATE_CONTAIN_CHOOSE_WRONG.name());
                errorInfo.setErrorType("ERROR");
                StringBuilder supportCertificateDescBuilder = new StringBuilder();
                for (PassengerCertificateTypeEnum passengerCertificateTypeEnum : supportCertificateType) {
                    String sharkDesc = passengerCertificateTypeEnum.getSharkDesc();
                    if (StringUtil.isBlank(sharkDesc)) {
                        continue;
                    }
                    if (!supportCertificateDescBuilder.isEmpty()) {
                        supportCertificateDescBuilder.append("/");
                    }
                    supportCertificateDescBuilder.append(sharkDesc);
                }
                errorInfo.setErrorMessage(BFFSharkUtil.getSharkValueFormat(PassengerCheckErrorEnum.CERTIFICATE_CONTAIN_CHOOSE_WRONG.getSharkKey(),
                        supportCertificateDescBuilder.toString()));
                return errorInfo;
            }
            // 证件号码校验
            ErrorInfo checkCardNumber = checkCardNumber(defaultCertificateInfo);
            if (checkCardNumber != null) {
                return checkCardNumber;
            }
            // 证件姓名校验
            ErrorInfo checkCardName = checkCardName(defaultCertificateInfo);
            if (checkCardName != null) {
                return checkCardName;
            }
            // 证件国籍匹配
            ErrorInfo checkCardNationality = checkCardNationality(defaultCertificateInfo);
            if (checkCardNationality != null) {
                return checkCardNationality;
            }
            // 证件有效期校验
            ErrorInfo checkCardTimeLimit = checkCardTimeLimit(defaultCertificateInfo, queryCheckAvailContextResponseType);
            if (checkCardTimeLimit != null) {
                return checkCardTimeLimit;
            }
        }

        return null;
    }


    /**
     * 证件号校验
     *
     * @param certificateInfo
     * @return
     */
    private ErrorInfo checkCardNumber(CertificateInfo certificateInfo) {
        if (certificateInfo == null) {
            return null;
        }
        if (StringUtil.isBlank(certificateInfo.getTransferCertificateNo())) {
            return buildErrorInfo(PassengerCheckErrorEnum.ID_NUMBER_INVALID);
        }
        CertificateRuleListEntity certificateRuleListEntity = Optional.ofNullable(qconfigOfPassengerFormConfig.getPassengerFormConfigEntity())
                .map(PassengerFormConfigEntity::getCertificateFormRuleInfos)
                .orElse(Collections.emptyList()).stream()
                .filter(x -> StringUtil.equalsIgnoreCase(x.getCertificateType(), certificateInfo.getCertificateType()))
                .findFirst().orElse(null);
        if (certificateRuleListEntity == null || CollectionUtil.isEmpty(certificateRuleListEntity.getRuleInfoList())) {
            return null;
        }
        RuleInfoEntity idNumberRule = certificateRuleListEntity.getRuleInfoList().stream()
                .filter(x -> StringUtil.equalsIgnoreCase(x.getFormType(), "ID_NUMBER")).findFirst().orElse(null);
        if (idNumberRule == null || StringUtil.isBlank(idNumberRule.getRegex())) {
            return null;
        }
        // 创建Pattern对象
        Pattern pattern = Pattern.compile(idNumberRule.getRegex());
        if (!pattern.matcher(certificateInfo.getTransferCertificateNo()).matches()) {
            return buildErrorInfo(PassengerCheckErrorEnum.ID_NUMBER_INVALID);
        }
        return null;
    }

    /**
     * 校验证件姓名
     *
     * @param certificateInfo
     * @return
     */
    private ErrorInfo checkCardName(CertificateInfo certificateInfo) {
        if (certificateInfo == null) {
            return null;
        }
        CertificateRuleListEntity certificateRuleListEntity = Optional.ofNullable(qconfigOfPassengerFormConfig)
                .map(QconfigOfPassengerFormConfig::getPassengerFormConfigEntity)
                .map(PassengerFormConfigEntity::getCertificateFormRuleInfos).orElse(Collections.emptyList())
                .stream().filter(x -> StringUtil.equalsIgnoreCase(x.getCertificateType(), certificateInfo.getCertificateType()))
                .findFirst().orElse(null);
        if (certificateRuleListEntity == null || CollectionUtil.isEmpty(certificateRuleListEntity.getRuleInfoList())) {
            return null;
        }
        RuleInfoEntity firstNameAndMiddleName = certificateRuleListEntity.getRuleInfoList().stream()
                .filter(x -> StringUtil.equalsIgnoreCase(x.getFormType(), "FIRST_NAME_AND_MIDDLE_NAME")).findFirst().orElse(null);
        if (firstNameAndMiddleName != null && StringUtil.isNotBlank(firstNameAndMiddleName.getRegex())) {
            Pattern pattern = Pattern.compile(firstNameAndMiddleName.getRegex());
            String legalFirstName = certificateInfo.getLegalFirstName();
            if (StringUtil.isNotBlank(legalFirstName) && !pattern.matcher(legalFirstName).matches()) {
                return buildErrorInfo(PassengerCheckErrorEnum.LEGAL_FIRST_NAME_INVALID);
            }
        }

        RuleInfoEntity lastName = certificateRuleListEntity.getRuleInfoList().stream()
                .filter(x -> StringUtil.equalsIgnoreCase(x.getFormType(), "LAST_NAME")).findFirst().orElse(null);
        if (lastName != null && StringUtil.isNotBlank(lastName.getRegex())) {
            Pattern pattern = Pattern.compile(lastName.getRegex());
            String legalLastName = certificateInfo.getLegalLastName();
            if (StringUtil.isNotBlank(legalLastName) && !pattern.matcher(legalLastName).matches()) {
                return buildErrorInfo(PassengerCheckErrorEnum.LEGAL_LAST_NAME_INVALID);
            }
        }
        return null;
    }

    /**
     * 证件有效期校验
     *
     * @param certificateInfo
     * @param queryCheckAvailContextResponseType
     * @return
     */
    private ErrorInfo checkCardTimeLimit(CertificateInfo certificateInfo,
                                         QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
        if (certificateInfo == null) {
            return null;
        }
        // 证件有效期校验
        String cardTimeLimit = StringUtil.subPre(
                Optional.ofNullable(certificateInfo).map(CertificateInfo::getCardTimeLimit).orElse(null),
                DateUtil.YYYY_MM_DD.length());
        String checkInDate = StringUtil.subPre(Optional.ofNullable(queryCheckAvailContextResponseType)
                        .map(QueryCheckAvailContextResponseType::getBaseInfo).map(BookBaseInfoEntity::getStartTime).orElse(null),
                DateUtil.YYYY_MM_DD.length());
        if (StringUtil.isNotBlank(cardTimeLimit) && StringUtil.isNotEmpty(checkInDate)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD);
            LocalDate cardLimitTimeLocalDate = LocalDate.parse(cardTimeLimit, formatter);
            LocalDate lastDeptLocalDate = LocalDate.parse(checkInDate, formatter);
            if (cardLimitTimeLocalDate.compareTo(lastDeptLocalDate) < 0) {
                return buildErrorInfo(PassengerCheckErrorEnum.CERTIFICATE_EXPIRATION_EXPIRED);
            }
            // CHECK 证件有效期是否过期
            if (cardLimitTimeLocalDate.minusMonths(6).compareTo(lastDeptLocalDate) < 0) {
                return buildErrorInfo(PassengerCheckErrorEnum.CERTIFICATE_EXPIRATION_EXPIRED_WARNING);
            }

        }
        return null;
    }

    /**
     * 出行人员工校验
     *
     * @param passengerInfo
     * @param queryCheckAvailContextResponseType
     * @return
     */
    private ErrorInfo checkPassengerEmployee(PassengerInfo passengerInfo,
                                             QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
        List<String> restrictRuleList = Optional.ofNullable(queryCheckAvailContextResponseType)
                .map(QueryCheckAvailContextResponseType::getBookingRules).map(QueryBookingRulesType::getRestrictRuleList).orElse(Collections.emptyList());
        if (restrictRuleList.contains("EMPLOYEE_BOOKING") && StringUtil.equalsIgnoreCase(passengerInfo.getEmployee(), "F")) {
            return buildErrorInfo(PassengerCheckErrorEnum.PASSENGER_NOT_EMPLOYEE);
        }
        return null;
    }

    /**
     * 出行人必填信息校验
     *
     * @param passengerInfo
     * @return
     */
    private ErrorInfo checkPassengerMustInfo(PassengerInfo passengerInfo) {
        // 邮箱
        String transferEmail = Optional.ofNullable(passengerInfo.getEmailInfo()).map(EmailInfo::getTransferEmail).orElse(null);
        if (StringUtil.isBlank(transferEmail)) {
            return buildErrorInfo(PassengerCheckErrorEnum.EMAIL_NULL);
        }
        return null;
    }

    /**
     * 出行人性别校验
     *
     * @param passengerInfo
     * @param queryCheckAvailContextResponseType
     * @return
     */
    private ErrorInfo checkPassengerGender(PassengerInfo passengerInfo,
                                           QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
        Integer vendorId = Optional.ofNullable(queryCheckAvailContextResponseType).map(QueryCheckAvailContextResponseType::getHotelInfo)
                .map(BookHotelInfoEntity::getVendorId).orElse(null);
        String gender = Optional.ofNullable(passengerInfo).map(PassengerInfo::getPassengerBasicInfo)
                .map(PassengerBasicInfo::getGender).orElse(null);
        // bagashi资源一定需要性别
        if (TemplateNumberUtil.getValue(vendorId) == CommonConstant.VENDOR_ID_BAGASHI && !isGenderMaleOrFemale(gender)) {
            return buildErrorInfo(PassengerCheckErrorEnum.GENDER_NULL);
        }
        return null;
    }

    private boolean isGenderMaleOrFemale(String gender) {
        return StringUtil.equalsIgnoreCase(gender, CommonConstant.GENDER_MALE)
                || StringUtil.equalsIgnoreCase(gender, CommonConstant.GENDER_FEMALE);
    }


    /**
     * 国籍校验
     *
     * @param passengerInfo
     * @param queryCheckAvailContextResponseType
     * @return
     */
    private ErrorInfo checkPassengerNationality(PassengerInfo passengerInfo,
                                                QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
        String countryCode = null;
        boolean needCertificate = Optional.ofNullable(queryCheckAvailContextResponseType).map(QueryCheckAvailContextResponseType::getBookingRules)
                .map(QueryBookingRulesType::getCertificateInfo).map(QueryCertificateType::isNeedCertificate).orElse(false);
        CertificateInfo defaultCertificateInfo = getDefaultCertificateInfo(passengerInfo.getCertificateInfos(), queryCheckAvailContextResponseType);
        if (needCertificate) {
            // 有证件场景取选证件国籍兜底
            countryCode = Optional.ofNullable(defaultCertificateInfo).map(CertificateInfo::getNationalityInfo)
                            .map(NationalityInfo::getNationalityCode).orElse(null);
        } else {
            // 无证件预订取OTHER证件的国籍
            countryCode = Optional.ofNullable(defaultCertificateInfo).map(CertificateInfo::getNationalityInfo)
                    .map(NationalityInfo::getNationalityCode).orElse(null);

        }

        // 马来西亚资源国籍校验
        ErrorInfo checkMalaysiaHotelPassengerNationality = checkMalaysiaHotelPassengerNationality(queryCheckAvailContextResponseType, countryCode);
        if (checkMalaysiaHotelPassengerNationality != null) {
            return checkMalaysiaHotelPassengerNationality;
        }

        // 中宾内宾国籍校验
        ErrorInfo checkOnlyForCNHotelPassengerNationality = checkOnlyForCNHotelPassengerNationality(queryCheckAvailContextResponseType, countryCode);
        if (checkOnlyForCNHotelPassengerNationality != null) {
            return checkOnlyForCNHotelPassengerNationality;
        }
        return null;
    }

    private ErrorInfo checkOnlyForCNHotelPassengerNationality(QueryCheckAvailContextResponseType queryCheckAvailContextResponseType,
                                                             String passengerCountryCode) {
        List<String> allowCountryCodeList = Optional.ofNullable(queryCheckAvailContextResponseType).map(QueryCheckAvailContextResponseType::getBookingRules)
                .map(QueryBookingRulesType::getNationalityRestrictionInfo).map(NationalityRestrictionType::getAllowCountryCodeList).orElse(null);

        if (!AllowCountryCodeUtil.isOnlyForCnGuest(allowCountryCodeList) && !AllowCountryCodeUtil.isOnlyForCnGatGuest(allowCountryCodeList)) {
            return null;
        }

        // 出行人国籍二字码
        if (StringUtil.isBlank(passengerCountryCode)) {
            return buildErrorInfo(PassengerCheckErrorEnum.NATIONALITY_NULL);
        }

        if (CollectionUtil.isNotEmpty(allowCountryCodeList)) {
            if (!allowCountryCodeList.contains(passengerCountryCode)) {
                if (AllowCountryCodeUtil.isOnlyForCnGuest(allowCountryCodeList)) {
                    return buildErrorInfo(PassengerCheckErrorEnum.ONLY_FOR_CN_GUEST);
                }
                if (AllowCountryCodeUtil.isOnlyForCnGatGuest(allowCountryCodeList)) {
                    return buildErrorInfo(PassengerCheckErrorEnum.ONLY_FOR_CN_GAT_GUEST);
                }
            }
        }
        return null;
    }


    private ErrorInfo checkMalaysiaHotelPassengerNationality(QueryCheckAvailContextResponseType queryCheckAvailContextResponseType,
                                                             String passengerCountryCode) {
        // 非马来西亚，不校验
        Integer hotelCountryId = Optional.ofNullable(queryCheckAvailContextResponseType).map(QueryCheckAvailContextResponseType::getHotelInfo)
                .map(BookHotelInfoEntity::getGeographicalInfo).map(GeographicalSituationInfo::getCountryInfo).map(RegionInfo::getId).orElse(null);
        if (!PassengerInfoConfigUtil.isMalaysiaCountry(hotelCountryId)) {
            return null;
        }

        // 出行人国籍二字码
        if (StringUtil.isBlank(passengerCountryCode)) {
            return buildErrorInfo(PassengerCheckErrorEnum.NATIONALITY_NULL);
        }

        // 国籍白名单二字码
        List<String> allowCountryCodeList = Optional.ofNullable(queryCheckAvailContextResponseType).map(QueryCheckAvailContextResponseType::getBookingRules)
                .map(QueryBookingRulesType::getNationalityRestrictionInfo).map(NationalityRestrictionType::getAllowCountryCodeList).orElse(null);
        if (CollectionUtil.isNotEmpty(allowCountryCodeList)) {
            if (!allowCountryCodeList.contains(passengerCountryCode)) {
                return buildErrorInfo(PassengerCheckErrorEnum.NATIONALITY_NOT_ALLOW);
            }
        }

        // 国籍黑名单二字码
        List<String> blockCountryCodeList = Optional.ofNullable(queryCheckAvailContextResponseType).map(QueryCheckAvailContextResponseType::getBookingRules)
                .map(QueryBookingRulesType::getNationalityRestrictionInfo).map(NationalityRestrictionType::getBlockCountryCodeList).orElse(null);
        if (CollectionUtil.isNotEmpty(blockCountryCodeList)) {
            if (blockCountryCodeList.contains(passengerCountryCode)) {
                return buildErrorInfo(PassengerCheckErrorEnum.NATIONALITY_NOT_ALLOW);
            }
        }
        return null;
    }


    /**
     * 姓名校验
     *
     * @param passengerInfo
     * @param queryCheckAvailContextResponseType
     * @return
     */
    private ErrorInfo checkPassengerName(PassengerInfo passengerInfo,
                                         QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
        boolean needCertificate = Optional.ofNullable(queryCheckAvailContextResponseType).map(QueryCheckAvailContextResponseType::getBookingRules)
                .map(QueryBookingRulesType::getCertificateInfo).map(QueryCertificateType::isNeedCertificate).orElse(false);
        boolean employee = StringUtil.equalsIgnoreCase(passengerInfo.getEmployee(), "T");
        // 姓名为空校验
        if (!passengerHasName(passengerInfo, queryCheckAvailContextResponseType)) {
            return needCertificate ?
                    buildErrorInfo(PassengerCheckErrorEnum.CERTIFICATE_LACK_NAME) : buildErrorInfo(PassengerCheckErrorEnum.PASSENGER_NAME_NULL);
        }
        // 海外、港澳台、amadeus需要英文名
        Integer cityId = Optional.ofNullable(queryCheckAvailContextResponseType).map(QueryCheckAvailContextResponseType::getHotelInfo)
                .map(BookHotelInfoEntity::getGeographicalInfo).map(GeographicalSituationInfo::getCityInfo)
                .map(RegionInfo::getId).orElse(null);
        boolean amadeus = StringUtil.equalsIgnoreCase("Amadeus", Optional.ofNullable(queryCheckAvailContextResponseType)
                .map(QueryCheckAvailContextResponseType::getRoomInfo).map(BookRoomInfoEntity::getGdsType).orElse(null));
        if (CityInfoUtil.oversea(cityId)
                || CityInfoUtil.hmt(cityId)
                || amadeus) {
            if (!passengerHasEnName(passengerInfo, queryCheckAvailContextResponseType)) {
                return needCertificate ?
                        buildErrorInfo(PassengerCheckErrorEnum.CERTIFICATE_LACK_EN_NAME) : buildErrorInfo(PassengerCheckErrorEnum.PASSENGER_EN_NAME_NULL);
            }
        }
        List<String> guestsNameLanguages = Optional.ofNullable(queryCheckAvailContextResponseType).map(QueryCheckAvailContextResponseType::getBookingRules)
                .map(QueryBookingRulesType::getBillingGuestInfo).map(QueryBillingGuestInfoType::getGuestsNameLanguages).orElse(Collections.emptyList());
        if (guestsNameLanguages.contains("en") && !passengerHasEnName(passengerInfo, queryCheckAvailContextResponseType)) {
            return needCertificate ?
                    buildErrorInfo(PassengerCheckErrorEnum.CERTIFICATE_LACK_EN_NAME) : buildErrorInfo(PassengerCheckErrorEnum.PASSENGER_EN_NAME_NULL);
        }
        // 有证件预定非员工姓名校验
        PassengerCertificateTypeEnum defaultCertificateType = PassengerCertificateTypeEnum.findByCommonCertificateType(
                Optional.ofNullable(passengerInfo.getCertificateInfos()).orElse(Collections.emptyList()).stream()
                .filter(x -> StringUtil.equalsIgnoreCase("T", x.getDefaultCertificateFlag()))
                .findFirst().orElse(new CertificateInfo()).getCertificateType());
        String defaultNameType = Optional.ofNullable(passengerInfo.getPassengerBasicInfo()).map(PassengerBasicInfo::getDefaultNameType).orElse(null);
        if (needCertificate && !employee) {
            // 身份证只允许当地名
            if (defaultCertificateType == PassengerCertificateTypeEnum.IDENTITY_CARD && !StringUtil.equalsIgnoreCase(defaultNameType, NAME_TYPE_LOCAL)) {
                return buildErrorInfo(PassengerCheckErrorEnum.PASSENGER_NEED_LOCAL_NAME);
            }
            // 护照只允许英文名
            if (defaultCertificateType == PassengerCertificateTypeEnum.PASSPORT && !StringUtil.equalsIgnoreCase(defaultNameType, NAME_TYPE_EN)) {
                return buildErrorInfo(PassengerCheckErrorEnum.PASSENGER_NEED_EN_NAME);
            }
        }

        return null;
    }


    private boolean passengerHasName(PassengerInfo passengerInfo, QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
        if (passengerInfo == null) {
            return false;
        }
        CertificateInfo defaultCertificateInfo = getDefaultCertificateInfo(passengerInfo.getCertificateInfos(), queryCheckAvailContextResponseType);
        // 优先取证件名
        if (defaultCertificateInfo != null
                && !namesAllEmpty(defaultCertificateInfo.getLegalFirstName(), defaultCertificateInfo.getLegalMiddleName(), defaultCertificateInfo.getLegalLastName())) {
            return true;
        }
        PassengerBasicInfo passengerBasicInfo = passengerInfo.getPassengerBasicInfo();

        String defaultNameType = Optional.ofNullable(passengerBasicInfo).map(PassengerBasicInfo::getDefaultNameType).orElse(null);
        if (StringUtil.equalsIgnoreCase("EN", defaultNameType) && passengerBasicInfo != null && passengerBasicInfo.getEnName() != null &&
                !namesAllEmpty(passengerBasicInfo.getEnName().getFirstName(), null, passengerBasicInfo.getEnName().getFirstName())) {
            return true;
        }
        if (StringUtil.equalsIgnoreCase("LOCAL", defaultNameType) && passengerBasicInfo != null && passengerBasicInfo.getLocalName() != null &&
                !namesAllEmpty(passengerBasicInfo.getLocalName().getFirstName(), null, passengerBasicInfo.getLocalName().getFirstName())) {
            return true;
        }
        return false;
    }

    private boolean passengerHasEnName(PassengerInfo passengerInfo, QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
        if (passengerInfo == null) {
            return false;
        }
        CertificateInfo defaultCertificateInfo = getDefaultCertificateInfo(passengerInfo.getCertificateInfos(), queryCheckAvailContextResponseType);
        // 优先取证件名
        if (defaultCertificateInfo != null
                && !namesAllEmpty(defaultCertificateInfo.getLegalFirstName(), defaultCertificateInfo.getLegalMiddleName(), defaultCertificateInfo.getLegalLastName())) {
            return namesAllInEn(defaultCertificateInfo.getLegalFirstName(), defaultCertificateInfo.getLegalMiddleName(), defaultCertificateInfo.getLegalLastName());
        }
        PassengerBasicInfo passengerBasicInfo = passengerInfo.getPassengerBasicInfo();

        String defaultNameType = Optional.ofNullable(passengerBasicInfo).map(PassengerBasicInfo::getDefaultNameType).orElse(null);
        if (StringUtil.equalsIgnoreCase("EN", defaultNameType) && passengerBasicInfo != null && passengerBasicInfo.getEnName() != null &&
                !namesAllEmpty(passengerBasicInfo.getEnName().getFirstName(), null, passengerBasicInfo.getEnName().getFirstName())) {
            return namesAllInEn(passengerBasicInfo.getEnName().getFirstName(), null, passengerBasicInfo.getEnName().getFirstName());
        }
        if (StringUtil.equalsIgnoreCase("LOCAL", defaultNameType) && passengerBasicInfo != null && passengerBasicInfo.getLocalName() != null &&
                !namesAllEmpty(passengerBasicInfo.getLocalName().getFirstName(), null, passengerBasicInfo.getLocalName().getFirstName())) {
            return namesAllInEn(passengerBasicInfo.getLocalName().getFirstName(), null, passengerBasicInfo.getLocalName().getFirstName());
        }
        return false;
    }


    private boolean namesAllEmpty(String firstName, String lastName, String middleName) {
        return StringUtil.isBlank(firstName) && StringUtil.isBlank(middleName) && StringUtil.isBlank(lastName);
    }

    private boolean namesAllInEn(String firstName, String lastName, String middleName) {
        if (StringUtil.isNotBlank(firstName) && !firstName.matches(EN_NAME_PATTERN)) {
            return false;
        }
        if (StringUtil.isNotBlank(middleName) && !middleName.matches(EN_NAME_PATTERN)) {
            return false;
        }
        if (StringUtil.isNotBlank(lastName) && !lastName.matches(EN_NAME_PATTERN)) {
            return false;
        }
        return true;
    }


    /**
     * 出行人为空校验
     *
     * @param passengerInfoList
     * @return
     */
    private ErrorInfo checkPassengerNull(List<PassengerInfo> passengerInfoList) {
        if (CollectionUtil.isEmpty(passengerInfoList)) {
            return buildErrorInfo(PassengerCheckErrorEnum.PASSENGER_EMPTY_INVALID);
        }
        return null;
    }

    /**
     * 出行人人数校验
     *
     * @param passengerInfoList 出行人列表
     * @param queryCheckAvailContextResponseType 可订反查
     * @return
     */
    private ErrorInfo checkPassengerNumber(List<PassengerInfo> passengerInfoList,
                                           QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
        if (CollectionUtil.isEmpty(passengerInfoList)) {
            return null;
        }
        // 房间可容纳人数
        final int capacity = Optional.ofNullable(queryCheckAvailContextResponseType)
                .map(QueryCheckAvailContextResponseType::getRoomInfo).map(BookRoomInfoEntity::getMaxGuestCountPerRoom).orElse(0);
        // 预定房间数
        final int roomQuantity = Optional.ofNullable(queryCheckAvailContextResponseType)
                .map(QueryCheckAvailContextResponseType::getBaseInfo).map(BookBaseInfoEntity::getQuantity).orElse(0);
        if (MathUtils.isLessOrEqualsZero(capacity) || MathUtils.isLessOrEqualsZero(roomQuantity)) {
            return null;
        }
        final int passengerSize = passengerInfoList.size();
        final int maxPassengerSize = roomQuantity * capacity;
        if (passengerSize > maxPassengerSize) {
            return buildErrorInfoOfPassengerNum(PassengerCheckErrorEnum.PASSENGER_OVER_MAX, maxPassengerSize);
        }
        if (passengerSize < roomQuantity) {
            return buildErrorInfoOfPassengerNum(PassengerCheckErrorEnum.PASSENGER_OVER_MIN, roomQuantity);
        }

        return null;
    }

    /**
     * 默认证件
     *
     * @param certificateInfoList
     * @return
     */
    private CertificateInfo getDefaultCertificateInfo(List<CertificateInfo> certificateInfoList,
                                                      QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
        if (CollectionUtil.isEmpty(certificateInfoList)) {
            return null;
        }
        // 默认证件
        CertificateInfo defaultCertificateInfo = certificateInfoList.stream().filter(x -> StringUtil.equalsIgnoreCase("T", x.getDefaultCertificateFlag()))
                .findFirst().orElse(null);

        // 是否无证件预订
        boolean needCertificate = Optional.ofNullable(queryCheckAvailContextResponseType).map(QueryCheckAvailContextResponseType::getBookingRules)
                .map(QueryBookingRulesType::getCertificateInfo).map(QueryCertificateType::isNeedCertificate).orElse(false);
        if (!needCertificate) {
            return certificateInfoList.stream().filter(x -> StringUtil.equalsIgnoreCase("OTHERDOCUMENT", x.getCertificateType()))
                    .findFirst().orElse(defaultCertificateInfo);
        }
        return defaultCertificateInfo;
    }

    private ErrorInfo buildErrorInfoOfPassengerNum(PassengerCheckErrorEnum passengerCheckErrorEnum, int num) {
        if (passengerCheckErrorEnum == null) {
            return null;
        }
        ErrorInfo errorInfo = new ErrorInfo();
        errorInfo.setErrorCode(passengerCheckErrorEnum.name());
        errorInfo.setErrorType(passengerCheckErrorEnum.getErrorType());
        String sharkKey = passengerCheckErrorEnum.getSharkKey();
        String template = BFFSharkUtil.getTemplate(sharkKey, new BigDecimal(num));
        String errorMessage = template.replace("%0$s", String.valueOf(num));
        errorInfo.setErrorMessage(errorMessage);
        return errorInfo;
    }

    private ErrorInfo buildErrorInfo(PassengerCheckErrorEnum passengerCheckErrorEnum) {
        if (passengerCheckErrorEnum == null) {
            return null;
        }
        ErrorInfo errorInfo = new ErrorInfo();
        errorInfo.setErrorCode(passengerCheckErrorEnum.name());
        errorInfo.setErrorType(passengerCheckErrorEnum.getErrorType());
        errorInfo.setErrorMessage(passengerCheckErrorEnum.getErrorMessage());
        return errorInfo;
    }


    public static <T> void addIfNotNull(List<T> list, T element) {
        Optional.ofNullable(element).ifPresent(list::add);
    }

    @Override
    protected ParamCheckResult check(Tuple2<HotelPassengerCheckRequestType, QueryCheckAvailContextResponseType> tuple) {
        return null;
    }
}
