package com.ctrip.corp.bff.hotel.book.processor;

import com.ctrip.basebiz.geolocation.service.GetAllNationalityRequestType;
import com.ctrip.basebiz.geolocation.service.GetAllNationalityResponseType;
import com.ctrip.basebiz.iplocation.proto.GetIpInfoRequestTypeV2;
import com.ctrip.basebiz.iplocation.proto.GetIpInfoResponseTypeV2;
import com.ctrip.corp.agg.commonws.entity.GetPackageRoomListRequestType;
import com.ctrip.corp.agg.commonws.entity.GetPackageRoomListResponseType;
import com.ctrip.corp.agg.commonws.pkg.room.snapshot.entity.GetPackageRoomSnapshotRequestType;
import com.ctrip.corp.agg.commonws.pkg.room.snapshot.entity.GetPackageRoomSnapshotResponseType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookingRulesType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckAvailRequestType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckAvailResponseType;
import com.ctrip.corp.agg.hotel.salestrategy.entity.CalculateTravelRewardsRequestType;
import com.ctrip.corp.agg.hotel.salestrategy.entity.CalculateTravelRewardsResponseType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyRequestType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType;
import com.ctrip.corp.agg.hotel.tmc.entity.GetHotelTravelPolicyRequestType;
import com.ctrip.corp.agg.hotel.tmc.entity.GetHotelTravelPolicyResponseType;
import com.ctrip.corp.bff.framework.hotel.common.builder.GetTravelPolicyRequest;
import com.ctrip.corp.bff.framework.hotel.common.builder.SearchApprovalRequest;
import com.ctrip.corp.bff.framework.hotel.common.mapper.*;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfPersonalAccountConfig;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfRoomNumberInfoConfig;
import com.ctrip.corp.bff.framework.hotel.common.util.HotelBookPassengerInputUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.WaitFutureUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfHotelTravelPolicy;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfSearchApproval;
import com.ctrip.corp.bff.framework.hotel.entity.contract.*;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.BookInitToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.utils.ResourceTokenUtil;
import com.ctrip.corp.bff.framework.template.common.qconfig.QConfigUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ResourceTokenInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.handler.WaitFuture;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.*;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import com.ctrip.corp.bff.hotel.book.common.builder.BookingInitAssembleRequest;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.mapper.MapperOfCheckTravelPolicyRequestType;
import com.ctrip.corp.bff.hotel.book.common.mapper.MapperOfGetPlatformRelationByUidRequestType;
import com.ctrip.corp.bff.hotel.book.common.mapper.MapperOfPayConfigRequestType;
import com.ctrip.corp.bff.hotel.book.common.mapper.MapperOfQueryIndividualAccountRequestType;
import com.ctrip.corp.bff.hotel.book.common.util.*;
import com.ctrip.corp.bff.hotel.book.common.util.BookingInitProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.util.BookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckTravelPolicy;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitResponseType;
import com.ctrip.corp.bff.hotel.book.handler.appmanagerservice.HandlerOfGetCorpUserInfoDetailList;
import com.ctrip.corp.bff.hotel.book.handler.bbzmbrcommonpassenger.HandlerOfGetCommonPassenger;
import com.ctrip.corp.bff.hotel.book.handler.corp4jservice.HandlerOfGetCorpInfo;
import com.ctrip.corp.bff.hotel.book.handler.corp4jservice.HandlerOfGetReasoncodes;
import com.ctrip.corp.bff.hotel.book.handler.corpaccountqueryservice.HandlerOfGeneralSearchAccountInfo;
import com.ctrip.corp.bff.hotel.book.handler.corpaccountqueryservice.HandlerOfGetAuthDelayConfig;
import com.ctrip.corp.bff.hotel.book.handler.corpaccountqueryservice.HandlerOfQueryIndividualAccount;
import com.ctrip.corp.bff.hotel.book.handler.corpagghotelexpenseservice.HandlerOfCalculateServiceChargeV2;
import com.ctrip.corp.bff.hotel.book.handler.corpagghotelexpenseservice.HandlerOfGetRoomChargePolicyList;
import com.ctrip.corp.bff.hotel.book.handler.corpagghotelsalestrategyservice.HandlerOfCalculateTravelRewards;
import com.ctrip.corp.bff.hotel.book.handler.corpagghoteltmcservice.HandlerOfCheckTravelPolicy;
import com.ctrip.corp.bff.hotel.book.handler.corpagghoteltmcservice.HandlerOfGetHotelTravelPolicy;
import com.ctrip.corp.bff.hotel.book.handler.corpauthoritymanageservice.HandlerOfQueryAuth;
import com.ctrip.corp.bff.hotel.book.handler.corpbffspecificservice.HandlerOfApprovalTextInfo;
import com.ctrip.corp.bff.hotel.book.handler.corpbffspecificservice.HandlerOfBatchApprovalDefault;
import com.ctrip.corp.bff.hotel.book.handler.corpbfftoolsservice.HandlerOfCountryQuery;
import com.ctrip.corp.bff.hotel.book.handler.corpbfftoolsservice.HandlerOfMaskData;
import com.ctrip.corp.bff.hotel.book.handler.corpconfigurationservice.HandlerOfGetSubAccountConfig;
import com.ctrip.corp.bff.hotel.book.handler.corphotelbookcommonws.HandlerOfDistributePaymentAmount;
import com.ctrip.corp.bff.hotel.book.handler.corphotelbookcommonws.HandlerOfGetCancelPolicyDesc;
import com.ctrip.corp.bff.hotel.book.handler.corphotelbookcommonws.HandlerOfGetPackageRoomList;
import com.ctrip.corp.bff.hotel.book.handler.corphotelbookcommonws.HandlerOfGetPackageRoomSnapshot;
import com.ctrip.corp.bff.hotel.book.handler.corphotelbookcommonws.HandlerOfGetSupportedInvoiceType;
import com.ctrip.corp.bff.hotel.book.handler.corphotelbookcommonws.HandlerOfGetSupportedPaymentMethod;
import com.ctrip.corp.bff.hotel.book.handler.corphotelbookqueryservice.HandlerOfGetCityBaseInfo;
import com.ctrip.corp.bff.hotel.book.handler.corphotelbookqueryservice.HandlerOfGetHotelDetailInfo;
import com.ctrip.corp.bff.hotel.book.handler.corphotelmemberservice.HandlerOfSearchRegistrationFields;
import com.ctrip.corp.bff.hotel.book.handler.corphotelorderdetailservice.HandlerOfQueryOrderSettings;
import com.ctrip.corp.bff.hotel.book.handler.corphotelroomavailableservice.HandlerOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.handler.corpsettlementinvoiceservice.HandlerOfCorpOrderInvoiceInfo;
import com.ctrip.corp.bff.hotel.book.handler.corpuserinfoservice4j.HandlerOfGetContactInvoiceDefaultInfo;
import com.ctrip.corp.bff.hotel.book.handler.corpuserinfoservice4j.HandlerOfGetCorpUserHotelVipCard;
import com.ctrip.corp.bff.hotel.book.handler.corpuserinfoservice4j.HandlerOfGetCorpUserInfo;
import com.ctrip.corp.bff.hotel.book.handler.corpuserinfoservice4j.HandlerOfGetPlatformRelationByUid;
import com.ctrip.corp.bff.hotel.book.handler.corpuserinfoservice4j.HandlerOfGetUserAddressInfo;
import com.ctrip.corp.bff.hotel.book.handler.geolocationservice.HandlerOfGetAllNationality;
import com.ctrip.corp.bff.hotel.book.handler.handlerofgroup4jservice.HandlerOfQueryBizModeBindRelation;
import com.ctrip.corp.bff.hotel.book.handler.htlconfigurationmiddleservice.HandlerOfGetGroupVipPackage;
import com.ctrip.corp.bff.hotel.book.handler.iplocationservice.HandlerOfGetIpInfo;
import com.ctrip.corp.bff.hotel.book.handler.orderdataaggregationqueryservice.HandlerOfQueryHotelOrderData;
import com.ctrip.corp.bff.hotel.book.handler.ordermanagementcenterhotelservice.HandlerOfXProductEnquire;
import com.ctrip.corp.bff.hotel.book.handler.orderpaymentcentertransactionservice.HandlerOfPayConfig;
import com.ctrip.corp.bff.hotel.book.handler.orderreimbursementservice.HandlerOfReimbursementQuery;
import com.ctrip.corp.bff.hotel.book.handler.preapprovalservice.HandlerOfSearchApproval;
import com.ctrip.corp.bff.hotel.book.handler.rewardservice.HandlerOfQueryMrgMemberUserInfo;
import com.ctrip.corp.bff.hotel.book.handler.triporderservice.HandlerOfGetTripBookingInfos;
import com.ctrip.corp.bff.hotel.book.handler.triporderservice.HandlerOfSearchTripBasicInfo;
import com.ctrip.corp.bff.hotel.book.handler.triporderservice.HandlerOfSearchTripDetail;
import com.ctrip.corp.bff.hotel.book.handler.userinfoqueryservice.HandlerOfBatchSearchClientsInfo;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfApprovalOutput;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfApprovalTextInfoRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfBatchApprovalDefaultRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfBatchSearchClientsInfoRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfBuildTravelPolicyRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfCalculateServiceChargeV2RequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfCalculateTravelRewardsRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfCheckAvailRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfCorpOrderInvoiceDetailInfoQueryRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfDistributePaymentAmountRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetCancelPolicyDescRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetContactInvoiceDefaultInfoRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetCorpUserHotelVipCardRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetCorpUserInfoByPolicyRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetGroupVipPackageRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetHotelDetailInfoRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetPackageRoomListRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetPackageRoomSnapshotRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetRoomChargePolicyListRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetSupportedInvoiceRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetSupportedPaymentMethodRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetTripBookingInfosRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetUserAddressInfoRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfQueryCtripMrgMemberUserInfoRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfQueryHotelOrderDataRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfQueryOrderSettingsRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfReimbursementQueryRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfSearchRegistrationFieldsRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfXProductEnquireRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.*;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.response.MapperOfBookingInitResponse;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.response.MapperOfGetSupportedPaymentMethodResponseType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.*;
import com.ctrip.corp.bff.hotel.book.qconfig.*;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.MemberBonusRuleEntry;
import com.ctrip.corp.bff.specific.contract.ApprovalTextInfoRequestType;
import com.ctrip.corp.bff.specific.contract.ApprovalTextInfoResponseType;
import com.ctrip.corp.bff.specific.contract.BatchApprovalDefaultRequestType;
import com.ctrip.corp.bff.specific.contract.BatchApprovalDefaultResponseType;
import com.ctrip.corp.bff.tools.contract.CountryQueryRequestType;
import com.ctrip.corp.bff.tools.contract.CountryQueryResponseType;
import com.ctrip.corp.bff.tools.contract.MaskDataRequestType;
import com.ctrip.corp.bff.tools.contract.MaskDataResponseType;
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigRequestType;
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.SearchApprovalRequestType;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.SearchApprovalResponseType;
import com.ctrip.corp.foundation.common.util.Null;
import com.ctrip.corp.hotel.book.query.entity.*;
import com.ctrip.corp.hotelbook.commonws.entity.DistributePaymentAmountRequestType;
import com.ctrip.corp.hotelbook.commonws.entity.DistributePaymentAmountResponseType;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedInvoiceTypeRequestType;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedInvoiceTypeResponseType;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodRequestType;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodResponseType;
import com.ctrip.corp.order.data.aggregation.query.contract.*;
import com.ctrip.corp.order.managementcenter.hotel.service.contract.XProductEnquireRequestType;
import com.ctrip.corp.order.managementcenter.hotel.service.contract.XProductEnquireResponseType;
import com.ctrip.hotel.order.rewardservice.contract.CtripMemberUserInfoResponse;
import com.ctrip.hotel.order.rewardservice.contract.QueryCtripMrgMemberUserInfoRequest;
import com.ctrip.hotel.wireless.htlgetgroupvippackagefunction.soa.contract.GetGroupVipPackageRequestType;
import com.ctrip.hotel.wireless.htlgetgroupvippackagefunction.soa.contract.GetGroupVipPackageResponseType;
import com.ctrip.model.CalculateServiceChargeV2RequestType;
import com.ctrip.model.CalculateServiceChargeV2ResponseType;
import com.ctrip.model.GetCancelPolicyDescRequestType;
import com.ctrip.model.GetCancelPolicyDescResponseType;
import com.ctrip.model.GetRoomChargePolicyListRequestType;
import com.ctrip.model.GetRoomChargePolicyListResponseType;
import com.ctrip.model.SearchRegistrationFieldsRequestType;
import com.ctrip.model.SearchRegistrationFieldsResponseType;
import com.ctrip.order.reimbursement.ReimbursementInfoType;
import com.ctrip.order.reimbursement.ReimbursementQueryRequestType;
import com.ctrip.order.reimbursement.ReimbursementQueryResponseType;
import com.ctrip.soa._21234.GetTripBookingInfosRequestType;
import com.ctrip.soa._21234.GetTripBookingInfosResponseType;
import com.ctrip.soa._21234.SearchTripBasicInfoRequestType;
import com.ctrip.soa._21234.SearchTripBasicInfoResponseType;
import com.ctrip.soa._21234.SearchTripDetailRequestType;
import com.ctrip.soa._21234.SearchTripDetailResponseType;
import com.ctrip.soa._21685.PayConfigRequestType;
import com.ctrip.soa._21685.PayConfigResponseType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.QueryOrderSettingsRequestType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.QueryOrderSettingsResponseType;
import com.google.common.collect.Lists;
import corp.settlement.service.invoice.corpinvoice.CorpOrderInvoiceDetailInfoQueryRequest;
import corp.settlement.service.invoice.corpinvoice.CorpOrderInvoiceDetailInfoQueryResponse;
import corp.user.service.AppManagerservice.GetCorpUserInfoDetailListRequestType;
import corp.user.service.AppManagerservice.GetCorpUserInfoDetailListResponseType;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoRequestType;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType;
import corp.user.service.CorpAccountQueryService.GetAuthDelayRequestType;
import corp.user.service.CorpAccountQueryService.GetAuthDelayResponseType;
import corp.user.service.CorpAccountQueryService.QueryIndividualAccountRequestType;
import corp.user.service.CorpAccountQueryService.QueryIndividualAccountResponseType;
import corp.user.service.UserInfoQueryService.BatchSearchClientsInfoRequestType;
import corp.user.service.UserInfoQueryService.BatchSearchClientsInfoResponseType;
import corp.user.service.authorityManage.QueryAuthRequestType;
import corp.user.service.authorityManage.QueryAuthResponseType;
import corp.user.service.corp4jservice.GetCorpInfoRequestType;
import corp.user.service.corp4jservice.GetCorpInfoResponseType;
import corp.user.service.corp4jservice.GetReasoncodesRequestType;
import corp.user.service.corp4jservice.GetReasoncodesResponseType;
import corp.user.service.corpUserInfoService.GetContactInvoiceDefaultInfoRequestType;
import corp.user.service.corpUserInfoService.GetContactInvoiceDefaultInfoResponseType;
import corp.user.service.corpUserInfoService.GetCorpUserHotelVipCardRequestType;
import corp.user.service.corpUserInfoService.GetCorpUserHotelVipCardResponseType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoRequestType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType;
import corp.user.service.corpUserInfoService.GetPlatformRelationByUidRequestType;
import corp.user.service.corpUserInfoService.GetPlatformRelationByUidResponseType;
import corp.user.service.corpUserInfoService.GetUserAddressInfoRequestType;
import corp.user.service.corpUserInfoService.GetUserAddressInfoResponseType;
import corp.user.service.group4j.accounts.QueryBizModeBindRelationRequestType;
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType;
import ctrip.BBZ.members.bbzmbrCommonPassenger.GetCommonPassengerRequestType;
import ctrip.BBZ.members.bbzmbrCommonPassenger.GetCommonPassengerResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/20 0:11
 */
@Component
public class ProcessorOfBookingInit extends AbstractProcessor<BookingInitRequestType, BookingInitResponseType> {
    // region 注入
    @Autowired
    private MapperOfBatchApprovalDefaultRequest mapperOfBatchApprovalDefaultRequest;
    @Autowired
    private HandlerOfBatchApprovalDefault handlerOfBatchApprovalDefault;
    @Autowired
    private HandlerOfApprovalTextInfo handlerOfApprovalTextInfo;
    @Autowired
    private MapperOfApprovalTextInfoRequestType mapperOfApprovalTextInfoRequestType;
    @Autowired
    private MapperOfGeneralSearchAccountInfoRequest mapperOfGeneralSearchAccountInfoRequest;
    @Autowired
    private HandlerOfGeneralSearchAccountInfo handlerOfGeneralSearchAccountInfo;
    @Autowired
    private HandlerOfGetCorpUserInfo handlerOfGetCorpUserInfo;
    @Autowired
    private MapperOfGetCorpUserInfoRequest mapperOfGetCorpUserInfoRequest;
    @Autowired
    private HandlerOfGetSubAccountConfig handlerOfGetSubAccountConfig;
    @Autowired
    private MapperOfGetSubAccountConfigRequest mapperOfGetSubAccountConfigRequest;
    @Autowired
    private MapperOfApprovalOutput mapperOfApprovalOutput;
    @Autowired
    private HandlerOfQueryHotelOrderData handlerOfQueryHotelOrderData;
    @Autowired
    private MapperOfQueryHotelOrderDataRequest mapperOfQueryHotelOrderDataRequest;
    @Autowired
    private HandlerOfQueryOrderSettings handlerOfQueryOrderSettings;
    @Autowired
    private MapperOfQueryOrderSettingsRequestType mapperOfQueryOrderSettingsRequestType;
    @Autowired
    private HandlerOfSearchApproval handlerOfSearchApproval;
    @Autowired
    private MapperOfSearchApprovalRequest mapperOfSearchApprovalRequest;
    @Autowired
    private HandlerOfGetHotelTravelPolicy handlerOfGetHotelTravelPolicy;
    @Autowired
    private MapperOfGetHotelTravelPolicyRequest mapperOfGetHotelTravelPolicyRequest;
    @Autowired
    private MapperOfBuildTravelPolicyRequestType mapperOfBuildTravelPolicyRequestType;
    @Autowired
    private HandlerOfCheckAvail handlerOfCheckAvail;
    @Autowired
    private MapperOfCheckAvailRequest mapperOfCheckAvailRequest;
    @Autowired
    private MapperOfGetIpInfoRequest mapperOfGetIpInfoRequest;
    @Autowired
    private HandlerOfGetIpInfo handlerOfGetIpInfo;
    @Autowired
    private HandlerOfCalculateServiceChargeV2 handlerOfCalculateServiceChargeV2;
    @Autowired
    private MapperOfCalculateServiceChargeV2RequestType mapperOfCalculateServiceChargeV2Request;
    @Autowired
    private HandlerOfGetSupportedPaymentMethod handlerOfGetSupportedPaymentMethod;
    @Autowired
    private MapperOfGetSupportedPaymentMethodRequestType mapperOfGetSupportedPaymentMethodRequestType;
    @Autowired
    private HandlerOfReimbursementQuery handlerOfReimbursementQuery;
    @Autowired
    private MapperOfReimbursementQueryRequestType mapperOfReimbursementQueryRequestType;
    @Autowired
    private HandlerOfGetSupportedInvoiceType handlerOfGetSupportedInvoiceType;
    @Autowired
    private MapperOfGetSupportedInvoiceRequestType mapperOfGetSupportedInvoiceRequestType;
    @Autowired
    private HandlerOfGetContactInvoiceDefaultInfo handlerOfGetContactInvoiceDefaultInfo;
    @Autowired
    private MapperOfGetContactInvoiceDefaultInfoRequest mapperOfGetContactInvoiceDefaultInfoRequest;
    @Autowired
    private HandlerOfGetUserAddressInfo handlerOfGetUserAddressInfo;
    @Autowired
    private MapperOfGetUserAddressInfoRequestType mapperOfGetUserAddressInfoRequestType;
    @Autowired
    private HandlerOfCalculateTravelRewards handlerOfCalculateTravelRewards;
    @Autowired
    private MapperOfCalculateTravelRewardsRequest mapperOfCalculateTravelRewardsRequest;
    @Autowired
    private HandlerOfDistributePaymentAmount handlerOfDistributePaymentAmount;
    @Autowired
    private MapperOfDistributePaymentAmountRequest mapperOfDistributePaymentAmountRequest;
    @Autowired
    private HandlerOfGetRoomChargePolicyList handlerOfGetRoomChargePolicyList;
    @Autowired
    private MapperOfGetRoomChargePolicyListRequestType mapperOfGetRoomChargePolicyListRequestType;
    @Autowired
    private HandlerOfGetPlatformRelationByUid handlerOfGetPlatformRelationByUid;
    @Autowired
    private MapperOfGetPlatformRelationByUidRequestType mapperOfGetPlatformRelationByUidRequestType;
    @Autowired
    private HandlerOfGetGroupVipPackage handlerOfGetGroupVipPackage;
    @Autowired
    private MapperOfGetGroupVipPackageRequestType mapperOfGetGroupVipPackageRequestType;
    @Autowired
    private HandlerOfQueryMrgMemberUserInfo handlerOfQueryMrgMemberUserInfo;
    @Autowired
    private MapperOfQueryCtripMrgMemberUserInfoRequest mapperOfQueryCtripMrgMemberUserInfoRequest;
    @Autowired
    private HandlerOfXProductEnquire handlerOfXProductEnquire;
    @Autowired
    private MapperOfXProductEnquireRequestType mapperOfXProductEnquireRequestType;
    @Autowired
    private HandlerOfGetCorpUserHotelVipCard handlerOfGetCorpUserHotelVipCard;
    @Autowired
    private MapperOfGetCorpUserHotelVipCardRequestType mapperOfGetCorpUserHotelVipCardRequestType;
    @Autowired
    private HandlerOfGetAuthDelayConfig handlerOfGetAuthDelayConfig;
    @Autowired
    private MapperOfGetAuthDelayRequestType mapperOfGetAuthDelayRequestType;
    @Autowired
    private HandlerOfGetCityBaseInfo handlerOfGetCityBaseInfo;
    @Autowired
    private MapperOfGetCityBaseInfoRequestType mapperOfGetCityBaseInfoRequestType;
    @Autowired
    private HandlerOfGetCancelPolicyDesc handlerOfGetCancelPolicyDesc;
    @Autowired
    private MapperOfGetCancelPolicyDescRequest mapperOfGetCancelPolicyDescRequest;
    @Autowired
    private MapperOfBookingInitResponse mapperOfBookingInitResponse;
    @Autowired
    private HandlerOfQueryIndividualAccount handlerOfQueryIndividualAccount;
    @Autowired
    private MapperOfQueryIndividualAccountRequestType mapperOfQueryIndividualAccountRequestType;
    @Autowired
    private MapperOfGetCorpUserInfoByPolicyRequest mapperOfGetCorpUserInfoByPolicyRequest;
    @Autowired
    private QConfigOfCodeMappingConfig qConfigOfCodeMappingConfig;
    @Autowired
    private MapperOfGetPackageRoomListRequestType mapperOfGetPackageRoomListRequestType;
    @Autowired
    private MapperOfGetPackageRoomSnapshotRequestType mapperOfGetPackageRoomSnapshotRequestType;
    @Autowired
    private HandlerOfGetPackageRoomList handlerOfGetPackageRoomList;
    @Autowired
    private HandlerOfGetPackageRoomSnapshot handlerOfGetPackageRoomSnapshot;
    @Autowired
    private QConfigOfPersonalAccountConfig qConfigOfPersonalAccountConfig;
    @Autowired
    private QConfigOfRoomNumberInfoConfig qConfigOfRoomNumberInfoConfig;
    @Autowired
    private HandlerOfGetHotelDetailInfo handlerOfGetHotelDetailInfo;
    @Autowired
    private MapperOfGetHotelDetailInfoRequest mapperOfGetHotelDetailInfoRequest;
    @Autowired
    private HandlerOfSearchRegistrationFields handlerOfSearchRegistrationFields;
    @Autowired
    private MapperOfSearchRegistrationFieldsRequestType mapperOfSearchRegistrationFieldsRequestType;
    @Autowired
    private HandlerOfBatchSearchClientsInfo handlerOfBatchSearchClientsInfo;
    @Autowired
    private MapperOfBatchSearchClientsInfoRequestType mapperOfBatchSearchClientsInfoRequestType;
    @Autowired
    private QConfigOfContactConfig qConfigOfContactConfig;
    @Autowired
    private HandlerOfCheckTravelPolicy handlerOfCheckTravelPolicy;
    @Autowired
    private MapperOfCheckTravelPolicyRequestType mapperOfCheckTravelPolicyRequestType;
    @Autowired
    private MapperOfGetTripBookingInfosRequest mapperOfGetTripBookingInfosRequest;
    @Autowired
    private HandlerOfGetTripBookingInfos handlerOfGetTripBookingInfos;
    @Autowired
    private QconfigOfGroupHotelMemberCardRuleConfig qconfigOfGroupHotelMemberCardRuleConfig;
    @Autowired
    private HandlerOfSearchTripDetail handlerOfSearchTripDetail;
    @Autowired
    private MapperOfSearchTripDetailRequestType mapperOfSearchTripDetailRequestType;
    @Autowired
    private MapperOfPayConfigRequestType mapperOfPayConfigRequestType;
    @Autowired
    private HandlerOfPayConfig handlerOfPayConfig;
    @Autowired
    private HandlerOfQueryBizModeBindRelation handlerOfQueryBizModeBindRelation;
    @Autowired
    private MapperOfQueryBizModeBindRelationRequestType mapperOfQueryBizModeBindRelationRequestType;
    @Autowired
    private HandlerOfGetReasoncodes handlerOfGetReasoncodes;
    @Autowired
    private MapperOfGetReasonCodesRequestType mapperOfGetReasonCodesRequestType;
    @Autowired
    private MapperOfPolicyGeneralSearchAccountInfoRequest mapperOfPolicyAccountInfoRequest;
    @Autowired
    private QConfigOfCustomizedSharkConfig qConfigOfCustomizedSharkConfig;
    @Autowired
    private HandlerOfCorpOrderInvoiceInfo handlerOfCorpOrderInvoiceInfo;
    @Autowired
    private MapperOfCorpOrderInvoiceDetailInfoQueryRequest mapperOfCorpOrderInvoiceDetailInfoQueryRequest;
    @Autowired
    private QconfigOfRegisterConfig qconfigOfRegisterConfig;
    @Autowired
    private MapperOfCountryQueryRequestType mapperOfCountryQueryRequestType;
    @Autowired
    private HandlerOfCountryQuery handlerOfCountryQuery;
    @Autowired
    private MapperOfGetAllNationalityRequestType mapperOfGetAllNationalityRequestType;
    @Autowired
    private HandlerOfGetAllNationality handlerOfGetAllNationality;
    @Autowired
    private QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig;
    @Autowired
    private MapperOfSearchTripBasicInfoRequestType mapperOfSearchTripBasicInfoRequestType;
    @Autowired
    private HandlerOfSearchTripBasicInfo handlerOfSearchTripBasicInfo;
    @Autowired
    private HandlerOfQueryAuth handlerOfQueryAuth;
    @Autowired
    private MapperOfQueryAuthRequestType mapperOfQueryAuthRequestType;
    @Autowired
    private HandlerOfGetCorpInfo handlerOfGetCorpInfo;
    @Autowired
    private MapperOfGetCorpInfoRequest mapperOfGetCorpInfoRequest;
    @Autowired
    private MapperOfHotelSearchApprovalRequest mapperOfHotelSearchApprovalRequest;
    @Autowired
    private MapperOfGetSupportedPaymentMethodResponseType mapperOfGetSupportedPaymentMethodResponseType;
    @Autowired
    private HandlerOfGetCommonPassenger handlerOfGetCommonPassenger;
    @Autowired
    private MapperOfGetCommonPassengerRequestType mapperOfGetCommonPassengerRequestType;
    @Autowired
    private HandlerOfGetCorpUserInfoDetailList handlerOfGetCorpUserInfoDetailList;
    @Autowired
    private MapperOfGetCorpUserInfoDetailListRequestType mapperOfGetCorpUserInfoDetailListRequestType;
    @Autowired
    private HandlerOfMaskData handlerOfMaskData;
    @Autowired
    private MapperOfMaskDataRequest mapperOfMaskDataRequestType;
    // endregion

    // TODO 不放置该处 会员卡号规则
    private static final String QCONFIG_FILE_MEMBER_BONUS_RULE = "memberBonusRule.json";
    @Override
    public BookingInitResponseType execute(BookingInitRequestType requestType) throws Exception {
        ResourceToken resourceToken = ResourceTokenUtil.tryParseToken(
            Optional.ofNullable(requestType.getResourceTokenInfo()).map(ResourceTokenInfo::getResourceToken).orElse(null));
        Long orderId = BookingInitUtil.getOrderId(requestType, resourceToken);
        Map<String, StrategyInfo> strategyInfoMap =
            StrategyOfBookingInitUtil.buildStrategyInfoMap(requestType.getStrategyInfos());

        // region hierarchy 1 仅依赖入参request,resourceToken
        // 审批单紧急预订文案
        WaitFuture<ApprovalTextInfoRequestType, ApprovalTextInfoResponseType> approvalTextInfoResponseTypeWaitFuture =
            null;
        if (BookingInitProcessorOfUtil.needApprovalTextInfo(strategyInfoMap)) {
            approvalTextInfoResponseTypeWaitFuture = handlerOfApprovalTextInfo.handleAsync(
                mapperOfApprovalTextInfoRequestType.map(
                    Tuple2.of(requestType.getIntegrationSoaRequestType(), strategyInfoMap)));
        }
        // 账户开关
        WaitFuture<GeneralSearchAccountInfoRequestType, GeneralSearchAccountInfoResponseType> generalSearchAccountInfoResponseTypeWaitFuture =
                handlerOfGeneralSearchAccountInfo.handleAsync(mapperOfGeneralSearchAccountInfoRequest.map(
                        Tuple1.of(requestType.getIntegrationSoaRequestType())));
        // 个人账户开关
        WaitFuture<QueryIndividualAccountRequestType, QueryIndividualAccountResponseType> queryIndividualAccountWaitFuture =
                handlerOfQueryIndividualAccount.handleAsync(mapperOfQueryIndividualAccountRequestType.map(
                        Tuple1.of(requestType.getIntegrationSoaRequestType())));
        // 获取用户信息
        WaitFuture<GetCorpUserInfoRequestType, GetCorpUserInfoResponseType> getCorpUserInfoResponseTypeWaitFuture =
                handlerOfGetCorpUserInfo.handleAsync(mapperOfGetCorpUserInfoRequest.map(
                        Tuple1.of(requestType.getIntegrationSoaRequestType())));
        // 公司信息
        WaitFuture<GetCorpInfoRequestType, GetCorpInfoResponseType> getCorpInfoWaitFuture =
                handlerOfGetCorpInfo.handleAsync(mapperOfGetCorpInfoRequest.map(
                        Tuple1.of(requestType.getIntegrationSoaRequestType())));
        // 双付丰享支付配置查询
        WaitFuture<PayConfigRequestType, PayConfigResponseType> payConfigResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needPayConfig(strategyInfoMap)) {
            payConfigResponseTypeWaitFuture = handlerOfPayConfig.handleAsync(
                mapperOfPayConfigRequestType.map(Tuple1.of(requestType.getIntegrationSoaRequestType())));
        }
        // 登录卡用户信息
        WaitFuture<BatchSearchClientsInfoRequestType, BatchSearchClientsInfoResponseType> batchSearchClientsInfoResponseTypeWaitFuture =
                handlerOfBatchSearchClientsInfo.handleAsync(mapperOfBatchSearchClientsInfoRequestType.map(
                        Tuple1.of(requestType.getIntegrationSoaRequestType())));
        // 酒店详情信息
        WaitFuture<GetHotelDetailInfoRequestType, GetHotelDetailInfoResponseType> getHotelDetailInfoWaitFuture =
                handlerOfGetHotelDetailInfo.handleAsync(mapperOfGetHotelDetailInfoRequest.map(
                        Tuple2.of(requestType, resourceToken)));
        // 发票信息
        WaitFuture<GetContactInvoiceDefaultInfoRequestType, GetContactInvoiceDefaultInfoResponseType>
            getContactInvoiceDefaultInfoResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needGetContactInvoiceDefaultInfo(strategyInfoMap)) {
            getContactInvoiceDefaultInfoResponseTypeWaitFuture = handlerOfGetContactInvoiceDefaultInfo.handleAsync(
                mapperOfGetContactInvoiceDefaultInfoRequest.map(Tuple1.of(requestType)));
        }
        // 审批沿用需要用到的开关
        WaitFuture<GetAuthDelayRequestType, GetAuthDelayResponseType> getAuthDelayResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needGetAuthDelayConfig(strategyInfoMap)) {
            getAuthDelayResponseTypeWaitFuture = handlerOfGetAuthDelayConfig.handleAsync(
                mapperOfGetAuthDelayRequestType.map(Tuple1.of(requestType.getIntegrationSoaRequestType())));
        }
        // ip属地
        WaitFuture<GetIpInfoRequestTypeV2, GetIpInfoResponseTypeV2> getIpInfoWaitFuture =
                handlerOfGetIpInfo.handleAsync(mapperOfGetIpInfoRequest.map(
                        Tuple1.of(requestType.getIntegrationSoaRequestType())));
        // 获取主营卡uid
        WaitFuture<QueryBizModeBindRelationRequestType, QueryBizModeBindRelationResponseType>
            queryBizModeBindRelationResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needQueryBizModeBindRelation(strategyInfoMap)) {
            queryBizModeBindRelationResponseTypeWaitFuture = handlerOfQueryBizModeBindRelation.handleAsync(
                mapperOfQueryBizModeBindRelationRequestType.map(
                    Tuple2.of(requestType.getIntegrationSoaRequestType(), null)));
        }
        // 原单信息
        WaitFuture<QueryHotelOrderDataRequestType, QueryHotelOrderDataResponseType> queryHotelOrderDataResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needQueryHotelOrderData(orderId)) {
            queryHotelOrderDataResponseTypeWaitFuture =
                    handlerOfQueryHotelOrderData.handleAsync(mapperOfQueryHotelOrderDataRequest.map(
                            Tuple2.of(requestType.getIntegrationSoaRequestType(), orderId)));
        }
        // 查询RC信息
        WaitFuture<GetReasoncodesRequestType, GetReasoncodesResponseType> getReasoncodesResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needGetReasoncodes(strategyInfoMap)) {
            getReasoncodesResponseTypeWaitFuture =
                    handlerOfGetReasoncodes.handleAsync(mapperOfGetReasonCodesRequestType.map(
                            Tuple1.of(requestType.getIntegrationSoaRequestType())));
        }
        // 查询政策执行人账户信息
        WaitFuture<GeneralSearchAccountInfoRequestType, GeneralSearchAccountInfoResponseType> policyAccountInfoResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needGeneralSearchAccountInfoOfPolicy(requestType.getPolicyInput())) {
            policyAccountInfoResponseTypeWaitFuture =
                    handlerOfGeneralSearchAccountInfo.handleAsync(mapperOfPolicyAccountInfoRequest.map(
                            Tuple2.of(requestType.getIntegrationSoaRequestType(), requestType.getPolicyInput())));
        }
        // 服务费版本
        WaitFuture<QueryOrderSettingsRequestType, QueryOrderSettingsResponseType> queryOrderSettingsResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needQueryHotelOrderData(orderId)) {
            queryOrderSettingsResponseTypeWaitFuture =
                    handlerOfQueryOrderSettings.handleAsync(mapperOfQueryOrderSettingsRequestType.map(
                            Tuple1.of(orderId)));
        }
        // 行程信息
        WaitFuture<GetTripBookingInfosRequestType, GetTripBookingInfosResponseType> getTripBookingInfosResponseWaitFuture = null;
        if (BookingInitProcessorOfUtil.needGetTripBookingInfos(requestType, strategyInfoMap)) {
            getTripBookingInfosResponseWaitFuture =
                    handlerOfGetTripBookingInfos.handleAsync(mapperOfGetTripBookingInfosRequest.map(
                            Tuple2.of(BookingInitUtil.buildTripInput(requestType.getTripInfoInput()), resourceToken)));
        }
        // 行程详情
        WaitFuture<SearchTripDetailRequestType, SearchTripDetailResponseType> searchTripDetailResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needSearchTripDetail(requestType, strategyInfoMap)) {
            searchTripDetailResponseTypeWaitFuture =
                    handlerOfSearchTripDetail.handleAsync(mapperOfSearchTripDetailRequestType.map(
                            Tuple3.of(requestType.getIntegrationSoaRequestType(), requestType.getTripInfoInput().getTripId(), null)));
        }
        // 原单发票信息
        WaitFuture<ReimbursementQueryRequestType, ReimbursementQueryResponseType> reimbursementQueryResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needReimbursementQuery(orderId, strategyInfoMap)) {
            reimbursementQueryResponseTypeWaitFuture =
                    handlerOfReimbursementQuery.handleAsync(mapperOfReimbursementQueryRequestType.map(
                            Tuple2.of(requestType, orderId)));

        }
        // 联系人国家码限制信息
        WaitFuture<GetAllNationalityRequestType, GetAllNationalityResponseType> getAllNationalityResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needGetAllNationality(requestType.getStrategyInfos(), strategyInfoMap)) {
            getAllNationalityResponseTypeWaitFuture =
                    handlerOfGetAllNationality.handleAsync(mapperOfGetAllNationalityRequestType.map(
                            Tuple1.of(requestType.getIntegrationSoaRequestType())));

        }
        // xProduct
        WaitFuture<XProductEnquireRequestType, XProductEnquireResponseType> xProductEnquireResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needXProductEnquire(orderId, strategyInfoMap)) {
            xProductEnquireResponseTypeWaitFuture =
                    handlerOfXProductEnquire.handleAsync(mapperOfXProductEnquireRequestType.map(
                            Tuple2.of(requestType, orderId)));
        }
        // 员工出行人信息
        List<WaitFuture<GetCorpUserInfoDetailListRequestType, GetCorpUserInfoDetailListResponseType>> corpUserInfoDetailListWaitFutures = null;
        if (BookingInitProcessorOfUtil.needGetCorpUserInfoDetailList(requestType.getIntegrationSoaRequestType(), requestType.getHotelBookPassengerInputs(), strategyInfoMap)) {
            List<String> uids = Null.or(requestType.getHotelBookPassengerInputs(), new ArrayList<HotelBookPassengerInput>())
                    .stream().map(HotelBookPassengerInput::getHotelPassengerInput).map(HotelPassengerInput::getUid)
                    .filter(StringUtil::isNotBlank).toList();
            corpUserInfoDetailListWaitFutures =
                    (uids.size() > HandlerOfGetCorpUserInfoDetailList.LIMIT_SIZE ?
                            Lists.partition(uids, HandlerOfGetCorpUserInfoDetailList.LIMIT_SIZE) : Arrays.asList(uids)).stream()
                    .map(uidList ->
                            handlerOfGetCorpUserInfoDetailList.handleAsync(mapperOfGetCorpUserInfoDetailListRequestType.map(
                                    Tuple2.of(requestType.getIntegrationSoaRequestType(), uidList))))
                    .toList();
        }
        // 非员工出行人信息
        List<WaitFuture<GetCommonPassengerRequestType, GetCommonPassengerResponseType>> commonPassengerWaitFutures = null;
        if (BookingInitProcessorOfUtil.needGetCommonPassenger(requestType.getIntegrationSoaRequestType(), requestType.getHotelBookPassengerInputs(), strategyInfoMap)) {
            List<String> infoids = Null.or(requestType.getHotelBookPassengerInputs(), new ArrayList<HotelBookPassengerInput>())
                    .stream().map(HotelBookPassengerInput::getHotelPassengerInput).map(HotelPassengerInput::getInfoId)
                    .filter(StringUtil::isNotBlank).toList();
            commonPassengerWaitFutures =
                    (infoids.size() > HandlerOfGetCommonPassenger.LIMIT_SIZE ?
                            Lists.partition(infoids, HandlerOfGetCommonPassenger.LIMIT_SIZE) : Arrays.asList(infoids)).stream()
                    .map(ids ->
                            handlerOfGetCommonPassenger.handleAsync(mapperOfGetCommonPassengerRequestType.map(
                                    Tuple2.of(requestType.getIntegrationSoaRequestType(), ids))))
                    .toList();
        }
        // endregion

        // region hierarchy 2 依赖于hierarchy 1
        // 查询子账户信息
        WaitFuture<GetSubAccountConfigRequestType, GetSubAccountConfigResponseType> getSubAccountConfigResponseTypeWaitFuture =
                handlerOfGetSubAccountConfig.handleAsync(mapperOfGetSubAccountConfigRequest.map(
                        Tuple1.of(WaitFutureUtil.safeGetFuture(getCorpUserInfoResponseTypeWaitFuture))));
        // 城市信息
        WaitFuture<GetCityBaseInfoRequestType, GetCityBaseInfoResponseType> getCityBaseInfoResponseTypeWaitFuture = null;
        if (StrategyOfBookingInitUtil.needCityBaseInfo(requestType.getStrategyInfos())) {
            getCityBaseInfoResponseTypeWaitFuture =
                    handlerOfGetCityBaseInfo.handleAsync(mapperOfGetCityBaseInfoRequestType.map(
                            Tuple2.of(requestType.getIntegrationSoaRequestType(),
                                    CityInputUtil.getCityInput(resourceToken, queryHotelOrderDataResponseTypeWaitFuture))));
        }
        // 散客uid,获取会员绑定
        WaitFuture<GetPlatformRelationByUidRequestType, GetPlatformRelationByUidResponseType> getPlatformRelationByUidResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needGetPlatformRelationByUid(requestType.getIntegrationSoaRequestType(),
                WaitFutureUtil.safeGetFutureWithoutError(queryBizModeBindRelationResponseTypeWaitFuture), strategyInfoMap)) {
            getPlatformRelationByUidResponseTypeWaitFuture =
                    handlerOfGetPlatformRelationByUid.handleAsync(mapperOfGetPlatformRelationByUidRequestType.map(
                            Tuple2.of(requestType.getIntegrationSoaRequestType(),
                                    WaitFutureUtil.safeGetFutureWithoutError(queryBizModeBindRelationResponseTypeWaitFuture))));
        }
        // 行程基础信息
        WaitFuture<SearchTripBasicInfoRequestType, SearchTripBasicInfoResponseType> searchTripBasicInfoResponseTypeWaitFutureOfOriginalOrder = null;
        if (BookingInitProcessorOfUtil.needSearchTripBasicInfoOfOriginalOrderId(
            WaitFutureUtil.safeGetFuture(queryHotelOrderDataResponseTypeWaitFuture), strategyInfoMap)) {
            searchTripBasicInfoResponseTypeWaitFutureOfOriginalOrder =
                    handlerOfSearchTripBasicInfo.handleAsync(mapperOfSearchTripBasicInfoRequestType.map(
                            Tuple2.of(Optional.ofNullable(WaitFutureUtil.safeGetFuture(queryHotelOrderDataResponseTypeWaitFuture))
                                            .map(QueryHotelOrderDataResponseType::getOrderBasicInfo)
                                            .map(OrderBasicInfoType::getTripOrderId).orElse(null),
                                    requestType.getIntegrationSoaRequestType())));
        }
        // 管理员权限
        WaitFuture<QueryAuthRequestType, QueryAuthResponseType> queryAuthWaitFuture = null;
        if (BookingInitProcessorOfUtil.needQueryAuth(WaitFutureUtil.safeGetFuture(getCorpInfoWaitFuture))) {
            queryAuthWaitFuture = handlerOfQueryAuth.handleAsync(mapperOfQueryAuthRequestType.map(
                            Tuple1.of(requestType.getIntegrationSoaRequestType())));
        }
        // 掩码接口获取传输字段
        WaitFuture<MaskDataRequestType, MaskDataResponseType> maskDataResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needMaskData(requestType.getIntegrationSoaRequestType(), requestType.getHotelBookPassengerInputs(), strategyInfoMap)) {
            maskDataResponseTypeWaitFuture =
                    handlerOfMaskData.handleAsync(mapperOfMaskDataRequestType.map(
                            Tuple4.of(requestType.getIntegrationSoaRequestType(),
                                    Null.or(corpUserInfoDetailListWaitFutures,
                                                    new ArrayList<WaitFuture<GetCorpUserInfoDetailListRequestType, GetCorpUserInfoDetailListResponseType>>())
                                            .stream().map(WaitFutureUtil::safeGetFutureWithoutError).collect(Collectors.toList()),
                                    Null.or(commonPassengerWaitFutures, new ArrayList<WaitFuture<GetCommonPassengerRequestType, GetCommonPassengerResponseType>>())
                                            .stream().map(WaitFutureUtil::safeGetFutureWithoutError).collect(Collectors.toList()),
                                    CityInputUtil.getCityInput(resourceToken, queryHotelOrderDataResponseTypeWaitFuture))));
        }
        // endregion

        // region hierarchy 3 依赖于hierarchy 2
        // 账户开关wrapper
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
            .accountInfo(WaitFutureUtil.safeGetFuture(generalSearchAccountInfoResponseTypeWaitFuture))
            .policyAccountInfo(WaitFutureUtil.safeGetFuture(policyAccountInfoResponseTypeWaitFuture))
            .corpUserInfo(WaitFutureUtil.safeGetFuture(getCorpUserInfoResponseTypeWaitFuture))
            .subAccountConfig(WaitFutureUtil.safeGetFuture(getSubAccountConfigResponseTypeWaitFuture))
            .build();
        // 政策执行人信息
        WaitFuture<GetCorpUserInfoRequestType, GetCorpUserInfoResponseType> getCorpUserInfoByPolicyResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needGetCorpUserInfoOfPolicy(requestType, accountInfo)) {
            getCorpUserInfoByPolicyResponseTypeWaitFuture =
                    handlerOfGetCorpUserInfo.handleAsync(mapperOfGetCorpUserInfoByPolicyRequest.map(
                            Tuple1.of(requestType)));
        }
        // 查询默认审批单信息
        WaitFuture<BatchApprovalDefaultRequestType, BatchApprovalDefaultResponseType> batchApprovalDefaultResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needBatchApprovalDefault(requestType, accountInfo, resourceToken)) {
            batchApprovalDefaultResponseTypeWaitFuture =
                    handlerOfBatchApprovalDefault.handleAsync(mapperOfBatchApprovalDefaultRequest.map(
                            Tuple2.of(requestType, accountInfo)));
        }
        // 查询审批单信息
        WaitFuture<SearchApprovalRequestType, SearchApprovalResponseType> searchApprovalResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needSearchApproval(requestType, queryHotelOrderDataResponseTypeWaitFuture, resourceToken, accountInfo)) {
            SearchApprovalRequest searchApprovalRequest =
                    mapperOfHotelSearchApprovalRequest.map(
                            Tuple4.of(requestType, WaitFutureUtil.safeGetFuture(queryHotelOrderDataResponseTypeWaitFuture), resourceToken, accountInfo));
            searchApprovalResponseTypeWaitFuture =
                    handlerOfSearchApproval.handleAsync(mapperOfSearchApprovalRequest.map(
                            Tuple1.of(searchApprovalRequest)));
        }
        // endregion

        // region hierarchy 4 依赖于hierarchy 3
        WrapperOfSearchApproval.ApprovalInfo approvalInfo = null;
        if (BookingInitProcessorOfUtil.needSearchApproval(requestType, queryHotelOrderDataResponseTypeWaitFuture, resourceToken, accountInfo)) {
            approvalInfo = WrapperOfSearchApproval.builder()
                    .searchApprovalResponseType(WaitFutureUtil.safeGetFutureWithoutError(searchApprovalResponseTypeWaitFuture))
                    .build();
        }
        QueryHotelOrderDataResponseType queryHotelOrderDataResponseType =
                WaitFutureUtil.safeGetFuture(queryHotelOrderDataResponseTypeWaitFuture);
        HotelPayTypeEnum selectedRoomPayType = HotelPayTypeUtil.getSelectedRoomPayType(requestType, queryHotelOrderDataResponseType);

        // TODO 待重构代码
        ApprovalOutput approvalOutput = mapperOfApprovalOutput.map(
            Tuple7.of(requestType, WaitFutureUtil.safeGetFutureWithoutError(batchApprovalDefaultResponseTypeWaitFuture),
                approvalInfo, queryHotelOrderDataResponseType, resourceToken, accountInfo,
                WaitFutureUtil.safeGetFutureWithoutError(approvalTextInfoResponseTypeWaitFuture)));
        // TODO 去掉中间对象 input逻辑迁移至Output
        ApprovalInput approvalInput = buildApprovalInput(requestType, approvalOutput);
        // 出行人信息
        List<HotelBookPassengerInput> hotelBookPassengerInputs = HotelBookPassengerInputUtil.buildRealTimeHotelPassengerInput(requestType.getHotelBookPassengerInputs(),
                Null.or(corpUserInfoDetailListWaitFutures, new ArrayList<WaitFuture<GetCorpUserInfoDetailListRequestType, GetCorpUserInfoDetailListResponseType>>())
                        .stream().map(WaitFutureUtil::safeGetFutureWithoutError).collect(Collectors.toList()),
                Null.or(commonPassengerWaitFutures, new ArrayList<WaitFuture<GetCommonPassengerRequestType, GetCommonPassengerResponseType>>())
                        .stream().map(WaitFutureUtil::safeGetFutureWithoutError).collect(Collectors.toList()),
                WaitFutureUtil.safeGetFutureWithoutError(maskDataResponseTypeWaitFuture));
        // 查询差标
        WaitFuture<GetHotelTravelPolicyRequestType, GetHotelTravelPolicyResponseType> getHotelTravelPolicyResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needGetHotelTravelPolicy(requestType, accountInfo)) {
            GetTravelPolicyRequest getTravelPolicyRequest =
                    mapperOfBuildTravelPolicyRequestType.map(
                            Tuple6.of(requestType, resourceToken, approvalInput, selectedRoomPayType, accountInfo, hotelBookPassengerInputs));
            getHotelTravelPolicyResponseTypeWaitFuture =
                    handlerOfGetHotelTravelPolicy.handleAsync(mapperOfGetHotelTravelPolicyRequest.map(
                            Tuple2.of(getTravelPolicyRequest, accountInfo)));
        }
        // endregion

        // region hierarchy 5 依赖于hierarchy 4
        WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo hotelTravelPolicyInfo = null;
        if (BookingInitProcessorOfUtil.needGetHotelTravelPolicy(requestType, accountInfo)) {
            hotelTravelPolicyInfo = WrapperOfHotelTravelPolicy.builder()
                    .hotelTravelPolicyResponseType(WaitFutureUtil.safeGetFuture(getHotelTravelPolicyResponseTypeWaitFuture))
                    .build();
        }
        // 查询可订
        WaitFuture<CheckAvailRequestType, CheckAvailResponseType> checkAvailResponseTypeWaitFuture =
                handlerOfCheckAvail.handleAsync(mapperOfCheckAvailRequest.map(
                        Tuple7.of(requestType, hotelTravelPolicyInfo, accountInfo,
                                resourceToken, getIpInfoWaitFuture.getWithoutError(), strategyInfoMap, hotelBookPassengerInputs)));
        // endregion

        // region hierarchy 6 依赖于hierarchy 5
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(WaitFutureUtil.safeGetFuture(checkAvailResponseTypeWaitFuture))
                .setResourceToken(resourceToken)
                .check(qConfigOfCodeMappingConfig, requestType.getStrategyInfos(), requestType.getIntegrationSoaRequestType(), strategyInfoMap)
                .build()
                .getCheckAvailInfo();
        // 支付方式
        WaitFuture<GetSupportedPaymentMethodRequestType, GetSupportedPaymentMethodResponseType> getSupportedPaymentMethodResponseTypeWaitFuture =
                handlerOfGetSupportedPaymentMethod.handleAsync(mapperOfGetSupportedPaymentMethodRequestType.map(
                        Tuple6.of(requestType, checkAvailInfo, hotelTravelPolicyInfo, accountInfo,
                                queryHotelOrderDataResponseType, WaitFutureUtil.safeGetFutureWithoutError(queryOrderSettingsResponseTypeWaitFuture))));
        // 集团信息
        WaitFuture<GetGroupVipPackageRequestType, GetGroupVipPackageResponseType>
            getGroupVipPackageResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needGetGroupVipPackage(strategyInfoMap)) {
            getGroupVipPackageResponseTypeWaitFuture = handlerOfGetGroupVipPackage.handleAsync(
                mapperOfGetGroupVipPackageRequestType.map(Tuple3.of(requestType, checkAvailInfo,
                    WaitFutureUtil.safeGetFutureWithoutError(getPlatformRelationByUidResponseTypeWaitFuture))));
        }
        // 会员互通注册规则信息
        WaitFuture<SearchRegistrationFieldsRequestType, SearchRegistrationFieldsResponseType>
            searchRegistrationFieldsResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needSearchRegistrationFields(checkAvailInfo, strategyInfoMap)) {
            searchRegistrationFieldsResponseTypeWaitFuture =
                    handlerOfSearchRegistrationFields.handleAsync(mapperOfSearchRegistrationFieldsRequestType.map(
                            Tuple2.of(requestType.getIntegrationSoaRequestType(), checkAvailInfo)));
        }
        // 集团会员信息
        WaitFuture<QueryCtripMrgMemberUserInfoRequest, CtripMemberUserInfoResponse> ctripMemberUserInfoResponseWaitFuture = null;
        if (BookingInitProcessorOfUtil.needQueryMrgMemberUserInfo(checkAvailInfo, strategyInfoMap)) {
            ctripMemberUserInfoResponseWaitFuture =
                    handlerOfQueryMrgMemberUserInfo.handleAsync(mapperOfQueryCtripMrgMemberUserInfoRequest.map(
                            Tuple4.of(requestType, checkAvailInfo,
                                    WaitFutureUtil.safeGetFutureWithoutError(getPlatformRelationByUidResponseTypeWaitFuture),
                                    WaitFutureUtil.safeGetFutureWithoutError(queryBizModeBindRelationResponseTypeWaitFuture))));
        }
        // 查询用户酒店会员卡信息
        WaitFuture<GetCorpUserHotelVipCardRequestType, GetCorpUserHotelVipCardResponseType> getCorpUserHotelVipCardResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(requestType.getIntegrationSoaRequestType(),
                WaitFutureUtil.safeGetFutureWithoutError(queryBizModeBindRelationResponseTypeWaitFuture),
                requestType.getStrategyInfos(), requestType.getHotelBookPassengerInputs(), strategyInfoMap)) {
            getCorpUserHotelVipCardResponseTypeWaitFuture =
                    handlerOfGetCorpUserHotelVipCard.handleAsync(mapperOfGetCorpUserHotelVipCardRequestType.map(
                            Tuple5.of(requestType.getIntegrationSoaRequestType(), checkAvailInfo,
                                    WaitFutureUtil.safeGetFutureWithoutError(queryBizModeBindRelationResponseTypeWaitFuture),
                                    requestType.getStrategyInfos(), requestType.getHotelBookPassengerInputs())));
        }
        // 套餐信息
        WaitFuture<GetPackageRoomListRequestType, GetPackageRoomListResponseType> getPackageRoomListWaitFuture = null;
        if (BookingInitProcessorOfUtil.needGetPackageRoomList(checkAvailInfo, strategyInfoMap)) {
             getPackageRoomListWaitFuture =
                    handlerOfGetPackageRoomList.handleAsync(mapperOfGetPackageRoomListRequestType.map(
                            Tuple3.of(requestType, resourceToken, checkAvailInfo)));
        }
        // 套餐快照
        WaitFuture<GetPackageRoomSnapshotRequestType, GetPackageRoomSnapshotResponseType> GetPackageRoomSnapshotWaitFuture = null;
        if (BookingInitProcessorOfUtil.needGetPackageRoomSnapshot(resourceToken, checkAvailInfo, strategyInfoMap)) {
            GetPackageRoomSnapshotWaitFuture =
                    handlerOfGetPackageRoomSnapshot.handleAsync(mapperOfGetPackageRoomSnapshotRequestType.map(
                            Tuple3.of(requestType, resourceToken, checkAvailInfo)));
        }
        // 国籍限制信息
        WaitFuture<CountryQueryRequestType, CountryQueryResponseType> countryQueryResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needCountryQuery(requestType.getStrategyInfos(), requestType.getIntegrationSoaRequestType(),
                Optional.ofNullable(checkAvailInfo).map(WrapperOfCheckAvail.CheckAvailInfo::getBookingRules)
                        .map(BookingRulesType::getNationalityRestrictionInfo).orElse(null))) {
            countryQueryResponseTypeWaitFuture =
                    handlerOfCountryQuery.handleAsync(mapperOfCountryQueryRequestType.map(
                            Tuple2.of(requestType.getIntegrationSoaRequestType(),
                                    Optional.ofNullable(checkAvailInfo).map(WrapperOfCheckAvail.CheckAvailInfo::getBookingRules)
                                            .map(BookingRulesType::getNationalityRestrictionInfo).orElse(null))));
        }
        // endregion

        // region hierarchy 7 依赖于hierarchy 6
        GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponseType = getSupportedPaymentMethodResponseTypeWaitFuture.getWithoutError();
        // 支付方式校验
        mapperOfGetSupportedPaymentMethodResponseType.map(Tuple1.of(getSupportedPaymentMethodResponseType));
        // 房费支付方式
        HotelPayTypeEnum roomPayTypeEnum = BookingInitUtil.getRoomPayType(selectedRoomPayType, getSupportedPaymentMethodResponseType, requestType.getFlashStayInput(), accountInfo);
        // 服务费V2接口
        WaitFuture<CalculateServiceChargeV2RequestType, CalculateServiceChargeV2ResponseType> calculateServiceChargeV2ResponseTypeWaitFuture = null;

        if (BookingInitProcessorOfUtil.needCalculateServiceChargeV2(requestType)) {
            calculateServiceChargeV2ResponseTypeWaitFuture =
                    handlerOfCalculateServiceChargeV2.handleAsync(mapperOfCalculateServiceChargeV2Request.map(
                            Tuple8.of(accountInfo, queryHotelOrderDataResponseType, WaitFutureUtil.safeGetFutureWithoutError(queryOrderSettingsResponseTypeWaitFuture),
                                    checkAvailInfo, requestType, resourceToken, getSupportedPaymentMethodResponseTypeWaitFuture.getWithoutError()
                                    , getHotelDetailInfoWaitFuture.getWithoutError())));
        }
        // 服务费政策
        WaitFuture<GetRoomChargePolicyListRequestType, GetRoomChargePolicyListResponseType> getRoomChargePolicyListWaitFuture = null;
        if (BookingInitProcessorOfUtil.needGetRoomChargePolicyList(requestType.getCorpPayInfo())) {
            getRoomChargePolicyListWaitFuture =
                    handlerOfGetRoomChargePolicyList.handleAsync(mapperOfGetRoomChargePolicyListRequestType.map(
                            Tuple9.of(accountInfo, queryHotelOrderDataResponseType, WaitFutureUtil.safeGetFutureWithoutError(queryOrderSettingsResponseTypeWaitFuture),
                                    checkAvailInfo, requestType, resourceToken, getSupportedPaymentMethodResponseType, roomPayTypeEnum
                                    ,getHotelDetailInfoWaitFuture.getWithoutError())));
        }
        // endregion

        // region hierarchy 8 依赖于hierarchy 7
        CalculateServiceChargeV2ResponseType calculateServiceChargeV2ResponseType = WaitFutureUtil.safeGetFutureWithoutError(calculateServiceChargeV2ResponseTypeWaitFuture);
        // 服务费支付方式
        HotelPayTypeEnum servicePayTypeEnum = BookingInitUtil.getServicePayType(requestType.getHotelPayTypeInput(),
                selectedRoomPayType, getSupportedPaymentMethodResponseType, null, accountInfo, calculateServiceChargeV2ResponseType);
        // TODO 发票新版 - 技改跟进
        WaitFuture<GetSupportedInvoiceTypeRequestType, GetSupportedInvoiceTypeResponseType> getSupportedInvoiceTypeResponseTypeWaitFuture = null;
        if (!BookingInitUtil.supportNewInvoice(requestType)) {
            getSupportedInvoiceTypeResponseTypeWaitFuture =
                    handlerOfGetSupportedInvoiceType.handleAsync(mapperOfGetSupportedInvoiceRequestType.map(
                            Tuple5.of(checkAvailInfo, calculateServiceChargeV2ResponseType, requestType, roomPayTypeEnum, servicePayTypeEnum)));
        }
        // 发票信息获取
        WaitFuture<CorpOrderInvoiceDetailInfoQueryRequest, CorpOrderInvoiceDetailInfoQueryResponse> corpOrderInvoiceDetailInfoQueryResponseWaitFuture = null;
        if (BookingInitProcessorOfUtil.needCorpOrderInvoiceInfo(strategyInfoMap)) {
            corpOrderInvoiceDetailInfoQueryResponseWaitFuture =
                    handlerOfCorpOrderInvoiceInfo.handleAsync(mapperOfCorpOrderInvoiceDetailInfoQueryRequest.map(
                            Tuple6.of(requestType, resourceToken, accountInfo, checkAvailInfo, roomPayTypeEnum, servicePayTypeEnum)));
        }
        // 旅行奖励费用计算
        WaitFuture<CalculateTravelRewardsRequestType, CalculateTravelRewardsResponseType>
            calculateTravelRewardsResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needCalculateTravelRewards(strategyInfoMap)) {
            calculateTravelRewardsResponseTypeWaitFuture = handlerOfCalculateTravelRewards.handleAsync(
                mapperOfCalculateTravelRewardsRequest.map(
                    Tuple8.of(requestType, checkAvailInfo, hotelTravelPolicyInfo, calculateServiceChargeV2ResponseType,
                        accountInfo, getSupportedPaymentMethodResponseType, selectedRoomPayType,
                        hotelBookPassengerInputs)));
        }
        // agg费用详情
        WaitFuture<DistributePaymentAmountRequestType, DistributePaymentAmountResponseType> distributePaymentAmountResponseTypeWaitFuture =
                handlerOfDistributePaymentAmount.handleAsync(mapperOfDistributePaymentAmountRequest.map(
                        Tuple7.of(requestType, checkAvailInfo, hotelTravelPolicyInfo, getSupportedPaymentMethodResponseType,
                                accountInfo, calculateServiceChargeV2ResponseType, selectedRoomPayType)));
        // 取消政策
        WaitFuture<GetCancelPolicyDescRequestType, GetCancelPolicyDescResponseType> getCancelPolicyDescResponseTypeWaitFuture =
                handlerOfGetCancelPolicyDesc.handleAsync(mapperOfGetCancelPolicyDescRequest.map(
                        Tuple5.of(requestType, checkAvailInfo, calculateServiceChargeV2ResponseType, roomPayTypeEnum, servicePayTypeEnum)));
        // 查询CheckTravelPolicy 和 审批单信息
        WaitFuture<CheckTravelPolicyRequestType, CheckTravelPolicyResponseType> checkTravelPolicyResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needCheckTravelPolicy(requestType)) {
            WrapperOfCheckTravelPolicy checkTravelPolicy = WrapperOfCheckTravelPolicy.builder()
                    .setResourceToken(resourceToken)
                    .setCheckAvailInfo(checkAvailInfo)
                    .setAccountInfo(accountInfo)
                    .setHotelPolicyInput(new HotelPolicyInput(requestType.getPolicyInput(), null))
                    .setIntegrationSoaRequestType(requestType.getIntegrationSoaRequestType())
                    .setApprovalInput(approvalInput)
                    .setAddPriceInput(requestType.getAddPriceInput())
                    .setHotelBookInput(requestType.getHotelBookInput())
                    .setScene(MapperOfCheckTravelPolicyRequestType.SCENE_PAY_TYPE_FROM_DEFAULT )
                    .setHotelInsuranceInput(requestType.getHotelInsuranceInput())
                    .setHotelPayTypeInputs(requestType.getHotelPayTypeInput())
                    .setGetSupportedPaymentMethodResponseType(getSupportedPaymentMethodResponseType)
                    .setFlashStayInput(requestType.getFlashStayInput())
                    .setCalculateServiceChargeV2ResponseType(calculateServiceChargeV2ResponseType)
                    .setRcInfos(requestType.getRcInfos())
                    .setHotelBookPassengerInputs(hotelBookPassengerInputs)
                    .setCorpPayInfo(requestType.getCorpPayInfo())
                    .setRoomPayType(roomPayTypeEnum)
                    .setStrategyInfos(requestType.getStrategyInfos())
                    .setPolicyToken(Optional.ofNullable(hotelTravelPolicyInfo).map(WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo::getPolicyToken).orElse(null))
                    .setServicePayType(servicePayTypeEnum)
                    .setStrategyInfoMap(strategyInfoMap)
                    .setApprovalTextInfoResponseType(WaitFutureUtil.safeGetFutureWithoutError(approvalTextInfoResponseTypeWaitFuture))
                    .build();
            checkTravelPolicyResponseTypeWaitFuture =
                    handlerOfCheckTravelPolicy.handleAsync(mapperOfCheckTravelPolicyRequestType.map(
                            Tuple1.of(checkTravelPolicy)));
        }
        // endregion

        // region assemble request
        BookingInitAssembleRequest bookInitAssembleRequest = BookingInitAssembleRequest.builder()
            .withBookingInitRequest(requestType)
            .withCancelPolicyDesc(Optional.ofNullable(WaitFutureUtil.safeGetFutureWithoutError(getCancelPolicyDescResponseTypeWaitFuture))
                    .map(GetCancelPolicyDescResponseType::getCancelPolicyDescList).filter(CollectionUtil::isNotEmpty)
                    .map(o -> CollectionUtil.findFirst(o, null)).orElse(null))
            .withGetAuthDelayResponse(WaitFutureUtil.safeGetFutureWithoutError(getAuthDelayResponseTypeWaitFuture))
            .withAccountInfo(accountInfo)
            .withQueryHotelOrderDataResponse(queryHotelOrderDataResponseType)
            .withGetSupportedPaymentMethodResponse(getSupportedPaymentMethodResponseType)
            .withGetGroupVipPackageResponse(WaitFutureUtil.safeGetFutureWithoutError(getGroupVipPackageResponseTypeWaitFuture))
            .withCtripMemberUserInfoResponse(WaitFutureUtil.safeGetFutureWithoutError(ctripMemberUserInfoResponseWaitFuture))
            .withCalculateTravelRewardsResponse(WaitFutureUtil.safeGetFutureWithoutError(calculateTravelRewardsResponseTypeWaitFuture))
            .withQConfigOfPersonalAccountConfig(qConfigOfPersonalAccountConfig.getPersonalAccountConfig())
            .withRoomNumberInfoConfig(qConfigOfRoomNumberInfoConfig.getRoomNumberInfoConfig())
            .withQueryIndividualAccountResponse(WaitFutureUtil.safeGetFuture(queryIndividualAccountWaitFuture))
            .withGetCorpUserHotelVipCardResponse(WaitFutureUtil.safeGetFutureWithoutError(getCorpUserHotelVipCardResponseTypeWaitFuture))
            .withCalculateServiceChargeV2Response(calculateServiceChargeV2ResponseType)
            .withReimbursementDetailInfoType(Optional.ofNullable(WaitFutureUtil.safeGetFutureWithoutError(reimbursementQueryResponseTypeWaitFuture))
                    .map(ReimbursementQueryResponseType::getReimbursementInfo)
                    .map(ReimbursementInfoType::getReimbursementDetailInfoList)
                    .map(o -> CollectionUtil.findFirst(o, null))
                    .orElse(null))
            .withGetSupportedInvoiceTypeResponseType(WaitFutureUtil.safeGetFuture(getSupportedInvoiceTypeResponseTypeWaitFuture))
            .withGetContactInvoiceDefaultInfoResponseType(WaitFutureUtil.safeGetFutureWithoutError(getContactInvoiceDefaultInfoResponseTypeWaitFuture))
            .withRoomPayType(roomPayTypeEnum)
            .withServicePayType(servicePayTypeEnum)
            .withResourceToken(resourceToken)
            .withGetCorpUserInfoResponseType(WaitFutureUtil.safeGetFuture(getCorpUserInfoResponseTypeWaitFuture))
            .withHotelTravelPolicyInfo(hotelTravelPolicyInfo)
            .withCheckAvailInfo(checkAvailInfo)
            .withApprovalInfo(approvalInfo)
            .withGetCorpUserInfoByPolicyResponseType(WaitFutureUtil.safeGetFuture(getCorpUserInfoByPolicyResponseTypeWaitFuture))
            .withDistributePaymentAmountResponse(WaitFutureUtil.safeGetFuture(distributePaymentAmountResponseTypeWaitFuture))
            .withApprovalDefaultResponseType(WaitFutureUtil.safeGetFuture(batchApprovalDefaultResponseTypeWaitFuture))
            .withApprovalTextInfoResponseType(WaitFutureUtil.safeGetFutureWithoutError(approvalTextInfoResponseTypeWaitFuture))
            .withGetPackageRoomListResponseType(WaitFutureUtil.safeGetFutureWithoutError(getPackageRoomListWaitFuture))
            .withGetPackageRoomSnapshotResponseType(WaitFutureUtil.safeGetFutureWithoutError(GetPackageRoomSnapshotWaitFuture))
            .withGetHotelDetailInfoResponseType(getHotelDetailInfoWaitFuture.getWithoutError())
            .withSearchRegistrationFieldsResponseType(WaitFutureUtil.safeGetFutureWithoutError(searchRegistrationFieldsResponseTypeWaitFuture))
            .withBatchSearchClientsInfoResponseType(WaitFutureUtil.safeGetFutureWithoutError(batchSearchClientsInfoResponseTypeWaitFuture))
            .withQConfigOfContactConfig(qConfigOfContactConfig)
            .withCheckTravelPolicyResponseType(WaitFutureUtil.safeGetFuture(checkTravelPolicyResponseTypeWaitFuture))
            .withXProductEnquireResponseType(WaitFutureUtil.safeGetFutureWithoutError(xProductEnquireResponseTypeWaitFuture))
            .withGetTripBookingInfosResponseType(WaitFutureUtil.safeGetFutureWithoutError(getTripBookingInfosResponseWaitFuture))
            .withQconfigEntityOfGroupHotelMemberCardRule(qconfigOfGroupHotelMemberCardRuleConfig.getQconfigEntityOfGroupHotelMemberCardRule())
            .withBookInitToken(TokenParseUtil.parseToken(requestType.getBookInitToken(), BookInitToken.class))
            .withApprovalOutPut(approvalOutput)
            .withGetPlatformRelationByUidResponseType(WaitFutureUtil.safeGetFutureWithoutError(getPlatformRelationByUidResponseTypeWaitFuture))
            .withSearchTripDetailResponseType(WaitFutureUtil.safeGetFutureWithoutError(searchTripDetailResponseTypeWaitFuture))
            .withApprovalInput(approvalInput)
            .withSelectedRoomPayType(selectedRoomPayType)
                // TODO 注入的形式
            .withMemberBonusRuleEntries(QConfigUtil.getFile(QCONFIG_FILE_MEMBER_BONUS_RULE).asJsonArray(MemberBonusRuleEntry.class, null))
            .withPayConfigResponseType(WaitFutureUtil.safeGetFutureWithoutError(payConfigResponseTypeWaitFuture))
            .withGetRoomChargePolicyListResponseType(WaitFutureUtil.safeGetFutureWithoutError(getRoomChargePolicyListWaitFuture))
            .withGetReasoncodesResponseType(WaitFutureUtil.safeGetFutureWithoutError(getReasoncodesResponseTypeWaitFuture))
            .withCustomizedSharkConfigList(qConfigOfCustomizedSharkConfig.getCustomizedSharkConfigs())
            .withQueryBizModeBindRelationResponseType(WaitFutureUtil.safeGetFutureWithoutError(queryBizModeBindRelationResponseTypeWaitFuture))
            .withCorpOrderInvoiceDetailInfoQueryResponse(
                WaitFutureUtil.safeGetFutureWithoutError(corpOrderInvoiceDetailInfoQueryResponseWaitFuture))
            .withQconfigOfRegisterConfig(qconfigOfRegisterConfig)
            .withStrategyInfoMap(strategyInfoMap)
            .withCountryQueryResponseType(WaitFutureUtil.safeGetFutureWithoutError(countryQueryResponseTypeWaitFuture))
            .withGetAllNationalityResponseType(WaitFutureUtil.safeGetFutureWithoutError(getAllNationalityResponseTypeWaitFuture))
            .withGetCityBaseInfoResponseType(WaitFutureUtil.safeGetFutureWithoutError(getCityBaseInfoResponseTypeWaitFuture))
            .withQconfigOfCertificateInitConfig(qconfigOfCertificateInitConfig)
            .withCostCenterNew(BookingInitUtil.buildCostCenterNew(requestType))
            .withQueryAuthResponseType(WaitFutureUtil.safeGetFutureWithoutError(queryAuthWaitFuture))
            .withSearchTripBasicInfoResponseTypeOfOriginalOrder(
                WaitFutureUtil.safeGetFutureWithoutError(searchTripBasicInfoResponseTypeWaitFutureOfOriginalOrder))
            .withGetCorpInfoResponseType(WaitFutureUtil.safeGetFutureWithoutError(getCorpInfoWaitFuture))
            .withHotelBookPassengerInputs(hotelBookPassengerInputs)
            .build();
        // endregion
        return mapperOfBookingInitResponse.map(Tuple1.of(bookInitAssembleRequest));
    }

    private ApprovalInput buildApprovalInput(BookingInitRequestType requestType, ApprovalOutput approvalOutput) {
        if (requestType == null) {
            return null;
        }
        if (!StrategyOfBookingInitUtil.firstRequest(requestType.getStrategyInfos())) {
            return requestType.getApprovalInput();
        }
        if (approvalOutput == null) {
            return null;
        }
        ApprovalInput approvalInput = new ApprovalInput();
        approvalInput.setSubApprovalNo(approvalOutput.getDefaultApprovalSubNo());
        approvalInput.setMasterApprovalNo(approvalOutput.getDefaultApprovalMainNo());
        approvalInput.setEmergency(approvalOutput.getDefaultEmergencyBook());
        return approvalInput;
    }
    @Override
    public Map<String, String> tracking(BookingInitRequestType requestType, BookingInitResponseType responseType) {
        return BookingInitTraceUtil.tracking(requestType, responseType);
    }

}
