package com.ctrip.corp.bff.hotel.book.handler.bbzmbrcommonpassenger;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.AppManagerservice.GetCorpUserInfoDetailListResponseType;
import ctrip.BBZ.members.bbzmbrCommonPassenger.BbzmbrcommonpassengerClient;
import ctrip.BBZ.members.bbzmbrCommonPassenger.GetCommonPassengerRequestType;
import ctrip.BBZ.members.bbzmbrCommonPassenger.GetCommonPassengerResponseType;
import ctrip.BBZ.members.bbzmbrCommonPassenger.Result;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024-10-28 19:24:08
 */
@Component
public class HandlerOfGetCommonPassenger extends AbstractHandlerOfSOA<GetCommonPassengerRequestType, GetCommonPassengerResponseType, BbzmbrcommonpassengerClient> {

    public static final int LIMIT_SIZE = 49;
    @Override
    protected String getMethodName() {
        return "getCommonPassenger";
    }

    @Override
    protected String getLogErrorCode(GetCommonPassengerResponseType getCommonPassengerResponseType) {
        return String.valueOf(Optional.ofNullable(getCommonPassengerResponseType)
                .map(GetCommonPassengerResponseType::getResultInfo)
                .map(Result::getResultCode)
                .orElse(0));
    }

}
