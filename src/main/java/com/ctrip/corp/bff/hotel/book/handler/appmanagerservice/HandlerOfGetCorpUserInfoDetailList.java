package com.ctrip.corp.bff.hotel.book.handler.appmanagerservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.AppManagerservice.AppManagerserviceClient;
import corp.user.service.AppManagerservice.GetCorpUserInfoDetailListRequestType;
import corp.user.service.AppManagerservice.GetCorpUserInfoDetailListResponseType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/8/13
 */
@Component
public class HandlerOfGetCorpUserInfoDetailList extends AbstractHandlerOfSOA<GetCorpUserInfoDetailListRequestType,
        GetCorpUserInfoDetailListResponseType, AppManagerserviceClient> {

    public static final int LIMIT_SIZE = 99;

    @Override
    protected String getMethodName() {
        return "getCorpUserInfoDetailList";
    }

    @Override
    protected String getLogErrorCode(GetCorpUserInfoDetailListResponseType getCorpUserInfoDetailListResponseType) {
        return String.valueOf(Optional.ofNullable(getCorpUserInfoDetailListResponseType)
                .map(GetCorpUserInfoDetailListResponseType::getRetCode)
                .orElse(0));
    }
}
