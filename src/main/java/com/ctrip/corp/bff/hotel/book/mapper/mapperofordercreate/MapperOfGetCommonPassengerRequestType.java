package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import ctrip.BBZ.members.bbzmbrCommonPassenger.GetCommonPassengerRequestType;
import ctrip.BBZ.members.bbzmbrCommonPassenger.ParameterItem;
import ctrip.BBZ.members.bbzmbrCommonPassenger.QueryCondition;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/17 11:12
 */
@Service public class MapperOfGetCommonPassengerRequestType
    extends AbstractMapper<Tuple2<IntegrationSoaRequestType, List<String>>, GetCommonPassengerRequestType> {

    /**
     * 业务类型
     */
    private static final String BIZ_TYPE = "BizType";

    /**
     * BU 三字码
     */
    private static final String CRP = "CRP";

    /**
     * 出行人id
     */
    private static final String LIST_PASSENGER_ID = "ListPassengerID";

    @Override public GetCommonPassengerRequestType convert(Tuple2<IntegrationSoaRequestType, List<String>> tuple) {
        IntegrationSoaRequestType integrationSoaRequestType = tuple.getT1();
        List<String> infoids = tuple.getT2();
        GetCommonPassengerRequestType getCommonPassengerRequestType = new GetCommonPassengerRequestType();
        getCommonPassengerRequestType.setUID(RequestHeaderUtil.getUserId(integrationSoaRequestType));
        getCommonPassengerRequestType.setParameterList(buildParameterList());
        getCommonPassengerRequestType.setQueryConditionList(buildQueryConditionList(infoids));
        return getCommonPassengerRequestType;
    }

    @Override public ParamCheckResult check(Tuple2<IntegrationSoaRequestType, List<String>> tuple) {
        return null;
    }

    protected List<ParameterItem> buildParameterList() {
        ParameterItem parameterItem = new ParameterItem();
        parameterItem.setKey(BIZ_TYPE);
        parameterItem.setValue(CRP);
        return Arrays.asList(parameterItem);
    }

    protected List<QueryCondition> buildQueryConditionList(List<String> infoids) {
        if (CollectionUtils.isEmpty(infoids)) {
            return null;
        }
        QueryCondition queryCondition = new QueryCondition();
        queryCondition.setKey(LIST_PASSENGER_ID);
        queryCondition.setValue(infoids.stream().collect(Collectors.joining(",")));
        return Arrays.asList(queryCondition);
    }
}
