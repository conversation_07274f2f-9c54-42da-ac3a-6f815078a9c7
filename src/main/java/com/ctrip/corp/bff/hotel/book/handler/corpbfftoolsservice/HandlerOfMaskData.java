package com.ctrip.corp.bff.hotel.book.handler.corpbfftoolsservice;

import com.ctrip.corp.bff.framework.template.entity.IntegrationResponse;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.bff.tools.contract.CorpBffToolsServiceClient;
import com.ctrip.corp.bff.tools.contract.MaskDataRequestType;
import com.ctrip.corp.bff.tools.contract.MaskDataResponseType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/8/15
 */
@Component
public class HandlerOfMaskData extends AbstractHandlerOfSOA<MaskDataRequestType, MaskDataResponseType, CorpBffToolsServiceClient> {

    @Override
    protected String getMethodName() {
        return "maskData";
    }

    @Override
    protected String getLogErrorCode(MaskDataResponseType response) {
        return Optional.ofNullable(response)
                .map(MaskDataResponseType::getIntegrationResponse)
                .map(IntegrationResponse::getErrorCode)
                .orElse(null);
    }
}
