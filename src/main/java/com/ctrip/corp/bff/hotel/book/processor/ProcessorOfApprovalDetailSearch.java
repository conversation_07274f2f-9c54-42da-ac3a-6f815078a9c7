package com.ctrip.corp.bff.hotel.book.processor;

import com.ctrip.corp.bff.framework.hotel.common.builder.SearchApprovalRequest;
import com.ctrip.corp.bff.framework.hotel.common.mapper.*;
import com.ctrip.corp.bff.framework.hotel.common.util.ApprovalUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.TimeUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.WaitFutureUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfSearchApproval;
import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityRegionEnum;
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum;
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput;
import com.ctrip.corp.bff.framework.template.handler.WaitFuture;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.*;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.mapper.MapperOfSSOInfoQueryRequestType;
import com.ctrip.corp.bff.hotel.book.common.util.ApprovalDetailSearchUtil;
import com.ctrip.corp.bff.hotel.book.common.util.CityInfoValidateUtil;
import com.ctrip.corp.bff.hotel.book.contract.ApprovalDetailSearchRequestType;
import com.ctrip.corp.bff.hotel.book.contract.ApprovalDetailSearchResponseType;
import com.ctrip.corp.bff.hotel.book.handler.corpaccountqueryservice.HandlerOfGeneralSearchAccountInfo;
import com.ctrip.corp.bff.hotel.book.handler.corpbffprofileservice.HandlerOfSSOInfoQuery;
import com.ctrip.corp.bff.hotel.book.handler.corpbffspecificservice.HandlerOfApprovalTextInfo;
import com.ctrip.corp.bff.hotel.book.handler.corpbffspecificservice.HandlerOfBatchApprovalDefault;
import com.ctrip.corp.bff.hotel.book.handler.corpconfigurationservice.HandlerOfGetSubAccountConfig;
import com.ctrip.corp.bff.hotel.book.handler.corphotelbookqueryservice.HandlerOfGetCityBaseInfo;
import com.ctrip.corp.bff.hotel.book.handler.corpuserinfoservice4j.HandlerOfGetCorpUserInfo;
import com.ctrip.corp.bff.hotel.book.handler.preapprovalservice.HandlerOfSearchApproval;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofapprovaldetailsearch.MapperOfApprovalDetailSearchResponseType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofapprovaldetailsearch.MapperOfBatchApprovalDefaultRequestByDetail;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfApprovalTextInfoRequestType;
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryRequestType;
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryResponseType;
import com.ctrip.corp.bff.specific.contract.*;
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigRequestType;
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.CityInfo;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.SearchApprovalRequestType;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.SearchApprovalResponseType;
import com.ctrip.corp.foundation.common.plus.time.HotelTimeZoneUtil;
import com.ctrip.corp.foundation.common.util.NumberUtil;
import com.ctrip.corp.foundation.timezone.exception.TimeZoneNotFoundException;
import com.ctrip.corp.foundation.timezone.service.CorpGenericTimeZoneService;
import com.ctrip.corp.hotel.book.query.entity.CityBaseInfoEntity;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoRequestType;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoRequestType;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoRequestType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-11-14
 **/
@Component
public class ProcessorOfApprovalDetailSearch extends AbstractProcessor<ApprovalDetailSearchRequestType, ApprovalDetailSearchResponseType> {
    @Autowired
    private MapperOfApprovalDetailSearchResponseType mapperOfApprovalDetailSearchResponseType;
    @Autowired
    private HandlerOfApprovalTextInfo handlerOfApprovalTextInfo;
    @Autowired
    private MapperOfApprovalTextInfoRequestType mapperOfApprovalTextInfoRequestType;
    @Autowired
    private MapperOfGeneralSearchAccountInfoRequest mapperOfGeneralSearchAccountInfoRequest;
    @Autowired
    private HandlerOfGeneralSearchAccountInfo handlerOfGeneralSearchAccountInfo;
    @Autowired
    private HandlerOfGetCorpUserInfo handlerOfGetCorpUserInfo;
    @Autowired
    private MapperOfGetCorpUserInfoRequest mapperOfGetCorpUserInfoRequest;
    @Autowired
    private HandlerOfGetSubAccountConfig handlerOfGetSubAccountConfig;
    @Autowired
    private MapperOfGetSubAccountConfigRequest mapperOfGetSubAccountConfigRequest;
    @Autowired
    private HandlerOfSearchApproval handlerOfSearchApproval;
    @Autowired
    private MapperOfSearchApprovalRequest mapperOfSearchApprovalRequest;
    @Autowired
    private MapperOfBatchApprovalDefaultRequestByDetail mapperOfBatchApprovalDefaultRequest;
    @Autowired
    private HandlerOfBatchApprovalDefault handlerOfBatchApprovalDefault;
    @Autowired
    private HandlerOfSSOInfoQuery handlerOfSSOInfoQuery;
    @Autowired
    private MapperOfSSOInfoQueryRequestType mapperOfSSOInfoQueryRequestType;
    // 酒店、地面交通场景的时区转换类
    @Autowired
    private CorpGenericTimeZoneService corpGenericTimeZoneService;

    @Override
    public ApprovalDetailSearchResponseType execute(ApprovalDetailSearchRequestType ApprovalDetailSearchRequestType) throws Exception {
        WaitFuture<GeneralSearchAccountInfoRequestType, GeneralSearchAccountInfoResponseType> generalSearchAccountInfoResponseTypeWaitFuture =
                handlerOfGeneralSearchAccountInfo.handleAsync(mapperOfGeneralSearchAccountInfoRequest
                        .map(Tuple1.of(ApprovalDetailSearchRequestType.getIntegrationSoaRequestType())));
        // 获取用户信息
        WaitFuture<GetCorpUserInfoRequestType, GetCorpUserInfoResponseType> getCorpUserInfoResponseTypeWaitFuture =
                handlerOfGetCorpUserInfo.handleAsync(mapperOfGetCorpUserInfoRequest.map(Tuple1.of(ApprovalDetailSearchRequestType.getIntegrationSoaRequestType())));
        // 查询子账户信息
        WaitFuture<GetSubAccountConfigRequestType, GetSubAccountConfigResponseType> getSubAccountConfigResponseTypeWaitFuture =
                handlerOfGetSubAccountConfig.handleAsync(mapperOfGetSubAccountConfigRequest.map(Tuple1.of(WaitFutureUtil.safeGetFuture(getCorpUserInfoResponseTypeWaitFuture))));
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(WaitFutureUtil.safeGetFuture(generalSearchAccountInfoResponseTypeWaitFuture))
                .policyAccountInfo(null)
                .corpUserInfo(WaitFutureUtil.safeGetFuture(getCorpUserInfoResponseTypeWaitFuture))
                .subAccountConfig(WaitFutureUtil.safeGetFuture(getSubAccountConfigResponseTypeWaitFuture))
                .build();
        // 单点登录信息
        WaitFuture<SSOInfoQueryRequestType, SSOInfoQueryResponseType> ssoInfoQueryResponseTypeWaitFuture = null;
        if (ApprovalDetailSearchRequestType.getSsoInput() != null && StringUtil.isNotEmpty(ApprovalDetailSearchRequestType.getSsoInput().getSsoKey())) {
            ssoInfoQueryResponseTypeWaitFuture = handlerOfSSOInfoQuery.handleAsync(
                    mapperOfSSOInfoQueryRequestType.map(Tuple2.of(ApprovalDetailSearchRequestType.getIntegrationSoaRequestType(),
                            ApprovalDetailSearchRequestType.getSsoInput())));
        }
        // 审批单文案
        WaitFuture<ApprovalTextInfoRequestType, ApprovalTextInfoResponseType> approvalTextInfoResponseTypeWaitFuture =
                handlerOfApprovalTextInfo.handleAsync(mapperOfApprovalTextInfoRequestType.map(
                        Tuple2.of(ApprovalDetailSearchRequestType.getIntegrationSoaRequestType(), null)));
        WaitFuture<BatchApprovalDefaultRequestType, BatchApprovalDefaultResponseType> batchApprovalDefaultResponseTypeWaitFuture =
                handlerOfBatchApprovalDefault.handleAsync(mapperOfBatchApprovalDefaultRequest.map(Tuple3.of(ApprovalDetailSearchRequestType, accountInfo,
                        WaitFutureUtil.safeGetFutureWithoutError(ssoInfoQueryResponseTypeWaitFuture))));
        // 根据默认审批单查询
        WrapperOfSearchApproval.ApprovalInfo approvalInfo = null;
        SearchApprovalRequest adapterOfSearchApprovalRequest =
                buildSearchApprovalRequest(ApprovalDetailSearchRequestType, accountInfo, batchApprovalDefaultResponseTypeWaitFuture.get());
        if (adapterOfSearchApprovalRequest != null) {
            WaitFuture<SearchApprovalRequestType, SearchApprovalResponseType> searchApprovalResponseTypeWaitFuture =
                    handlerOfSearchApproval.handleAsync(mapperOfSearchApprovalRequest.map(Tuple1.of(adapterOfSearchApprovalRequest)));
            approvalInfo = WrapperOfSearchApproval.builder().searchApprovalResponseType(searchApprovalResponseTypeWaitFuture.getWithoutError()).build();
        }

        // 获取城市偏移量
        Map<String, String> cityIdToOffsetMap = buildCityIdToOffsetMap(corpGenericTimeZoneService, approvalInfo);

        return mapperOfApprovalDetailSearchResponseType.map(Tuple7.of(
                ApprovalDetailSearchRequestType, batchApprovalDefaultResponseTypeWaitFuture.getWithoutError(),
                accountInfo, approvalInfo, approvalTextInfoResponseTypeWaitFuture.getWithoutError(),
                WaitFutureUtil.safeGetFutureWithoutError(ssoInfoQueryResponseTypeWaitFuture), cityIdToOffsetMap));
    }

    @Override
    public Map<String, String> tracking(ApprovalDetailSearchRequestType ApprovalDetailSearchRequestType, ApprovalDetailSearchResponseType ApprovalDetailSearchResponseType) {
        return null;
    }

    private Map<String, String> buildCityIdToOffsetMap(
        CorpGenericTimeZoneService corpGenericTimeZoneService, WrapperOfSearchApproval.ApprovalInfo approvalInfo) {
        if (corpGenericTimeZoneService == null || approvalInfo == null) {
            return new HashMap<>();
        }
        List<CityInfo> checkInCityInfos = approvalInfo.getCheckInCityInfos();
        if (CollectionUtil.isEmpty(checkInCityInfos)) {
            return new HashMap<>();
        }
        return checkInCityInfos.stream().filter(Objects::nonNull)
            .filter(cityInfo -> CityInfoValidateUtil.validCity(cityInfo.getCityId(), cityInfo.getCityName()))
            .collect(Collectors.toMap(
                CityInfo::getCityId,
                cityInfo -> {
                    try {
                        String zoneId = corpGenericTimeZoneService.getZoneId(
                            NumberUtil.parseInt(cityInfo.getCityId()));
                        ZonedDateTime now = ZonedDateTime.now(ZoneId.of(zoneId));
                        return String.valueOf(now.getOffset().getTotalSeconds() / 60);
                    } catch (TimeZoneNotFoundException e) {
                        LogUtil.loggingClogOnly(LogLevelEnum.Error, this.getClass(),
                            "getZoneId error", e, null);
                    }
                    return StringUtil.EMPTY;
                },
                (left, right) -> left));
    }

    /**
     * 审批单详情查询
     * 前端传入>原单
     * @param ApprovalDetailSearchRequestType
     * @param accountInfo
     * @param batchApprovalDefaultResponseType
     * @return
     */
    private SearchApprovalRequest buildSearchApprovalRequest(ApprovalDetailSearchRequestType ApprovalDetailSearchRequestType,
                                                             WrapperOfAccount.AccountInfo accountInfo,
                                                             BatchApprovalDefaultResponseType batchApprovalDefaultResponseType) {
        ApprovalInfo approvalInfo = ApprovalDetailSearchUtil.buildDefaultApprovalInfo(batchApprovalDefaultResponseType);
        if (approvalInfo == null || approvalInfo.getApprovalBaseInfo() == null) {
            return null;
        }
        ApprovalInput approvalInput = new ApprovalInput();
        String mainApprovalNo = approvalInfo.getApprovalBaseInfo().getMasterApprovalNo();
        String subApprovalNo = approvalInfo.getApprovalBaseInfo().getSubApprovalNo();
        if (StringUtil.isEmpty(subApprovalNo)) {
            return null;
        }
        approvalInput.setMasterApprovalNo(mainApprovalNo);
        approvalInput.setSubApprovalNo(subApprovalNo);
        CityRegionEnum cityRegionEnum = CommonConstant.OPEN.equalsIgnoreCase(approvalInfo.getDomesticFlag()) ? CityRegionEnum.CN : CityRegionEnum.OVERSEA;
        ApprovalInput newApprovalInput = ApprovalUtil.buildApprovalInput(approvalInput, accountInfo, cityRegionEnum, ApprovalDetailSearchRequestType.getCorpPayInfo());
        return SearchApprovalRequest.builder()
                .approvalInput(newApprovalInput)
                .cityRegionEnum(cityRegionEnum)
                .integrationSoaRequestType(ApprovalDetailSearchRequestType.getIntegrationSoaRequestType())
                .returnRegionControlCityInfo(true)
                .compatibleCitySplit(false)
                .build();
    }
}
