package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.foundation.common.util.Null;
import corp.user.service.AppManagerservice.GetCorpUserInfoDetailListRequestType;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/13
 */
@Component
public class MapperOfGetCorpUserInfoDetailListRequestType extends AbstractMapper<Tuple2<IntegrationSoaRequestType, List<String>>, GetCorpUserInfoDetailListRequestType> {
    @Override
    protected GetCorpUserInfoDetailListRequestType convert(Tuple2<IntegrationSoaRequestType, List<String>> para) {
        if (para == null) {
            return null;
        }
        IntegrationSoaRequestType integrationSoaRequestType = para.getT1();
        List<String> userIds = para.getT2();
        if (CollectionUtil.isEmpty(userIds)) {
            return null;
        }
        GetCorpUserInfoDetailListRequestType res = new GetCorpUserInfoDetailListRequestType();
        res.setUidList(userIds);
        res.setRequestID(Null.or(integrationSoaRequestType, IntegrationSoaRequestType::getRequestId));
        return res;
    }

    @Override
    protected ParamCheckResult check(Tuple2<IntegrationSoaRequestType, List<String>> integrationSoaRequestTypeListTuple2) {
        return null;
    }
}
