package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.agg.hotel.roomavailable.entity.BillingGuestInfoType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookingRulesType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.CertificateInfoType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelItem;
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomItem;
import com.ctrip.corp.agg.hotel.roomavailable.entity.SpecialOfferRoomInfoEntity;
import com.ctrip.corp.agg.hotel.roomavailable.entity.TaxDetailType;
import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityInfoUtil;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.AmountInfo;
import com.ctrip.corp.bff.hotel.book.common.enums.IdCardTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.bff.hotel.book.contract.CertificateTypeInfo;
import com.ctrip.corp.bff.hotel.book.contract.NationalityLimit;
import com.ctrip.corp.foundation.common.util.Null;
import com.ctrip.corp.foundation.common.util.NumberUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ctrip.corp.bff.hotel.book.common.constant.SharkKeyConstant.ID_CARD_TYPE_NAME;
import static com.ctrip.corp.bff.hotel.book.common.constant.SharkKeyConstant.NATIONALITY_LIMIT_CONTENT;
import static com.ctrip.corp.bff.hotel.book.common.constant.SharkKeyConstant.NATIONALITY_LIMIT_TIP;
import static com.ctrip.corp.bff.hotel.book.common.constant.SharkKeyConstant.NATIONALITY_LIMIT_TITLE;
import static com.ctrip.corp.bff.hotel.book.common.enums.IdCardTypeEnum.getIdCardTypeEnum;
import static com.ctrip.corp.foundation.common.constant.StringConstants.F;
import static com.ctrip.corp.foundation.common.constant.StringConstants.T;

/**
 * @Author: chenchuang
 * @Date: 2024/9/27 21:30
 * @Description: 出行人信息工具类
 */
public class PassengerInfoConfigUtil {

    /**
     * 国家限制类型
     */
    public static final String NATIONALITY_LIMIT_TYPE = "1";

    private static final Integer DEFAULT_INDEX = 999;

    public static final String NAME_EN = "en";
    public static final String NAME_ZH = "zh";
    public static final char COMMA = ',';
    public static final int MALAYSIA_DEFAULT_COUNTRY_CODE = 2;

    // region 国籍限制信息 马来西亚税费信息
    public static NationalityLimit getNationalityLimitInfo(WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo) {
        NationalityLimit nationalityLimitInfo = new NationalityLimit();
        if (checkAvailInfo == null) {
            return nationalityLimitInfo;
        }
        BookingRulesType bookingRulesType = checkAvailInfo.getBookingRules();
        HotelItem hotelItem = checkAvailInfo.getHotelItem();

        // 不限制国籍
        if (!isNationalityLimit(bookingRulesType.getRestrictionType())) {
            nationalityLimitInfo.setLimit(F);
            return nationalityLimitInfo;
        }
        nationalityLimitInfo.setLimit(T);
        if (!isMalaysiaCountry(hotelItem.getCountry())) {
            return nationalityLimitInfo;
        }
        // 限制国籍 且 国籍是马来西亚 时出勾选框
        nationalityLimitInfo.setNeedCheck(T);
        TaxDetailType taxDetailType = getMalaysiaTax(Optional.ofNullable(checkAvailInfo.getRoomItem()).map(RoomItem::getTaxDetails).orElse(null));
        if (taxDetailType == null) {
            // 无税费时默认勾选
            nationalityLimitInfo.setChecked(T);
            return nationalityLimitInfo;
        }
        // 含马来西亚税费 不勾选
        nationalityLimitInfo.setChecked(F);
        String currency = CurrencyUtil.convertCurrency(taxDetailType.getCustomAmountPerUnit(), taxDetailType.getCustomCurrency());
        nationalityLimitInfo.setTip(MessageFormat.format(BFFSharkUtil.getSharkValue(NATIONALITY_LIMIT_TIP),
                currency, taxDetailType.getCustomAmountPerUnit()));
        nationalityLimitInfo.setTitle(BFFSharkUtil.getSharkValue(NATIONALITY_LIMIT_TITLE));
        nationalityLimitInfo.setContent(BFFSharkUtil.getSharkValue(NATIONALITY_LIMIT_CONTENT));
        nationalityLimitInfo.setAmountInfo(buildAmountInfo(taxDetailType.getCustomCurrency(), taxDetailType.getCustomAmountPerUnit()));
        return nationalityLimitInfo;
    }

    private static AmountInfo buildAmountInfo(String currency, BigDecimal customAmountPerUnit) {
        if (StringUtil.isBlank(currency) || customAmountPerUnit == null) {
            return null;
        }
        AmountInfo amountInfo = new AmountInfo();
        amountInfo.setCurrency(currency);
        amountInfo.setAmount(customAmountPerUnit.toPlainString());
        return amountInfo;
    }

    protected static boolean isNationalityLimit(String restrictionType) {
        return NATIONALITY_LIMIT_TYPE.equalsIgnoreCase(restrictionType);
    }

    /**
     * 马来西亚酒店缴纳旅游税
     */
    protected static TaxDetailType getMalaysiaTax(List<TaxDetailType> taxDetailTypeTOS) {
        if (CollectionUtils.isEmpty(taxDetailTypeTOS)) {
            return null;
        }
        for (TaxDetailType to : taxDetailTypeTOS) {
            if (to == null) {
                continue;
            }
            if (StringUtil.isBlank(to.getRestrictionValue())) {
                continue;
            }
            if (!isNationalityLimit(String.valueOf(to.getRestrictionType()))) {
                continue;
            }
            if (Stream.of(to.getRestrictionValue().split(String.valueOf(COMMA)))
                    .anyMatch(o -> StringUtils.isNumeric(o) && isMalaysiaCountry(
                            NumberUtil.parseInt(o)))) {
                return to;
            }
        }
        return null;
    }

    /**
     * 是否马来西亚
     */
    public static boolean isMalaysiaCountry(Integer country) {
        // todo:QConfig配置，目前固定写死了马来西亚
        return Objects.equals(MALAYSIA_DEFAULT_COUNTRY_CODE, country);
    }

    // 特惠房型提示信息
    public static List<String> getSpecialOfferTips(SpecialOfferRoomInfoEntity specialOfferRoomInfoEntity) {
        if (specialOfferRoomInfoEntity == null) {
            return Collections.emptyList();
        }
        List<String> tips = new ArrayList<>();
        if (StringUtil.isNotBlank(specialOfferRoomInfoEntity.getBookingRuleHint())) {
            tips.add(specialOfferRoomInfoEntity.getBookingRuleHint());
        }
        if (StringUtil.isNotBlank(specialOfferRoomInfoEntity.getCertificateInputHint())) {
            tips.add(specialOfferRoomInfoEntity.getCertificateInputHint());
        }
        return tips;
    }

    public static List<CertificateTypeInfo> getSupportsCertificateTypeInfos(CertificateInfoType certificateInfo) {
        // todo:证件获取顺序
        List<Integer> passengerCardSequence = new ArrayList<>();
        List<CertificateTypeInfo> specialOfferCardInfos =
                Optional.ofNullable(certificateInfo).map(CertificateInfoType::getSupportCertificateType).map(o -> o.stream().map(
                        certificateTypeEnum -> {
                            CertificateTypeInfo specialOfferCardInfo = new CertificateTypeInfo();
                            IdCardTypeEnum idCardTypeEnum = getIdCardTypeEnum(certificateTypeEnum);
                            if (idCardTypeEnum != null) {
                                specialOfferCardInfo.setCertificateType(String.valueOf(idCardTypeEnum.getIdCode()));
                                specialOfferCardInfo.setCertificateName(BFFSharkUtil.getSharkValue(MessageFormat.format(ID_CARD_TYPE_NAME, idCardTypeEnum.getSharkName())));
                            }
                            return specialOfferCardInfo;
                        }
                ).collect(Collectors.toList())).orElse(null);
        // 排序
        return sortSpecialOfferCardInfos(specialOfferCardInfos, passengerCardSequence);
    }

    /** todo:获取证件顺序
     public static List<Integer> getPassengerCardSequence() {
     String cargoConfig = BffCargoUtils.getConfig(HOTEL_BOOK_PASSENGER_CARD_SEQUENCE, BffSoaContextUtil.getGatewayHost());
     return BffCollectionUtil.asList(StringUtil.split(cargoConfig, "\\|"))
     .stream().map(BffNumberUtil::parseInt).filter(BffNumberUtil::isNotZeroAndNull).collect(Collectors.toList());
     }*/

    protected static List<CertificateTypeInfo> sortSpecialOfferCardInfos(List<CertificateTypeInfo> specialOfferCardInfos,
                                                                         List<Integer> passengerCardSequence) {
        if (CollectionUtil.isEmpty(specialOfferCardInfos) || CollectionUtil.isEmpty(passengerCardSequence)) {
            return specialOfferCardInfos;
        }
        specialOfferCardInfos.sort((c1, c2) -> {
            if (c1 == null || c2 == null) {
                return 1;
            }
            Integer c1Type = Optional.ofNullable(c1.getCertificateType()).map(Integer::valueOf).orElse(null);
            Integer c2Type = Optional.ofNullable(c2.getCertificateType()).map(Integer::valueOf).orElse(null);
            Integer c1index = passengerCardSequence.contains(c1Type) ? passengerCardSequence.indexOf(c2Type)
                    : DEFAULT_INDEX;
            Integer c2index = passengerCardSequence.contains(c1Type) ? passengerCardSequence.indexOf(c2Type)
                    : DEFAULT_INDEX;
            if (c1index > c2index) {
                return 1;
            } else if (c1index < c2index) {
                return -1;
            } else {
                return 0;
            }
        });
        return specialOfferCardInfos;
    }

    public static List<String> getGuestsNameLanguageList(WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo,
                                                         BookingInitRequestType bookingInitRequest) {
        List<String> guestsNameLanguageFromAgg = Optional.ofNullable(checkAvailInfo.getBookingRules()).map(BookingRulesType::getBillingGuestInfo)
                .map(BillingGuestInfoType::getGuestsNameLanguages).orElse(null);
        if (StrategyOfBookingInitUtil.isGuestNameLanguageAggResult(Null.or(bookingInitRequest, BookingInitRequestType::getStrategyInfos))) {
            return guestsNameLanguageFromAgg;
        }
        if (needOnlyUseEnName(checkAvailInfo)) {
            return Collections.singletonList(NAME_EN);
        }
        if (CollectionUtil.isEmpty(guestsNameLanguageFromAgg)) {
            return Arrays.asList(NAME_EN, NAME_ZH);
        }
        if (guestsNameLanguageFromAgg.stream().anyMatch(l -> StringUtil.equalsIgnoreCase(l, NAME_ZH))) {
            return Arrays.asList(NAME_ZH, NAME_EN);
        }
        return Collections.singletonList(NAME_EN);
    }

    /**
     * 必须英文名下单的场景 amadeus、海外
     *
     * @return
     */
    public static boolean needOnlyUseEnName(WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo) {
        RoomItem roomItem = checkAvailInfo.getRoomItem();
        HotelItem hotelItem = checkAvailInfo.getHotelItem();
        if (hotelItem == null || roomItem == null) {
            return false;
        }
        if (isAmadeus(roomItem)) {
            return true;
        }
        if (CityInfoUtil.oversea(hotelItem.getCity())) {
            return true;
        }
        if (CityInfoUtil.hmt(hotelItem.getCity())) {
            return true;
        }
        // NOTE:蓝色空间根据POS站决定是否需要英文名下单
        return false;
    }

    private static Boolean isAmadeus(RoomItem roomItem) {
        String gds = Optional.ofNullable(roomItem).map(RoomItem::getGDS).orElse(null);
        return StringUtil.equalsIgnoreCase(gds, "Amadeus");
    }

    // 前端逻辑下沉
    public static String requireNationality(RoomItem roomItem, NationalityLimit nationalityLimit) {
        String limit = Optional.ofNullable(nationalityLimit).map(NationalityLimit::getLimit).orElse(F);
        if (T.equals(limit)) {
            return T;
        }

        if (roomItem == null ||
                CollectionUtils.isEmpty(roomItem.getApplicativeCountryCodeList())) {
            return F;
        }
        return T;
    }
}
