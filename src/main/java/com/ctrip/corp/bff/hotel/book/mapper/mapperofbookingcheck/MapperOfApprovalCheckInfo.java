package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookingcheck;

import com.ctrip.corp.bff.framework.hotel.common.qconfig.CorpCustomizeUtil;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QconfigOfInitConfig;
import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfSearchApproval;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfSearchApproval.ApprovalInfo;
import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityRegionEnum;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.bookingcheck.ApprovalVerifyFieldEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.exception.BookingCheckErrorEnum;
import com.ctrip.corp.bff.hotel.book.service.ApprovalCheckInfo;
import com.ctrip.corp.bff.hotel.book.service.BookingCheckRequestType;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.ResponseStatus;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.*;
import com.ctrip.corp.foundation.common.util.StringUtilsExt;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024-11-07
 **/
@Component
public class MapperOfApprovalCheckInfo extends AbstractMapper<Tuple3<BookingCheckRequestType,
    SearchApprovalResponseType,
    WrapperOfSearchApproval.ApprovalInfo>, ApprovalCheckInfo> {

    private final static String SEARCH_APPROVAL_ERROR_CODE = "ctrip.com.hotel.bookingCheck.approval.search.{0}";

    private static final Integer APPROVAL_STATUS_PROGRESS = 3;
    /**
     * 管控定制化信息
     */
    private static final String QCONFIG_OF_APPROVAL_CHECK_MSG = "approvalCheckMsg";
    /**
     * 审批管控浮动开关
     */
    private static final String QCONFIG_OF_CITY_AFTER_FLOAT = "approvalCityCheckAfterFloatDay";

    /**
     * 定制错误码错误话术
     */
    private static final Integer SEARCH_APPROVAL_PRODCUT_NOT_MATCH = 103080006;

    @Override
    protected ApprovalCheckInfo convert(Tuple3<BookingCheckRequestType,
        SearchApprovalResponseType,
        WrapperOfSearchApproval.ApprovalInfo> param) {
        BookingCheckRequestType requestType = param.getT1();
        SearchApprovalResponseType searchApprovalResponseType = param.getT2();
        WrapperOfSearchApproval.ApprovalInfo approvalInfo = param.getT3();

        Integer cityId = Optional.ofNullable(requestType.getCityInput()).map(CityInput::getCityId).orElse(null);
        Boolean oversea = buildOverseaFlag(cityId);
        String corpId = requestType.getIntegrationSoaRequestType().getUserInfo().getCorpId();
        if (null == searchApprovalResponseType) {
            throw BusinessExceptionBuilder.createAlertException(BookingCheckErrorEnum.SEARCH_APPROVAL_ERROR, "");
        }
        Boolean isSuccess = Optional.ofNullable(searchApprovalResponseType.getStatus()).map(ResponseStatus::isSuccess).orElse(false);
        Integer errorCode = Optional.ofNullable(searchApprovalResponseType.getStatus()).map(ResponseStatus::getErrorCode).orElse(null);
        if (BooleanUtil.isNotTrue(isSuccess) && SEARCH_APPROVAL_PRODCUT_NOT_MATCH.equals(errorCode)) {
            throw BusinessExceptionBuilder.createAlertException(BookingCheckErrorEnum.SEARCH_APPROVAL_ERROR.getErrorCode(),
                    BookingCheckErrorEnum.SEARCH_APPROVAL_ERROR.getErrorMessage(),
                    BFFSharkUtil.getSharkValue(StringUtilsExt.format(SEARCH_APPROVAL_ERROR_CODE,
                            errorCode + (BooleanUtil.isTrue(oversea)
                                    ? CityRegionEnum.OVERSEA.name().toLowerCase()
                                    : CityRegionEnum.CN.name().toLowerCase()))), String.valueOf(errorCode));
        }
        if (BooleanUtil.isNotTrue(isSuccess) && errorCode != null) {
            throw BusinessExceptionBuilder.createAlertException(BookingCheckErrorEnum.SEARCH_APPROVAL_ERROR.getErrorCode(),
                    BookingCheckErrorEnum.SEARCH_APPROVAL_ERROR.getErrorMessage(),
                    BFFSharkUtil.getSharkValue(StringUtilsExt.format(SEARCH_APPROVAL_ERROR_CODE, errorCode)), String.valueOf(errorCode));
        }
        List<HotelApprovalInfo> hotelApprovalInfoList = Optional.ofNullable(searchApprovalResponseType.getSearchApprovalResult())
                .map(SearchApprovalResult::getHotelApprovalInfoList).orElse(Lists.newArrayList());
        HotelApprovalInfo hotelApprovalInfo = hotelApprovalInfoList.stream().findFirst().orElse(null);
        if (hotelApprovalInfo == null) {
            throw BusinessExceptionBuilder.createAlertException(BookingCheckErrorEnum.SEARCH_APPROVAL_ERROR, "");
        }
        if (!approvalInfo.cityWithinPreControl(cityId)) {
            throw BusinessExceptionBuilder.createAlertException(BookingCheckErrorEnum.APPROVAL_CITY_ERROR, "");
        }
        String startDate = requestType.getHotelBookInput().getHotelDateRangeInfo().getCheckIn();
        String endDate = requestType.getHotelBookInput().getHotelDateRangeInfo().getCheckOut();
        int floatDay =
            CorpCustomizeUtil.isGwCorp(requestType.getIntegrationSoaRequestType().getUserInfo().getCorpId()) ? 1 : 0;
        if (!approvalInfo.startDateWithinPreControl(startDate, floatDay)) {
            throw BusinessExceptionBuilder.createAlertException(BookingCheckErrorEnum.APPROVAL_START_DATE_ERROR, "");
        }
        if (!approvalInfo.endDateWithinPreControl(endDate, floatDay)) {
            throw BusinessExceptionBuilder.createAlertException(BookingCheckErrorEnum.APPROVAL_END_DATE_ERROR, "");
        }
        return buildApprovalCheckInfo(hotelApprovalInfo, corpId);
    }

    @Override
    protected ParamCheckResult check(Tuple3<BookingCheckRequestType,
        SearchApprovalResponseType,
        WrapperOfSearchApproval.ApprovalInfo> param) {
        return null;
    }

    private Boolean buildOverseaFlag(Integer cityId) {
        if (cityId == null) {
            return false;
        }
        // 是否海外会员酒店城市
        if (CityInfoUtil.hkOrMacao(cityId)) {
            // 港澳台当做国内
            return false;
        }
        return CityInfoUtil.oversea(cityId);
    }

    private ApprovalCheckInfo buildApprovalCheckInfo(HotelApprovalInfo hotelApprovalInfo, String corpId) {
        if (hotelApprovalInfo == null) {
            return null;
        }
        if (!QConfigOfCustomConfig.isSupport(QCONFIG_OF_APPROVAL_CHECK_MSG, corpId)) {
            return null;
        }
        if (hotelApprovalInfo.getApprovalBaseInfo() == null || !APPROVAL_STATUS_PROGRESS.equals(hotelApprovalInfo.getApprovalBaseInfo().getApprovalStatus())) {
            return null;
        }
        ApprovalCheckInfo approvalCheckInfo = new ApprovalCheckInfo();
        approvalCheckInfo.setApprovalStatusNeedRemind(CommonConstant.OPEN);
        return approvalCheckInfo;
    }
}
