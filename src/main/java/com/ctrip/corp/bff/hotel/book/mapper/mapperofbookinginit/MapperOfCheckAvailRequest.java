package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.basebiz.iplocation.proto.GetIpInfoResponseTypeV2;
import com.ctrip.basebiz.iplocation.proto.IpInfoEntityV2;
import com.ctrip.corp.agg.hotel.roomavailable.entity.ABTestConfigType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckAvailRequestType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckBaseEntity;
import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckBookingScenarioEntity;
import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckExtEntity;
import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckOriOrderEntity;
import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckRoomEntity;
import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckUserSettingEntity;
import com.ctrip.corp.agg.hotel.roomavailable.entity.FeeTypes;
import com.ctrip.corp.agg.hotel.roomavailable.entity.GuestInfoType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.ModifyReasonEnum;
import com.ctrip.corp.agg.hotel.roomavailable.entity.OperationType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.UseCouponInfoType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.UserBelongType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.UserChangeCouponInfoType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.WebSite;
import com.ctrip.corp.agg.hotel.roomavailable.entity.XProductEntityType;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.ExperimentConfigUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.HotelDateRangeUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount.AccountInfo;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfHotelTravelPolicy;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo;
import com.ctrip.corp.bff.framework.hotel.entity.contract.*;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.*;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.common.RcToken;
import com.ctrip.corp.bff.framework.hotel.util.cityinfo.CityInfoUtil;
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil;
import com.ctrip.corp.bff.framework.template.common.qconfig.QConfigFile;
import com.ctrip.corp.bff.framework.template.common.qconfig.QConfigUtil;
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.CalendarUtil;
import com.ctrip.corp.bff.framework.template.common.utils.HostUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.common.utils.date.DateUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.*;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple7;
import com.ctrip.corp.bff.hotel.book.common.enums.BookingInitErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.RcTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.CorpHotelBookCommonWSUtil;
import com.ctrip.corp.bff.hotel.book.common.util.DateUtils;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.foundation.common.enums.LanguageLocaleEnum;
import com.ctrip.corp.foundation.common.plus.time.HotelTimeZoneUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 可定检查请求
 * @Date 2024/8/22 09:58
 * @Version 1.0
 */
@Component
public class MapperOfCheckAvailRequest extends AbstractMapper<
        Tuple7<BookingInitRequestType, HotelTravelPolicyInfo, AccountInfo, ResourceToken, GetIpInfoResponseTypeV2,
                Map<String, StrategyInfo>, List<HotelBookPassengerInput>>, CheckAvailRequestType> {
    private static final String SERVER_FORM_H5_CN = "m.ct/html5";
    private static final String SERVER_FORM_H5_EN = "m.ct/html5/en";
    private static final String SERVER_FORM_ONLINE_CN = "ct.ctrip.com";
    private static final String SERVER_FORM_ONLINE_EN = "ct.ctrip.com/en";
    private static final String SERVER_FORM_OFFLINE = "corpint.ctripcorp.com";

    private static final String FILE_NAME = "config.properties";
    private static final String OFFLINE_SERVER_FROM_QCONFIG_KEY = "offlineServerFrom";
    /* 按卡号管控 */
    private static final String TRAVEL_POLICY_CONTROL_MODE_CARD = "C";
    /* 按出行人管控 */
    private static final String TRAVEL_POLICY_CONTROL_MODE_PASSENGER = "P";

    private static final Integer NUM_30 = 30;

    private static final String GUARANTEE_FLAG_QCONFIG_KEY = "guaranteeFlag";

    private static final String BOOKING_TYPE_SELF = "self";

    public static final Integer TEST_CITY_ID = 4;
    /**
     * 15分钟
     */
    private static final int TIME_MINUTE_15 = 15;

    private static final int DEFAULT_ROOM_QUANTITY = 1;

    @Override
    protected CheckAvailRequestType convert(
        Tuple7<BookingInitRequestType, HotelTravelPolicyInfo, AccountInfo, ResourceToken, GetIpInfoResponseTypeV2,
            Map<String, StrategyInfo>, List<HotelBookPassengerInput>> tuple) {
        BookingInitRequestType bookingInitRequestType = tuple.getT1();
        WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo getHotelTravelPolicyResponseType = tuple.getT2();
        WrapperOfAccount.AccountInfo accountInfo = tuple.getT3();
        ResourceToken resourceToken = tuple.getT4();
        GetIpInfoResponseTypeV2 getIpInfoResponseTypeV2 = tuple.getT5();
        Map<String, StrategyInfo> strategyInfoMap = tuple.getT6();
        List<HotelBookPassengerInput> hotelBookPassengerInputs = tuple.getT7();
        CheckAvailRequestType result = new CheckAvailRequestType();

        Integer cityId = Optional.ofNullable(resourceToken.getHotelResourceToken())
                .map(HotelResourceToken::getHotelGeoInfoResourceToken)
                .map(HotelGeoInfoResourceToken::getCityId).orElse(null);

        result.setBaseInfo(setCheckAvailBaseInfo(bookingInitRequestType, getHotelTravelPolicyResponseType,
                cityId, accountInfo, getIpInfoResponseTypeV2, hotelBookPassengerInputs));
        if (resourceToken != null) {
            result.setRoomInfo(
                setCheckAvailRoomInfo(bookingInitRequestType, resourceToken, accountInfo, strategyInfoMap, hotelBookPassengerInputs));
            result.setCheckExtInfo(setCheckAvailExtInfo(resourceToken));
            result.setOriOrderInfo(setCheckOriOrderInfo(bookingInitRequestType, resourceToken));
        }
        result.setBookingScenario(setBookScenarioInfo(bookingInitRequestType));
        result.setABTestConfig(getABTestConfig(Optional.ofNullable(bookingInitRequestType)
            .map(BookingInitRequestType::getIntegrationSoaRequestType)
            .map(IntegrationSoaRequestType::getUserInfo)
            .orElse(null)));

        return result;
    }

    @Override
    protected ParamCheckResult check(
        Tuple7<BookingInitRequestType, HotelTravelPolicyInfo, AccountInfo, ResourceToken, GetIpInfoResponseTypeV2,
            Map<String, StrategyInfo>, List<HotelBookPassengerInput>> tuple) {
        if (tuple == null || tuple.getT1() == null
                || tuple.getT3() == null) {
            return new ParamCheckResult(false,
                    BookingInitErrorEnum.MAPPER_PARAM_CHECK_ERROR,
                    this.getClass().getName() + " error");
        }
        return null;
    }

    /**
     * 设置可订检查预定基础信息
     */
    private CheckBaseEntity setCheckAvailBaseInfo(
            BookingInitRequestType bookingInitRequestType,
            HotelTravelPolicyInfo getHotelTravelPolicyResponseType, Integer cityId,
            AccountInfo accountInfo,
            GetIpInfoResponseTypeV2 getIpInfoResponseTypeV2,
            List<HotelBookPassengerInput> hotelBookPassengerInputs) {
        IntegrationSoaRequestType integrationSoaRequestType = bookingInitRequestType.getIntegrationSoaRequestType();
        if (integrationSoaRequestType == null) {
            return null;
        }
        CheckBaseEntity result = new CheckBaseEntity();

        result.setTraceId(integrationSoaRequestType.getRequestId());
        UserInfo userInfo = integrationSoaRequestType.getUserInfo();
        if (userInfo != null) {
            result.setCorpId(userInfo.getCorpId());
            result.setUid(userInfo.getUserId());
            result.setPOS(HostUtil.mapToAccountPos(userInfo.getPos()));
        }
        String policyUid = Optional.ofNullable(bookingInitRequestType.getPolicyInput())
                .map(PolicyInput::getPolicyUid).orElse(StringUtil.EMPTY);
        result.setPolicyUid(policyUid);
        result.setEid(Optional.ofNullable(integrationSoaRequestType.getEid()).orElse(StringUtil.EMPTY));
        // 差标Token
        result.setPolicyToken(Optional.ofNullable(getHotelTravelPolicyResponseType).map(WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo::getPolicyToken)
                .orElse(null));


        boolean isOverSea = CityInfoUtil.oversea(cityId);
        if (SourceFrom.Online.equals(integrationSoaRequestType.getSourceFrom())) {
            result.setWebSite(isOverSea ? WebSite.HotelDomestic : WebSite.HotelIntl);
        } else if (SourceFrom.Offline.equals(integrationSoaRequestType.getSourceFrom())) {
            result.setWebSite(isOverSea ? WebSite.OfflineIntl : WebSite.OfflineDomestic);
        } else {
            result.setWebSite(WebSite.WebApp);
        }

        result.setLocale(integrationSoaRequestType.getLanguage());
        result.setServerFrom(getServerFrom(integrationSoaRequestType));

        CheckUserSettingEntity checkUserSettingEntity = new CheckUserSettingEntity();
        checkUserSettingEntity.setTravelPolicyControlMode(
                accountInfo.isTravelStandCard() ? TRAVEL_POLICY_CONTROL_MODE_CARD : TRAVEL_POLICY_CONTROL_MODE_PASSENGER);
        // 卡号配置的币种
        checkUserSettingEntity.setCustomCurrency(accountInfo.getCurrency());
        result.setUserSettings(checkUserSettingEntity);
        // 预定子渠道
        result.setSubChannel(CorpHotelBookCommonWSUtil.buildSubChannel(integrationSoaRequestType));

        // 站点来源
        Long cityIdFromIp = Optional.ofNullable(getIpInfoResponseTypeV2).map(GetIpInfoResponseTypeV2::getResult).map(
            IpInfoEntityV2::getCityId).orElse(null);
        if (cityIdFromIp != null && integrationSoaRequestType.getSourceFrom() != SourceFrom.Offline) {
            UserBelongType userBelongType = new UserBelongType();
            userBelongType.setUsersCityId(cityIdFromIp.intValue());
            result.setUserBelongInfo(userBelongType);
        }
        result.setGuestInfoList(getGuestInfoList(
                accountInfo, hotelBookPassengerInputs));
        result.setPlatform(StrategyOfBookingInitUtil.getPlatForm(bookingInitRequestType.getStrategyInfos()));
        result.setSelectedCountryCode(getSelectedNationalityCode(hotelBookPassengerInputs,
                bookingInitRequestType.getStrategyInfos()));
        return result;
    }

    /**
     * 第二次写这个方法, 第三次就抽取成工具类
     *
     * @param integrationSoaRequestType
     * @return
     */
    private String getServerFrom(IntegrationSoaRequestType integrationSoaRequestType) {
        if (integrationSoaRequestType == null || integrationSoaRequestType.getSourceFrom() == null) {
            return SERVER_FORM_H5_CN;
        }

        SourceFrom sourceFrom = integrationSoaRequestType.getSourceFrom();
        String language = integrationSoaRequestType.getLanguage();

        switch (sourceFrom) {
            case H5:
            case Native:
            case CRN:
                return getValByLanguage(SERVER_FORM_H5_CN, SERVER_FORM_H5_EN, SERVER_FORM_H5_CN, language);
            case Online:
                return getValByLanguage(SERVER_FORM_ONLINE_CN, SERVER_FORM_ONLINE_EN, SERVER_FORM_ONLINE_CN, language);
            case Offline:
                QConfigFile configFile = QConfigUtil.getFile(FILE_NAME);
                return configFile.getConfigValue(OFFLINE_SERVER_FROM_QCONFIG_KEY, SERVER_FORM_OFFLINE);
            default:
                return SERVER_FORM_H5_CN;
        }
    }

    private String getValByLanguage(String zhCN, String mu, String defaultStr, String language) {
        LanguageLocaleEnum localeEnum = Optional.ofNullable(LanguageLocaleEnum.getByLanguageLocaleIgnoreCase(language))
                .orElse(LanguageLocaleEnum.ZH_CN);
        String resultStr = localeEnum == LanguageLocaleEnum.ZH_CN ? zhCN : mu;
        return StringUtils.isBlank(resultStr) ? defaultStr : resultStr;
    }

    /**
     * 可定检查需要传入人、房间去管控 优先使用前端传入的
     *
     * @return
     */
    private List<GuestInfoType> getGuestInfoList(WrapperOfAccount.AccountInfo accountInfo, final List<HotelBookPassengerInput> hotelBookPassengerInputs) {

        if (CollectionUtil.isEmpty(hotelBookPassengerInputs)) {
            return null;
        }
        List<GuestInfoType> guestInfoTypeList = Lists.newArrayList();
        for (HotelBookPassengerInput hotelBookPassengerInput : hotelBookPassengerInputs) {
            HotelPassengerInput passengerInput = Optional.ofNullable(hotelBookPassengerInput)
                    .map(HotelBookPassengerInput::getHotelPassengerInput).orElse(null);
            if (null == passengerInput) {
                continue;
            }
            GuestInfoType guestInfoType = new GuestInfoType();
            boolean isEmployee = BooleanUtil.parseStr(Boolean.TRUE).equals(passengerInput.getEmployee());
            guestInfoType.setUid(isEmployee ? passengerInput.getUid() : null);
            guestInfoType.setEmployee(isEmployee);
            guestInfoTypeList.add(guestInfoType);
        }
        return guestInfoTypeList;
    }

    /**
     * 马来西亚国籍code
     */
    public static final String NATIONAL_CODE_MAS = "MY";

    /**
     * 用户国籍 多个用逗号隔开
     */
    private String getSelectedNationalityCode(List<HotelBookPassengerInput> hotelBookPassengerInputs, List<StrategyInfo> strategyInfos) {
        if (StrategyOfBookingInitUtil.checkedMas(strategyInfos)) {
            return NATIONAL_CODE_MAS;
        }
        if (StrategyOfBookingInitUtil.useFirstNation(strategyInfos)) {
            return StrategyOfBookingInitUtil.getFirstNation(strategyInfos);
        }
        if (CollectionUtil.isEmpty(hotelBookPassengerInputs)) {
            return null;
        }
        // 使用证件国籍
        if (StrategyOfBookingInitUtil.useCertificateNationality(strategyInfos)) {
            return hotelBookPassengerInputs.stream()
                    .filter(Objects::nonNull)
                    .map(this::getCertificateNationality)
                    .filter(StringUtil::isNotBlank).collect(Collectors.joining(","));
        }
        return hotelBookPassengerInputs.stream()
                .filter(Objects::nonNull)
                .map(HotelBookPassengerInput::getNationalityInfo)
                .filter(Objects::nonNull)
                .map(NationalityInfo::getNationalityCode)
                .filter(StringUtil::isNotBlank).collect(Collectors.joining(","));
    }

    private static final String CERTIFICATE_TYPE_OTHER_DOCUMENT = "OTHERDOCUMENT";

    private String getCertificateNationality(HotelBookPassengerInput hotelBookPassengerInput) {
        // 取无证件节点的国籍
        CertificateInfo certificateInfo = Optional.ofNullable(hotelBookPassengerInput)
                .map(HotelBookPassengerInput::getCertificateInfos).orElse(Collections.emptyList())
                .stream().filter(x -> StringUtil.equalsIgnoreCase(CERTIFICATE_TYPE_OTHER_DOCUMENT, x.getCertificateType()))
                .findFirst().orElse(null);
        String certificateNationality = Optional.ofNullable(certificateInfo).map(CertificateInfo::getNationalityInfo)
                .map(NationalityInfo::getNationalityCode).orElse(null);
        if (StringUtil.isNotBlank(certificateNationality)) {
            return certificateNationality;
        }

        return Optional.ofNullable(hotelBookPassengerInput).map(HotelBookPassengerInput::getNationalityInfo)
                .map(NationalityInfo::getNationalityCode).orElse(null);
    }

    /**
     * 设置可订检查房型信息
     */
    private CheckRoomEntity setCheckAvailRoomInfo(BookingInitRequestType bookingInitRequestType,
                                                  ResourceToken resourceToken, AccountInfo accountInfo,
                                                  Map<String, StrategyInfo> strategyInfoMap,
                                                  List<HotelBookPassengerInput> hotelBookPassengerInputs) {
        if (resourceToken == null) {
            return null;
        }
        CheckRoomEntity checkRoomEntity = new CheckRoomEntity();
        checkRoomEntity.setFeeType(CorpPayInfoUtil.isPrivate(bookingInitRequestType.getCorpPayInfo()) ? FeeTypes.P : FeeTypes.C);

        Integer hotelId = Optional.ofNullable(resourceToken.getHotelResourceToken())
                .map(HotelResourceToken::getHotelId).orElse(null);
        if (hotelId != null) {
            checkRoomEntity.setHotelId(hotelId);
        }


        Integer cityId = Optional.ofNullable(resourceToken.getHotelResourceToken())
                .map(HotelResourceToken::getHotelGeoInfoResourceToken)
                .map(HotelGeoInfoResourceToken::getCityId).orElse(null);
        boolean isOverSea = CityInfoUtil.oversea(cityId);
        // 房型id 确认下是基础房型还是子房型
        checkRoomEntity.setCityId(cityId);
        checkRoomEntity.setOversea(isOverSea);

        HotelBookInput hotelBookInput = bookingInitRequestType.getHotelBookInput();
        if (hotelBookInput != null) {
            checkRoomEntity.setGuestPerson(buildGuestPerson(bookingInitRequestType, accountInfo, strategyInfoMap, hotelBookPassengerInputs));
            checkRoomEntity.setQuantity(hotelBookInput.getRoomQuantity());
        }

        // 汇率固化原房型币种
        RoomResourceToken roomResourceToken = resourceToken.getRoomResourceToken();
        if (roomResourceToken != null) {
            checkRoomEntity.setRoomOriginCurrency(roomResourceToken.getRoomOriginCurrency());
            checkRoomEntity.setRoomUniqueKey(roomResourceToken.getRoomUniqueKey());
        }

        checkRoomEntity.setUseCouponInfo(getMultiCoupon(bookingInitRequestType));

        HotelDateRangeInfo hotelDateRangeInfo = Optional.ofNullable(bookingInitRequestType.getHotelBookInput())
                .map(HotelBookInput::getHotelDateRangeInfo).orElse(null);
        if (hotelDateRangeInfo != null) {
            long diffDays = HotelDateRangeUtil.getDays(hotelDateRangeInfo);
            checkRoomEntity.setLongRental(diffDays > NUM_30);
            checkRoomEntity.setLocalCheckInDate(hotelDateRangeInfo.getCheckIn());
            checkRoomEntity.setLocalCheckOutDate(hotelDateRangeInfo.getCheckOut());
        }

        String arriveTimeTokenStr = Optional.ofNullable(bookingInitRequestType.getArriveTimeInput())
                .map(ArriveTimeInput::getArriveTimeToken).orElse(null);
        String arriveTime = getArriveTime(isOverSea, arriveTimeTokenStr, cityId);
        if (arriveTime != null) {
            checkRoomEntity.setLatestArrivalTimeBJT(arriveTime);
        }
        // 有钟点房的时候，将钟点房的其实时间作为最晚到店时间传入
        String hourRoomTokenStr = Optional.ofNullable(bookingInitRequestType.getHourRoomInput()).map(HourRoomInput::getHourRoomToken).orElse(null);
        HourRoomToken hourRoomToken = TokenParseUtil.parseToken(hourRoomTokenStr, HourRoomToken.class);
        if (hourRoomToken != null && hourRoomToken.getStartTime() != null) {
            checkRoomEntity.setLatestArrivalTimeBJT(CalendarUtil.format(hourRoomToken.getStartTime(), DateUtil.YYYY_MM_DD_HH_mm_ss));
        }
        return checkRoomEntity;
    }

    /**
     * 叠加券
     *
     * @return
     */
    private UseCouponInfoType getMultiCoupon(BookingInitRequestType bookingInitRequestType) {
        if (StrategyOfBookingInitUtil.hotelCheckAvail(bookingInitRequestType.getStrategyInfos())) {
            UseCouponInfoType useCouponInfoType = new UseCouponInfoType();
            useCouponInfoType.setAllCouponCanceled(false);
            return useCouponInfoType;
        }
        if (bookingInitRequestType.getCouponInfoInput() == null) {
            return null;
        }
        UseCouponInfoType result = new UseCouponInfoType();
        result.setAllCouponCanceled(!BooleanUtil.parseStr(Boolean.TRUE).equals(bookingInitRequestType.getCouponInfoInput().getUseCoupon()));

        List<CouponDetailInput> couponDetailInputList = bookingInitRequestType.getCouponInfoInput().getCouponDetailInputList();
        if (CollectionUtil.isEmpty(couponDetailInputList)) {
            return result;
        }
        result.setUserChangeCouponInfos(couponDetailInputList.stream().filter(Objects::nonNull).map(couponDetailInput -> {
            UserChangeCouponInfoType userChangeCoupon = new UserChangeCouponInfoType();
            CouponToken couponToken = TokenParseUtil.parseToken(
                    couponDetailInput.getCouponToken(), CouponToken.class);
            if (couponToken == null) {
                return null;
            }
            if (!StringUtil.isEmpty(couponToken.getVirtualCode())) {
                userChangeCoupon.setCouponCode(couponToken.getVirtualCode());
            } else {
                userChangeCoupon.setCouponCode(couponToken.getCouponCode());
            }
            userChangeCoupon.setCouponPlatformId(Optional.ofNullable(couponToken.getPlatformCouponId()).map(String::valueOf).orElse(null));
            return userChangeCoupon;
        }).filter(Objects::nonNull).collect(Collectors.toList()));
        return result;
    }


    /**
     * @param isOversea          是否海外酒店
     * @param arriveTimeTokenStr
     * @param cityId
     * @return
     */
    private String getArriveTime(boolean isOversea, String arriveTimeTokenStr, Integer cityId) {
        // 海外不传
        if (isOversea) {
            return null;
        }
        // 传入的到店时间为空，取当前时间15分钟后
        int offsetMinute = 0;
        try {
            offsetMinute = HotelTimeZoneUtil.getInstance().getOffsetMinute(cityId);
        } catch (Exception e) {
            // 目前到店时间只有国内酒店有，如果获取不到时区，就默认为东八区
            offsetMinute = 480;
        }
        String after15Min = getAfter15Min(offsetMinute).format(DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD_HH_mm_ss));
        if (StringUtil.isEmpty(arriveTimeTokenStr)) {
            return after15Min;
        }
        ArriveTimeToken arriveTimeToken = null;
        if (StringUtil.isNotBlank(arriveTimeTokenStr)) {
            arriveTimeToken = TokenParseUtil.parseToken(arriveTimeTokenStr, ArriveTimeToken.class);
        }
        String arriveTimeUTC = Optional.ofNullable(arriveTimeToken).map(ArriveTimeToken::getArriveTimeUTC).orElse(null);
        return StringUtil.isNotEmpty(arriveTimeUTC) ? DateUtils.toLocalDateTime(DateUtil.parseDate(arriveTimeUTC, DateUtils.UTC_FORMAT))
                .plusMinutes(offsetMinute).format(DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD_HH_mm_ss)) : after15Min;
    }

    private LocalDateTime getAfter15Min(Integer offsetMinute) {
        // 获取当地时间的15分钟后
        LocalDateTime nowAfter15Min = LocalDateTime.now(ZoneOffset.UTC).plusMinutes(offsetMinute).plusMinutes(TIME_MINUTE_15);
        int remainingMinutes = nowAfter15Min.getMinute() % 15;
        nowAfter15Min = nowAfter15Min.plusMinutes(15L - remainingMinutes);
        nowAfter15Min = nowAfter15Min.withSecond(0).withNano(0);
        return nowAfter15Min;
    }

    /**
     * 设置可订检查拓展参数
     */
    private CheckExtEntity setCheckAvailExtInfo(ResourceToken resourceToken) {
        HotelResourceToken hotelResourceToken = resourceToken.getHotelResourceToken();
        RoomResourceToken roomResourceToken = resourceToken.getRoomResourceToken();
        if (roomResourceToken == null || hotelResourceToken == null) {
            return null;
        }

        CheckExtEntity checkExtEntity = new CheckExtEntity();
        checkExtEntity.setRatePlanTraceLogId(roomResourceToken.getRatePlanTraceLogId());
        checkExtEntity.setSearchHotelTraceId(hotelResourceToken.getSearchHotelTraceId());
        checkExtEntity.setPid(roomResourceToken.getpId());


        XProductEntityType xProductEntityType = new XProductEntityType();
        if (roomResourceToken.getProductToken() != null) {
            xProductEntityType.setXProductType(1);
            xProductEntityType.setXProductToken(roomResourceToken.getProductToken());
            checkExtEntity.setProductTokenList(Collections.singletonList(xProductEntityType));
        }
        return checkExtEntity;
    }

    /**
     * 设置可订检查场景信息
     */
    protected CheckBookingScenarioEntity setBookScenarioInfo(BookingInitRequestType bookingInitRequestType) {
        CheckBookingScenarioEntity checkBookingScenarioEntity = new CheckBookingScenarioEntity();

        checkBookingScenarioEntity.setModifyOrCorpOrder(StrategyOfBookingInitUtil.modify(bookingInitRequestType.getStrategyInfos()) ||
            StrategyOfBookingInitUtil.copyOrder(bookingInitRequestType.getStrategyInfos()));
        checkBookingScenarioEntity.setBookingWithPersonalAccount(StrategyOfBookingInitUtil.bookingWithPersonalAccount(bookingInitRequestType.getStrategyInfos()));

        return checkBookingScenarioEntity;
    }

    /**
     * 构建原单信息 仅修改、制需要
     *
     * @return
     */
    private CheckOriOrderEntity setCheckOriOrderInfo(BookingInitRequestType bookingInitRequestType, ResourceToken resourceToken) {
        Long orderId = Optional.ofNullable(resourceToken.getOrderResourceToken())
                .map(OrderResourceToken::getOrderId).orElse(null);
        if (orderId == null) {
            return null;
        }
        if (!StrategyOfBookingInitUtil.modify(bookingInitRequestType.getStrategyInfos())
            && !StrategyOfBookingInitUtil.applyModify(bookingInitRequestType.getStrategyInfos())
            && !StrategyOfBookingInitUtil.copyOrder(bookingInitRequestType.getStrategyInfos())) {
            return null;
        }
        CheckOriOrderEntity checkOriOrderEntity = new CheckOriOrderEntity();
        checkOriOrderEntity.setModifyReason(getModifyReasonEnum(bookingInitRequestType));
        checkOriOrderEntity.setOperationType(buildOperationType(bookingInitRequestType));
        checkOriOrderEntity.setOriOrderId(orderId);
        return checkOriOrderEntity;
    }


    private OperationType buildOperationType(BookingInitRequestType bookingInitRequestType) {
        if (StrategyOfBookingInitUtil.modify(bookingInitRequestType.getStrategyInfos())) {
            return OperationType.CHANGEORDER;
        }
        if (StrategyOfBookingInitUtil.copyOrder(bookingInitRequestType.getStrategyInfos())) {
            return OperationType.COPYORDER;
        }
        if (StrategyOfBookingInitUtil.onlyApplyModify(bookingInitRequestType.getStrategyInfos())) {
            return OperationType.APPLYCHANGE;
        }
        if (StrategyOfBookingInitUtil.extend(bookingInitRequestType.getStrategyInfos())) {
            return OperationType.CONTINUELIVING;
        }
        return null;
    }

    private ModifyReasonEnum getModifyReasonEnum(BookingInitRequestType bookingInitRequestType) {
        SourceFrom sourceFrom = Optional.ofNullable(bookingInitRequestType.getIntegrationSoaRequestType())
                .map(IntegrationSoaRequestType::getSourceFrom).orElse(null);
        if (sourceFrom != SourceFrom.Offline) {
            return ModifyReasonEnum.ONLINECHANGE;

        }
        if (StrategyOfBookingInitUtil.modify(bookingInitRequestType.getStrategyInfos())) {
            return getModifyReasonEnumOffline(bookingInitRequestType.getRcInfos());
        }
        return ModifyReasonEnum.ONLINECHANGE;
    }

    private ModifyReasonEnum getModifyReasonEnumOffline(List<RCInput> rcInfos) {
        if (CollectionUtil.isNotEmpty(rcInfos)) {
            for (RCInput rcInput : rcInfos) {
                String rcTokenStr = Optional.ofNullable(rcInput)
                        .map(RCInput::getRcToken).orElse(null);
                if (StringUtil.isEmpty(rcTokenStr)) {
                    continue;
                }
                RcToken rctoken = TokenParseUtil.parseToken(rcTokenStr, RcToken.class);
                if (rctoken != null && RcTypeEnum.OFFLINE_MODIFY.getCode().equals(rctoken.getType())) {
                    return getModifyReasonEnumByRcCode(rctoken.getCode());
                }
            }
        }
        return ModifyReasonEnum.CCANCEL;
    }

    private ModifyReasonEnum getModifyReasonEnumByRcCode(String code) {
        if (code == null) {
            return ModifyReasonEnum.CCANCEL;
        }
        switch (code) {
            case "NOROOM":
                return ModifyReasonEnum.NOROOM;
            case "HOUZHUIDB":
                return ModifyReasonEnum.HOUZHUIDB;
            case "ALTERPRICE":
                return ModifyReasonEnum.ALTERPRICE;
            case "NOTFULFIL":
                return ModifyReasonEnum.NOTFULFIL;
            case "ONLINEMAN":
                return ModifyReasonEnum.ONLINEMAN;
            case "XIANGAIYU":
                return ModifyReasonEnum.XIANGAIYU;
            case "ONLINECHANGE":
                return ModifyReasonEnum.ONLINECHANGE;
            default:
                return ModifyReasonEnum.CCANCEL;
        }
    }

    private List<ABTestConfigType> getABTestConfig(UserInfo userInfo) {
        if (userInfo == null) {
            return null;
        }
        return Optional.ofNullable(ExperimentConfigUtil.getExperimentItemList(userInfo)).orElse(new ArrayList<>())
            .stream()
            .filter(Objects::nonNull)
            .filter(experimentItemConfig -> !StringUtil.isEmpty(experimentItemConfig.getExperimentName()) &&
                !StringUtil.isEmpty(experimentItemConfig.getVersion()))
            .map(experimentItemConfig -> {
                ABTestConfigType abTestConfigType = new ABTestConfigType();
                abTestConfigType.setExperimentName(experimentItemConfig.getExperimentName());
                abTestConfigType.setVersion(experimentItemConfig.getVersion());
                return abTestConfigType;
            }).collect(Collectors.toList());
    }


    /**
     * 构建入住人数
     *
     * @param bookingInitRequestType
     * @return
     */
    protected Integer buildGuestPerson(BookingInitRequestType bookingInitRequestType,
        WrapperOfAccount.AccountInfo accountInfo, Map<String, StrategyInfo> strategyInfoMap,
                                       List<HotelBookPassengerInput> hotelBookPassengerInputs) {
        if (StrategyOfBookingInitUtil.useHotelBookInputNew(strategyInfoMap)) {
            return buildAdultQuantityNew(bookingInitRequestType, bookingInitRequestType.getHotelBookInput(),
                accountInfo, hotelBookPassengerInputs);
        }
        if (bookingInitRequestType.getHotelBookInputOfQuantity() == null) {
            return bookingInitRequestType.getHotelBookInput().getAdultQuantity();
        }
        CheckAvailCompare checkAvailCompareOld =
            buildCheckAvailCompare(bookingInitRequestType.getHotelBookInput().getAdultQuantity(),
                bookingInitRequestType.getHotelBookInput().getRoomQuantity(), bookingInitRequestType, accountInfo, hotelBookPassengerInputs);
        CheckAvailCompare checkAvailCompareNew =
            buildCheckAvailCompare(buildAdultQuantityNew(bookingInitRequestType, accountInfo, hotelBookPassengerInputs),
                bookingInitRequestType.getHotelBookInputOfQuantity().getRoomQuantity(), bookingInitRequestType,
                accountInfo, hotelBookPassengerInputs);
        LogUtil.logCompare("buildAdultQuantity", JsonUtil.toJson(checkAvailCompareOld),
            JsonUtil.toJson(checkAvailCompareNew));
        return bookingInitRequestType.getHotelBookInput().getAdultQuantity();
    }

    protected CheckAvailCompare buildCheckAvailCompare(Integer adultQuantity, Integer roomQuantity,
        BookingInitRequestType bookingInitRequestType, WrapperOfAccount.AccountInfo accountInfo,
                                                       List<HotelBookPassengerInput> hotelBookPassengerInputs) {
        CheckAvailCompare checkAvailCompare = new CheckAvailCompare();
        checkAvailCompare.setAdultQuantity(adultQuantity);
        checkAvailCompare.setRoomQuantity(roomQuantity);
        checkAvailCompare.setPolicyModel(accountInfo.isPolicyModel());
        checkAvailCompare.setPackageEnabled(accountInfo.isPackageEnabled());
        checkAvailCompare.setTravelStandPolicy(accountInfo.isTravelStandPolicy());
        checkAvailCompare.setHotelBookPassengerInputsSize(
            CollectionUtil.isEmpty(hotelBookPassengerInputs) ? 0 :
                    hotelBookPassengerInputs.size());
        checkAvailCompare.setOaApprovalHead(accountInfo.isOaApprovalHead());
        return checkAvailCompare;
    }

    /**
     * 构建入住人数
     *
     * @param bookingInitRequestType
     * @return
     */
    public Integer buildAdultQuantityNew(BookingInitRequestType bookingInitRequestType,
        WrapperOfAccount.AccountInfo accountInfo, List<HotelBookPassengerInput> hotelBookPassengerInputs) {
        if (bookingInitRequestType.getHotelBookInputOfQuantity() == null) {
            return null;
        }
        return buildAdultQuantityNew(bookingInitRequestType, bookingInitRequestType.getHotelBookInputOfQuantity(),
            accountInfo, hotelBookPassengerInputs);
    }

    /**
     * 构建入住人数
     *
     * @param
     * @return
     */
    public Integer buildAdultQuantityNew(BookingInitRequestType bookingInitRequestType, HotelBookInput hotelBookInput,
        WrapperOfAccount.AccountInfo accountInfo, List<HotelBookPassengerInput> hotelBookPassengerInputs) {
        if (hotelBookInput == null) {
            return null;
        }
        int roomQuantity = Optional.ofNullable(hotelBookInput.getRoomQuantity()).orElse(DEFAULT_ROOM_QUANTITY);
        int adultQuantity = Optional.ofNullable(hotelBookInput.getAdultQuantity()).orElse(DEFAULT_ROOM_QUANTITY);
        if (accountInfo.isTravelStandPolicy() && CorpPayInfoUtil.isPublic(bookingInitRequestType.getCorpPayInfo())
            && CollectionUtils.isNotEmpty(hotelBookPassengerInputs)) {
            return Math.max(hotelBookPassengerInputs.size(), roomQuantity);
        }
        return Math.max(adultQuantity, roomQuantity);
    }

    class CheckAvailCompare {
        Integer adultQuantity;
        Integer roomQuantity;
        boolean policyModel;
        boolean packageEnabled;
        boolean travelStandPolicy;
        Integer hotelBookPassengerInputsSize;
        boolean oaApprovalHead;

        public boolean isOaApprovalHead() {
            return oaApprovalHead;
        }

        public void setOaApprovalHead(boolean oaApprovalHead) {
            this.oaApprovalHead = oaApprovalHead;
        }

        public Integer getAdultQuantity() {
            return adultQuantity;
        }

        public void setAdultQuantity(Integer adultQuantity) {
            this.adultQuantity = adultQuantity;
        }

        public Integer getRoomQuantity() {
            return roomQuantity;
        }

        public void setRoomQuantity(Integer roomQuantity) {
            this.roomQuantity = roomQuantity;
        }

        public boolean isPolicyModel() {
            return policyModel;
        }

        public void setPolicyModel(boolean policyModel) {
            this.policyModel = policyModel;
        }

        public boolean isPackageEnabled() {
            return packageEnabled;
        }

        public void setPackageEnabled(boolean packageEnabled) {
            this.packageEnabled = packageEnabled;
        }

        public boolean isTravelStandPolicy() {
            return travelStandPolicy;
        }

        public void setTravelStandPolicy(boolean travelStandPolicy) {
            this.travelStandPolicy = travelStandPolicy;
        }

        public Integer getHotelBookPassengerInputsSize() {
            return hotelBookPassengerInputsSize;
        }

        public void setHotelBookPassengerInputsSize(Integer hotelBookPassengerInputsSize) {
            this.hotelBookPassengerInputsSize = hotelBookPassengerInputsSize;
        }
    }
}
